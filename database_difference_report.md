# 数据库表差异分析报告

**生成时间**: 2025-08-04 21:44:22

## 📊 分析摘要

| 项目 | 数量 |
|------|------|
| 现有数据表 | 31 |
| 规划数据表 | 23 |
| 代码使用表 | 51 |
| 规划中缺失 | 14 |
| 文档中缺失 | 22 |
| 字段差异表 | 9 |
| **关键问题** | **23** |

## ⚠️ 规划中有但数据库中缺失的表

以下表在文档中有明确规划，但数据库中不存在：

### `p_music_library`
- **描述**: 音乐库表（AI生成音乐存储、MiniMax平台）
- **分类**: AI生成相关表
- **来源文档**: index-new.mdc
- **代码实现**: ❌ 无代码使用

### `p_sound_library`
- **描述**: 音效库表（AI生成音效存储、火山引擎豆包平台）
- **分类**: AI生成相关表
- **来源文档**: index-new.mdc
- **代码实现**: ❌ 无代码使用

### `p_timbre_library`
- **描述**: 音色库表（AI生成音色存储、双平台支持）
- **分类**: AI生成相关表
- **来源文档**: index-new.mdc
- **代码实现**: ❌ 无代码使用

### `p_story_library`
- **描述**: 故事库表（AI生成故事内容、项目表 p_projects 关联）
- **分类**: AI生成相关表
- **来源文档**: index-new.mdc
- **代码实现**: ❌ 无代码使用

### `referral_codes`
- **描述**: 邀请码表
- **分类**: 邀请佣金系统
- **来源文档**: dev-api-guidelines-add.mdc
- **代码实现**: ❌ 无代码使用

### `referral_commissions`
- **描述**: 邀请佣金表
- **分类**: 邀请佣金系统
- **来源文档**: dev-api-guidelines-add.mdc
- **代码实现**: ❌ 无代码使用

### `download_tracking`
- **描述**: 终端下载推广系统
- **分类**: 下载推广系统
- **来源文档**: dev-api-guidelines-add.mdc
- **代码实现**: ❌ 无代码使用

### `cache_statistics`
- **描述**: 缓存管理和性能优化系统
- **分类**: 缓存管理
- **来源文档**: dev-api-guidelines-add.mdc
- **代码实现**: ❌ 无代码使用

### `workflow_templates`
- **描述**: 工作流模板表
- **分类**: 工作流管理系统
- **来源文档**: dev-api-guidelines-add.mdc
- **代码实现**: ❌ 无代码使用

### `workflow_executions`
- **描述**: 工作流执行表
- **分类**: 工作流管理系统
- **来源文档**: dev-api-guidelines-add.mdc
- **代码实现**: ❌ 无代码使用

## 📝 现有数据表但文档中缺失规划

以下表已存在于数据库中，但文档中没有相应规划：

### `p_achievements`
- **字段数量**: 21
- **代码使用**: ✅ 被代码使用
  - **模型**: Achievement

### `p_resources`
- **字段数量**: 32
- **代码使用**: ✅ 被代码使用

### `p_character_categories`
- **字段数量**: 13
- **代码使用**: ✅ 被代码使用
  - **模型**: CharacterCategory
  - **控制器**: CharacterController
  - **服务**: CharacterService

### `p_daily_tasks`
- **字段数量**: 21
- **代码使用**: ✅ 被代码使用
  - **模型**: DailyTask

### `p_growth_histories`
- **字段数量**: 19
- **代码使用**: ✅ 被代码使用
  - **模型**: GrowthHistory

### `p_platform_performance_metrics`
- **字段数量**: 14
- **代码使用**: ✅ 被代码使用
  - **模型**: PlatformPerformanceMetric
  - **服务**: AiLoadBalancingService, AiPlatformHealthService, AiPlatformSelectionService

### `p_projects`
- **字段数量**: 18
- **代码使用**: ✅ 被代码使用
  - **模型**: Project
  - **控制器**: ProjectController
  - **服务**: ProjectManagementService, ProjectService, ResourceManagementService, SearchService, TemplateService

### `p_publications`
- **字段数量**: 25
- **代码使用**: ✅ 被代码使用
  - **模型**: Publication
  - **服务**: PublicationService, RecommendationService, ReviewService, SocialService

### `p_system_monitors`
- **字段数量**: 13
- **代码使用**: ✅ 被代码使用
  - **模型**: SystemMonitor

### `p_user_achievements`
- **字段数量**: 12
- **代码使用**: ✅ 被代码使用
  - **模型**: UserAchievement

### `p_user_character_bindings`
- **字段数量**: 16
- **代码使用**: ✅ 被代码使用
  - **模型**: UserCharacterBinding
  - **服务**: CharacterService

### `p_user_daily_tasks`
- **字段数量**: 14
- **代码使用**: ✅ 被代码使用
  - **模型**: UserDailyTask

### `p_user_files`
- **字段数量**: 21
- **代码使用**: ✅ 被代码使用
  - **模型**: UserFile
  - **服务**: FileService, SearchService

### `p_user_levels`
- **字段数量**: 17
- **代码使用**: ✅ 被代码使用
  - **模型**: UserLevel

### `p_user_model_preferences`
- **字段数量**: 14
- **代码使用**: ✅ 被代码使用
  - **模型**: UserModelPreference
  - **服务**: AiPlatformFallbackService, AiPlatformSelectionService

### `p_user_preferences`
- **字段数量**: 14
- **代码使用**: ✅ 被代码使用
  - **模型**: UserPreference
  - **控制器**: UserController
  - **服务**: AiModelService, RecommendationService, UserService

### `p_user_works`
- **字段数量**: 14
- **代码使用**: ✅ 被代码使用
  - **模型**: UserWork
  - **服务**: WorkPublishService

### `p_work_interactions`
- **字段数量**: 8
- **代码使用**: ✅ 被代码使用
  - **模型**: WorkInteraction
  - **控制器**: WorkPublishController
  - **服务**: WorkPublishService

### `p_work_plaza`
- **字段数量**: 33
- **代码使用**: ✅ 被代码使用
  - **模型**: WorkPlaza
  - **控制器**: WorkPublishController
  - **服务**: WorkPublishService

### `p_work_shares`
- **字段数量**: 11
- **代码使用**: ✅ 被代码使用
  - **模型**: WorkShare
  - **服务**: WorkPublishService

## 🔄 字段差异分析

### `users`
- **现有表名**: `p_users`
- **规划表名**: `p_users`
- **名称匹配**: ✅
- **代码使用**: ✅
- **现有表中缺失字段**: available_points
- **规划中缺失字段**: nickname, level, experience, avatar, bio, follower_count, following_count, inviter_id, remark, status, points, is_vip, vip_expires_at, last_login_ip, last_login_at
- **共同字段**: id, username, password, email, frozen_points, created_at, updated_at

### `points_transactions`
- **现有表名**: `p_points_transactions`
- **规划表名**: `p_points_transactions`
- **名称匹配**: ✅
- **代码使用**: ✅
- **现有表中缺失字段**: type, description
- **规划中缺失字段**: status, ai_platform, request_data, response_data, timeout_seconds, completed_at, failure_reason, updated_at
- **共同字段**: id, user_id, business_type, business_id, amount, created_at

### `points_freeze`
- **现有表名**: `p_points_freeze`
- **规划表名**: `p_points_freeze`
- **名称匹配**: ✅
- **代码使用**: ✅
- **现有表中缺失字段**: consumed_at, returned_at, consume_reason, return_reason
- **规划中缺失字段**: transaction_id, expires_at, released_at, reason
- **共同字段**: id, user_id, amount, status, business_type, business_id, created_at, updated_at

### `style_library`
- **现有表名**: `p_style_library`
- **规划表名**: `p_style_library`
- **名称匹配**: ✅
- **代码使用**: ✅
- **现有表中缺失字段**: config
- **规划中缺失字段**: category, style_config, prompt_template, thumbnail, is_active, is_premium, sort_order, usage_count, rating, tags, created_by
- **共同字段**: id, name, description, created_at, updated_at

### `character_library`
- **现有表名**: `p_character_library`
- **规划表名**: `p_character_library`
- **名称匹配**: ✅
- **代码使用**: ✅
- **现有表中缺失字段**: characteristics
- **规划中缺失字段**: category_id, gender, age_range, personality, background, appearance, avatar, images, voice_config, style_preferences, tags, is_active, is_premium, is_featured, sort_order, binding_count, rating, rating_count, created_by
- **共同字段**: id, name, description, created_at, updated_at

### `resource_versions`
- **现有表名**: `p_resource_versions`
- **规划表名**: `p_resource_versions`
- **名称匹配**: ✅
- **代码使用**: ✅
- **现有表中缺失字段**: version_name, description, generation_config
- **规划中缺失字段**: version_uuid, user_id, version_number, version_type, resource_url, original_filename, file_size, mime_type, generation_cost, cost_transaction_id, prompt_text, negative_prompt, ai_platform, ai_model, generation_params, ai_platform_metadata, generated_at, url_expires_at, resource_status, review_status, review_notes, is_downloaded_locally, local_save_path, downloaded_at, reviewer_id, reviewed_at, status
- **共同字段**: id, resource_id, created_at, updated_at

### `ai_generation_tasks`
- **现有表名**: `p_ai_generation_tasks`
- **规划表名**: `p_ai_generation_tasks`
- **名称匹配**: ✅
- **代码使用**: ✅
- **现有表中缺失字段**: progress, result_data
- **规划中缺失字段**: project_id, model_config_id, platform, model_name, output_data, generation_params, external_task_id, cost, tokens_used, processing_time_ms, started_at, completed_at, metadata, retry_count, max_retries
- **共同字段**: id, user_id, task_type, status, input_data, error_message, created_at, updated_at

### `websocket_sessions`
- **现有表名**: `p_websocket_sessions`
- **规划表名**: `p_websocket_sessions`
- **名称匹配**: ✅
- **代码使用**: ✅
- **规划中缺失字段**: client_version, connection_ip, user_agent, connection_info, subscribed_events, connected_at, last_ping_at, disconnected_at, message_count, disconnect_reason
- **共同字段**: id, session_id, user_id, client_type, status, created_at, updated_at

### `ai_model_configs`
- **现有表名**: `p_ai_model_configs`
- **规划表名**: `ai_model_configs`
- **名称匹配**: ❌
- **代码使用**: ✅
- **现有表中缺失字段**: provider
- **规划中缺失字段**: platform, api_endpoint, capabilities, is_default, max_tokens, timeout_seconds, rate_limits, performance_metrics, last_health_check, health_status, health_message
- **共同字段**: id, model_name, model_type, config_params, is_active, priority, cost_per_request, created_at, updated_at

## 🎯 整合调整方案

### 1. 创建规划中缺失的数据表 🔴 高优先级

**操作**: 需要创建 14 个数据表

**影响**: 这些表在文档中有明确规划，但数据库中不存在，可能影响相关功能的实现

**涉及表**: p_music_library, p_sound_library, p_timbre_library, p_story_library, p_resources, referral_codes, referral_commissions, download_tracking, cache_statistics, workflow_templates, workflow_executions

### 2. 更新文档以包含现有数据表 🟡 中优先级

**操作**: 需要在文档中补充 22 个数据表的规划

**影响**: 这些表已经存在并被代码使用，但文档中没有规划，需要补充文档

**涉及表**: p_achievements, p_resources, p_character_categories, p_daily_tasks, p_data_exports, p_growth_histories, p_migrations, p_platform_performance_metrics, p_projects, p_publications, p_system_monitors, p_user_achievements, p_user_character_bindings, p_user_daily_tasks, p_user_files, p_user_levels, p_user_model_preferences, p_user_preferences, p_user_works, p_work_interactions, p_work_plaza, p_work_shares

### 3. 同步数据表字段 🔴 高优先级

**操作**: 需要同步 9 个数据表的字段

**影响**: 字段不匹配可能导致代码运行错误或功能缺失

**涉及表**: users, points_transactions, points_freeze, style_library, character_library, resource_versions, ai_generation_tasks, websocket_sessions, ai_model_configs

## 📋 总结

本次分析发现了 **23** 个关键问题需要处理：

1. **数据表创建**: 需要创建 14 个规划中的数据表
2. **文档更新**: 需要在文档中补充 22 个现有数据表的规划
3. **字段同步**: 需要同步 9 个数据表的字段差异

建议按照推荐方案的优先级顺序进行整合调整，确保数据库结构与文档规划保持一致。
