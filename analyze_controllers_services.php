<?php
/**
 * 分析控制器和服务层代码中使用的数据表
 * 结合现有的数据库表结构和文档规划进行对比分析
 */

// 扫描目录
$controllerDir = 'php/api/app/Http/Controllers/PyApi';
$serviceDir = 'php/api/app/Services/PyApi';
$modelDir = 'php/api/app/Models';

$analysis = [
    'scan_time' => date('Y-m-d H:i:s'),
    'controllers' => [],
    'services' => [],
    'models' => [],
    'table_usage' => [],
    'model_table_mapping' => []
];

/**
 * 扫描控制器文件
 */
function scanControllers($dir) {
    $controllers = [];
    $files = glob($dir . '/*.php');
    
    foreach ($files as $file) {
        $filename = basename($file);
        $content = file_get_contents($file);
        
        // 提取类名
        preg_match('/class\s+(\w+Controller)/', $content, $classMatch);
        $className = $classMatch[1] ?? $filename;
        
        // 提取使用的服务
        preg_match_all('/use\s+App\\\\Services\\\\PyApi\\\\(\w+Service);/', $content, $serviceMatches);
        $usedServices = $serviceMatches[1] ?? [];
        
        // 提取使用的模型
        preg_match_all('/use\s+App\\\\Models\\\\(\w+);/', $content, $modelMatches);
        $usedModels = $modelMatches[1] ?? [];
        
        // 提取直接的数据库操作
        preg_match_all('/DB::table\([\'"]([^\'"]+)[\'"]\)/', $content, $dbMatches);
        $directDbTables = $dbMatches[1] ?? [];
        
        // 提取方法名
        preg_match_all('/public\s+function\s+(\w+)\s*\(/', $content, $methodMatches);
        $methods = $methodMatches[1] ?? [];
        
        $controllers[$className] = [
            'file' => $filename,
            'used_services' => array_unique($usedServices),
            'used_models' => array_unique($usedModels),
            'direct_db_tables' => array_unique($directDbTables),
            'methods' => $methods,
            'method_count' => count($methods)
        ];
    }
    
    return $controllers;
}

/**
 * 扫描服务层文件
 */
function scanServices($dir) {
    $services = [];
    $files = glob($dir . '/*.php');
    
    foreach ($files as $file) {
        $filename = basename($file);
        $content = file_get_contents($file);
        
        // 提取类名
        preg_match('/class\s+(\w+Service)/', $content, $classMatch);
        $className = $classMatch[1] ?? $filename;
        
        // 提取使用的模型
        preg_match_all('/use\s+App\\\\Models\\\\(\w+);/', $content, $modelMatches);
        $usedModels = $modelMatches[1] ?? [];
        
        // 提取直接的数据库操作
        preg_match_all('/DB::table\([\'"]([^\'"]+)[\'"]\)/', $content, $dbMatches);
        $directDbTables = $dbMatches[1] ?? [];
        
        // 提取方法名
        preg_match_all('/public\s+function\s+(\w+)\s*\(/', $content, $methodMatches);
        $methods = $methodMatches[1] ?? [];
        
        $services[$className] = [
            'file' => $filename,
            'used_models' => array_unique($usedModels),
            'direct_db_tables' => array_unique($directDbTables),
            'methods' => $methods,
            'method_count' => count($methods)
        ];
    }
    
    return $services;
}

/**
 * 扫描模型文件
 */
function scanModels($dir) {
    $models = [];
    $files = glob($dir . '/*.php');
    
    foreach ($files as $file) {
        $filename = basename($file, '.php');
        $content = file_get_contents($file);
        
        // 提取表名
        $tableName = null;
        if (preg_match('/protected\s+\$table\s*=\s*[\'"]([^\'"]+)[\'"]/', $content, $tableMatch)) {
            $tableName = $tableMatch[1];
        } else {
            // 如果没有明确指定表名，使用Laravel的命名约定
            $tableName = strtolower(preg_replace('/(?<!^)[A-Z]/', '_$0', $filename)) . 's';
        }
        
        // 提取fillable字段
        preg_match('/protected\s+\$fillable\s*=\s*\[(.*?)\];/s', $content, $fillableMatch);
        $fillableFields = [];
        if (isset($fillableMatch[1])) {
            preg_match_all('/[\'"]([^\'"]+)[\'"]/', $fillableMatch[1], $fieldMatches);
            $fillableFields = $fieldMatches[1] ?? [];
        }
        
        // 提取关联关系
        preg_match_all('/public\s+function\s+(\w+)\s*\(\s*\)\s*\{[^}]*return\s+\$this->(hasOne|hasMany|belongsTo|belongsToMany)\(([^)]+)\)/', $content, $relationMatches);
        $relations = [];
        if (!empty($relationMatches[1])) {
            for ($i = 0; $i < count($relationMatches[1]); $i++) {
                $relations[] = [
                    'method' => $relationMatches[1][$i],
                    'type' => $relationMatches[2][$i],
                    'target' => $relationMatches[3][$i]
                ];
            }
        }
        
        $models[$filename] = [
            'table_name' => $tableName,
            'fillable_fields' => $fillableFields,
            'relations' => $relations,
            'relation_count' => count($relations)
        ];
    }
    
    return $models;
}

// 执行扫描
echo "开始扫描控制器...\n";
$analysis['controllers'] = scanControllers($controllerDir);
echo "扫描到 " . count($analysis['controllers']) . " 个控制器\n";

echo "开始扫描服务层...\n";
$analysis['services'] = scanServices($serviceDir);
echo "扫描到 " . count($analysis['services']) . " 个服务\n";

echo "开始扫描模型...\n";
$analysis['models'] = scanModels($modelDir);
echo "扫描到 " . count($analysis['models']) . " 个模型\n";

// 统计表使用情况
$tableUsage = [];

// 从模型中收集表名
foreach ($analysis['models'] as $modelName => $modelInfo) {
    $tableName = $modelInfo['table_name'];
    if (!isset($tableUsage[$tableName])) {
        $tableUsage[$tableName] = [
            'used_by_models' => [],
            'used_by_controllers' => [],
            'used_by_services' => [],
            'direct_db_usage' => []
        ];
    }
    $tableUsage[$tableName]['used_by_models'][] = $modelName;
    $analysis['model_table_mapping'][$modelName] = $tableName;
}

// 从控制器中收集表使用情况
foreach ($analysis['controllers'] as $controllerName => $controllerInfo) {
    foreach ($controllerInfo['used_models'] as $model) {
        if (isset($analysis['model_table_mapping'][$model])) {
            $tableName = $analysis['model_table_mapping'][$model];
            $tableUsage[$tableName]['used_by_controllers'][] = $controllerName;
        }
    }
    foreach ($controllerInfo['direct_db_tables'] as $table) {
        if (!isset($tableUsage[$table])) {
            $tableUsage[$table] = [
                'used_by_models' => [],
                'used_by_controllers' => [],
                'used_by_services' => [],
                'direct_db_usage' => []
            ];
        }
        $tableUsage[$table]['direct_db_usage'][] = $controllerName;
    }
}

// 从服务层中收集表使用情况
foreach ($analysis['services'] as $serviceName => $serviceInfo) {
    foreach ($serviceInfo['used_models'] as $model) {
        if (isset($analysis['model_table_mapping'][$model])) {
            $tableName = $analysis['model_table_mapping'][$model];
            $tableUsage[$tableName]['used_by_services'][] = $serviceName;
        }
    }
    foreach ($serviceInfo['direct_db_tables'] as $table) {
        if (!isset($tableUsage[$table])) {
            $tableUsage[$table] = [
                'used_by_models' => [],
                'used_by_controllers' => [],
                'used_by_services' => [],
                'direct_db_usage' => []
            ];
        }
        $tableUsage[$table]['direct_db_usage'][] = $serviceName;
    }
}

$analysis['table_usage'] = $tableUsage;

// 保存分析结果
file_put_contents('controllers_services_analysis.json', json_encode($analysis, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

echo "\n分析完成！\n";
echo "控制器: " . count($analysis['controllers']) . " 个\n";
echo "服务: " . count($analysis['services']) . " 个\n";
echo "模型: " . count($analysis['models']) . " 个\n";
echo "涉及数据表: " . count($analysis['table_usage']) . " 个\n";
echo "结果已保存到 controllers_services_analysis.json\n";
