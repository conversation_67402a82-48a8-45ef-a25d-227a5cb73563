{"scan_time": "2025-08-04 21:38:11", "controllers": {"AdController": {"file": "AdController.php", "used_services": ["AuthService", "AdService", "WebSocketTokenService"], "used_models": [], "direct_db_tables": [], "methods": ["__construct", "ad_store", "ad_update", "store", "update"], "method_count": 5}, "AiGenerationController": {"file": "AiGenerationController.php", "used_services": ["AuthService", "AiGenerationService"], "used_models": [], "direct_db_tables": [], "methods": ["__construct", "generateText", "getTaskStatus", "getUserTasks", "retryTask"], "method_count": 5}, "AiModelController": {"file": "AiModelController.php", "used_services": ["AuthService", "ModelManagementService", "AiPlatformSelectionService", "AiPlatformHealthService"], "used_models": [], "direct_db_tables": [], "methods": ["__construct", "available", "detail", "test", "usageStats", "favorite", "favorites", "list", "switch", "selectOptimalPlatform", "getPlatformOptions", "getUserRecommendations", "checkPlatformHealth", "getAllPlatformsHealth", "getPlatformStats", "platformComparison", "businessPlatforms"], "method_count": 17}, "AiTaskController": {"file": "AiTaskController.php", "used_services": ["AuthService", "AiTaskService"], "used_models": [], "direct_db_tables": [], "methods": ["__construct", "index", "show", "stats", "cancel", "retry", "batchStatus", "recovery", "timeoutConfig"], "method_count": 9}, "AnalyticsController": {"file": "AnalyticsController.php", "used_services": ["AuthService", "AnalyticsService"], "used_models": [], "direct_db_tables": [], "methods": ["__construct", "getUserBehavior", "getSystemUsage", "getAiPerformance", "getUserRetention", "getRevenue", "generateCustomReport"], "method_count": 7}, "AssetController": {"file": "AssetController.php", "used_services": ["AssetService", "AuthService"], "used_models": [], "direct_db_tables": [], "methods": ["__construct", "list", "upload", "show", "delete"], "method_count": 5}, "AudioController": {"file": "AudioController.php", "used_services": ["AuthService", "AudioService"], "used_models": [], "direct_db_tables": [], "methods": ["__construct", "mix", "getMixStatus", "enhance", "getEnhanceStatus", "getPlatformOptions", "getUserRecommendations"], "method_count": 7}, "AuthController": {"file": "AuthController.php", "used_services": ["AuthService"], "used_models": [], "direct_db_tables": [], "methods": ["register", "login", "logout", "forgotPassword", "resetPassword", "verify"], "method_count": 6}, "BatchController": {"file": "BatchController.php", "used_services": ["AuthService", "BatchService"], "used_models": [], "direct_db_tables": [], "methods": ["__construct", "generateImages", "synthesizeVoices", "generateMusic", "getBatchStatus", "cancelBatch", "generateResources", "getResourcesStatus"], "method_count": 8}, "CacheController": {"file": "CacheController.php", "used_services": ["AuthService", "CacheService"], "used_models": [], "direct_db_tables": [], "methods": ["__construct", "getStats", "clearCache", "warmupCache", "get<PERSON><PERSON><PERSON>", "getValue", "setValue", "deleteKeys", "getConfig"], "method_count": 9}, "CharacterController": {"file": "CharacterController.php", "used_services": ["AuthService", "CharacterService"], "used_models": ["CharacterLibrary", "CharacterCategory"], "direct_db_tables": [], "methods": ["__construct", "getCategories", "getLibrary", "getCharacterDetail", "generate", "getRecommendations", "bindCharacter", "getMyBindings", "updateBinding", "unbindCharacter"], "method_count": 10}, "ConfigController": {"file": "ConfigController.php", "used_services": ["AuthService", "ConfigService"], "used_models": [], "direct_db_tables": [], "methods": ["__construct", "index", "getPublicConfig", "update", "batchUpdate", "reset", "history", "validateConfig"], "method_count": 8}, "CreditsController": {"file": "CreditsController.php", "used_services": ["AuthService", "PointsService"], "used_models": [], "direct_db_tables": [], "methods": ["__construct", "checkCredits", "freezeCredits", "refundCredits"], "method_count": 4}, "DeviceController": {"file": "DeviceController.php", "used_services": ["AuthService", "DeviceTokenService"], "used_models": [], "direct_db_tables": [], "methods": ["getDevices", "revokeDevice", "revokeAllDevices", "getTokenStatus", "deviceLogin"], "method_count": 5}, "DownloadController": {"file": "DownloadController.php", "used_services": ["AuthService", "DownloadManagementService"], "used_models": [], "direct_db_tables": [], "methods": ["__construct", "list", "retry", "statistics", "createLink", "secureDownload", "batchDownload", "cleanup"], "method_count": 8}, "FileController": {"file": "FileController.php", "used_services": ["AuthService", "FileService"], "used_models": [], "direct_db_tables": [], "methods": ["__construct", "upload", "getFiles", "getFileDetail", "deleteFile", "downloadFile"], "method_count": 6}, "ImageController": {"file": "ImageController.php", "used_services": ["AuthService", "ImageService"], "used_models": [], "direct_db_tables": [], "methods": ["__construct", "generate", "getStatus", "getResult", "batchGenerate", "getPlatformOptions", "getUserRecommendations"], "method_count": 7}, "LogController": {"file": "LogController.php", "used_services": ["AuthService", "LogService"], "used_models": [], "direct_db_tables": [], "methods": ["__construct", "systemLogs", "userActionLogs", "aiCallLogs", "errorLogs", "resolveError", "exportLogs"], "method_count": 7}, "MusicController": {"file": "MusicController.php", "used_services": ["AuthService", "MusicService"], "used_models": [], "direct_db_tables": [], "methods": ["__construct", "generate", "getStatus", "getResult", "batchGenerate", "getPlatformOptions", "getUserRecommendations"], "method_count": 7}, "NotificationController": {"file": "NotificationController.php", "used_services": ["AuthService", "NotificationService"], "used_models": [], "direct_db_tables": [], "methods": ["__construct", "index", "mark<PERSON><PERSON><PERSON>", "markAllAsRead", "destroy", "stats", "send"], "method_count": 7}, "PermissionController": {"file": "PermissionController.php", "used_services": ["AuthService", "PermissionService"], "used_models": [], "direct_db_tables": [], "methods": ["__construct", "getUserPermissions", "checkPermission", "getRoles", "assignRole", "grantPermissions", "revokePermissions", "getPermissionHistory"], "method_count": 8}, "PointsController": {"file": "PointsController.php", "used_services": ["PointsService", "AuthService"], "used_models": ["User", "PointsTransaction"], "direct_db_tables": [], "methods": ["__construct", "balance", "recharge", "transactions"], "method_count": 4}, "ProjectController": {"file": "ProjectController.php", "used_services": ["AuthService", "ProjectService"], "used_models": ["Project", "StyleLibrary"], "direct_db_tables": [], "methods": ["__construct", "createWithStory", "confirmTitle", "myProjects", "detail", "list", "create", "show", "update", "delete"], "method_count": 10}, "ProjectManagementController": {"file": "ProjectManagementController.php", "used_services": ["AuthService", "ProjectManagementService"], "used_models": [], "direct_db_tables": [], "methods": ["__construct", "createTask", "collaborate", "getProgress", "assignResources", "getStatistics", "getMilestones"], "method_count": 7}, "PublicationController": {"file": "PublicationController.php", "used_services": ["AuthService", "PublicationService"], "used_models": [], "direct_db_tables": [], "methods": ["__construct", "publish", "getStatus", "update", "delete", "unpublish", "myPublications", "plaza", "detail"], "method_count": 9}, "RecommendationController": {"file": "RecommendationController.php", "used_services": ["AuthService", "RecommendationService"], "used_models": [], "direct_db_tables": [], "methods": ["__construct", "content", "users", "topics", "feedback", "preferences", "updatePreferences", "analytics", "personalized"], "method_count": 9}, "ResourceController": {"file": "ResourceController.php", "used_services": ["AuthService", "ResourceManagementService"], "used_models": [], "direct_db_tables": [], "methods": ["__construct", "generate", "getStatus", "list", "delete", "getDownloadInfo", "confirmDownload", "myResources", "updateStatus"], "method_count": 9}, "ReviewController": {"file": "ReviewController.php", "used_services": ["AuthService", "ReviewService"], "used_models": [], "direct_db_tables": [], "methods": ["__construct", "submit", "getStatus", "appeal", "myReviews", "queueStatus", "guidelines", "<PERSON><PERSON><PERSON><PERSON>"], "method_count": 8}, "SocialController": {"file": "SocialController.php", "used_services": ["AuthService", "SocialService"], "used_models": [], "direct_db_tables": [], "methods": ["__construct", "follow", "follows", "like", "comment", "comments", "share", "feed", "notifications", "markNotificationsRead"], "method_count": 10}, "SoundController": {"file": "SoundController.php", "used_services": ["AuthService", "SoundService"], "used_models": [], "direct_db_tables": [], "methods": ["__construct", "generate", "getStatus", "getResult", "batchGenerate", "getPlatformOptions", "getUserRecommendations"], "method_count": 7}, "StoryController": {"file": "StoryController.php", "used_services": ["AuthService", "StoryService"], "used_models": [], "direct_db_tables": [], "methods": ["__construct", "generate", "getStatus", "getPlatformOptions", "getUserRecommendations"], "method_count": 5}, "StyleController": {"file": "StyleController.php", "used_services": ["StyleService"], "used_models": ["StyleLibrary"], "direct_db_tables": [], "methods": ["__construct", "list", "detail", "popular", "create"], "method_count": 5}, "TaskManagementController": {"file": "TaskManagementController.php", "used_services": ["AuthService", "TaskManagementService"], "used_models": [], "direct_db_tables": [], "methods": ["__construct", "cancelTask", "retryTask", "getBatchStatus", "getTimeoutConfig", "getRecoveryStatus"], "method_count": 6}, "TemplateController": {"file": "TemplateController.php", "used_services": ["AuthService", "TemplateService"], "used_models": [], "direct_db_tables": [], "methods": ["__construct", "create", "use", "marketplace", "myTemplates", "detail", "update", "delete"], "method_count": 8}, "UserController": {"file": "UserController.php", "used_services": ["UserService", "AuthService"], "used_models": ["UserPreference"], "direct_db_tables": [], "methods": ["__construct", "profile", "updateProfile", "updatePreferences", "getPreferences"], "method_count": 5}, "UserGrowthController": {"file": "UserGrowthController.php", "used_services": ["AuthService", "UserGrowthService"], "used_models": [], "direct_db_tables": [], "methods": ["__construct", "profile", "leaderboard", "completeAchievement", "dailyTasks", "completeDailyTask", "history", "statistics", "setGoals", "recommendations", "milestones"], "method_count": 11}, "VersionController": {"file": "VersionController.php", "used_services": ["AuthService", "VersionControlService"], "used_models": [], "direct_db_tables": [], "methods": ["__construct", "create", "list", "show", "setCurrent", "delete", "compare"], "method_count": 7}, "VideoController": {"file": "VideoController.php", "used_services": ["AuthService", "VideoService"], "used_models": [], "direct_db_tables": [], "methods": ["__construct", "generate", "getStatus", "getResult", "getPlatformOptions", "getUserRecommendations"], "method_count": 6}, "VoiceController": {"file": "VoiceController.php", "used_services": ["AuthService", "VoiceService"], "used_models": [], "direct_db_tables": [], "methods": ["__construct", "synthesize", "getStatus", "batchSynthesize", "clone", "getCloneStatus", "custom", "getCustomStatus", "getPlatformOptions", "getUserRecommendations"], "method_count": 10}, "WebSocketController": {"file": "WebSocketController.php", "used_services": ["AuthService", "WebSocketService"], "used_models": [], "direct_db_tables": [], "methods": ["__construct", "authenticate", "getSessions", "disconnect", "getStatus"], "method_count": 5}, "WorkPublishController": {"file": "WorkPublishController.php", "used_services": ["AuthService", "WorkPublishService", "WorkPublishPermissionService"], "used_models": ["WorkPlaza", "WorkInteraction"], "direct_db_tables": ["resources", "work_plaza"], "methods": ["__construct", "publishWork", "update", "delete", "myWorks", "gallery", "getShareLink", "like", "trending"], "method_count": 9}, "WorkflowController": {"file": "WorkflowController.php", "used_services": ["AuthService", "WorkflowService"], "used_models": [], "direct_db_tables": [], "methods": ["__construct", "create", "index", "show", "execute", "getExecutionStatus", "provideStepInput", "cancelExecution", "getExecutionHistory"], "method_count": 9}}, "services": {"AdService": {"file": "AdService.php", "used_models": [], "direct_db_tables": ["user_ad_logs"], "methods": ["store", "update"], "method_count": 2}, "AiGenerationService": {"file": "AiGenerationService.php", "used_models": ["AiModelConfig", "AiGenerationTask", "User"], "direct_db_tables": [], "methods": ["__construct", "generateText", "getTaskStatus", "getUserTasks", "retryTask"], "method_count": 5}, "AiLoadBalancingService": {"file": "AiLoadBalancingService.php", "used_models": ["AiModelConfig", "PlatformPerformanceMetric"], "direct_db_tables": [], "methods": ["__construct", "distributeTasks"], "method_count": 2}, "AiModelService": {"file": "AiModelService.php", "used_models": ["AiModelConfig", "AiGenerationTask", "UserPreference"], "direct_db_tables": [], "methods": ["switchUserModel", "getPerformanceMetrics", "checkServiceHealth", "getUserDefaultModel", "getModelByPlatform"], "method_count": 5}, "AiPlatformFallbackService": {"file": "AiPlatformFallbackService.php", "used_models": ["AiModelConfig", "UserModelPreference"], "direct_db_tables": [], "methods": ["__construct", "<PERSON><PERSON><PERSON>back", "<PERSON><PERSON><PERSON><PERSON>"], "method_count": 3}, "AiPlatformHealthService": {"file": "AiPlatformHealthService.php", "used_models": ["AiModelConfig", "PlatformPerformanceMetric"], "direct_db_tables": [], "methods": ["checkPlatformHealth", "checkAllPlatformsHealth", "getPlatformAvailability", "recordPerformanceMetric", "getHealthSummary", "checkAlertConditions", "getPlatformComparison", "getBusinessPlatforms"], "method_count": 8}, "AiPlatformSelectionService": {"file": "AiPlatformSelectionService.php", "used_models": ["AiModelConfig", "UserModelPreference", "PlatformPerformanceMetric"], "direct_db_tables": [], "methods": ["selectOptimalPlatform", "validateBusinessType", "getSupportedPlatforms", "isPlatformSupported"], "method_count": 4}, "AiTaskService": {"file": "AiTaskService.php", "used_models": ["AiGenerationTask"], "direct_db_tables": [], "methods": ["__construct", "createTask", "getTaskStatus", "cancelTask", "getTaskList", "retryTask", "getBatchTaskStatus", "getTaskRecoveryStatus", "getTimeoutConfig", "getUserTasks", "getTaskDetail", "getTaskStats", "getQueueStats"], "method_count": 13}, "AnalyticsService": {"file": "AnalyticsService.php", "used_models": [], "direct_db_tables": [], "methods": ["getUserBehavior", "getSystemUsage", "getAiPerformance", "getRevenue", "getUserRetention", "generateCustomReport"], "method_count": 6}, "AssetService": {"file": "AssetService.php", "used_models": [], "direct_db_tables": ["user_assets"], "methods": ["getAssetList", "uploadAsset", "getAssetDetail", "deleteAsset"], "method_count": 4}, "AudioService": {"file": "AudioService.php", "used_models": ["AiGenerationTask"], "direct_db_tables": [], "methods": ["__construct", "mixAudio", "getAudioMixStatus", "enhanceAudio", "getAudioEnhanceStatus"], "method_count": 5}, "AuthService": {"file": "AuthService.php", "used_models": [], "direct_db_tables": {"0": "users", "6": "p_users"}, "methods": ["register", "login", "loginByEmail", "getAuthUserID", "logout", "forgotPassword", "resetPassword", "refreshToken"], "method_count": 8}, "BatchService": {"file": "BatchService.php", "used_models": [], "direct_db_tables": [], "methods": ["createBatch", "createBatchTask", "getBatchStatus", "startBatch", "cancelBatch", "getBatchResults", "getBatchList", "retryFailedItems", "getBatchStats", "generateResources", "getResourcesStatus"], "method_count": 11}, "CacheService": {"file": "CacheService.php", "used_models": [], "direct_db_tables": [], "methods": ["set", "get", "delete", "setMultiple", "getMultiple", "flush", "healthCheck", "setWithTags", "flushTags", "clearCache", "warmupCache", "get<PERSON><PERSON><PERSON>", "getValue", "setValue", "deleteKeys", "getConfig", "getStats"], "method_count": 17}, "CharacterService": {"file": "CharacterService.php", "used_models": ["CharacterCategory", "CharacterLibrary", "UserCharacterBinding", "AiModelConfig", "AiGenerationTask"], "direct_db_tables": [], "methods": ["__construct", "getCategories", "bindCharacter", "unbindCharacter", "updateBinding", "getUserBindings", "getCharacters", "getCharacterDetail", "getRecommendations", "generateCharacter"], "method_count": 10}, "ConfigService": {"file": "ConfigService.php", "used_models": [], "direct_db_tables": [], "methods": ["get", "set", "getAll", "getAppConfig", "getDatabaseConfig", "getCacheConfig", "getQueueConfig", "getLogConfig", "validateConfig", "getEnvVars", "clearCache", "getConfigs", "getPublicConfig", "updateConfig", "batchUpdateConfigs", "resetConfig", "getConfigHistory", "validateConfigValue"], "method_count": 18}, "DeviceTokenService": {"file": "DeviceTokenService.php", "used_models": [], "direct_db_tables": [], "methods": [], "method_count": 0}, "DownloadManagementService": {"file": "DownloadManagementService.php", "used_models": ["Resource", "ResourceExport", "ResourceVersion", "ResourceDownload"], "direct_db_tables": [], "methods": ["getDownloadHistory", "retryDownload", "getDownloadStatistics", "createDownloadLink", "secureDownload", "createBatchDownload", "cleanupDownloads"], "method_count": 7}, "FileService": {"file": "FileService.php", "used_models": ["UserFile"], "direct_db_tables": [], "methods": ["uploadFile", "getUserFiles", "getFileDetail", "deleteFile", "getDownloadUrl"], "method_count": 5}, "ImageService": {"file": "ImageService.php", "used_models": ["AiModelConfig", "AiGenerationTask", "CharacterLibrary"], "direct_db_tables": [], "methods": ["__construct", "generateImage", "getImageStatus", "getImageResult", "batchGenerateImages"], "method_count": 5}, "LogService": {"file": "LogService.php", "used_models": [], "direct_db_tables": [], "methods": ["writeLog", "getLogs", "readLog", "analyzeLog", "cleanLogs", "downloadLog", "getLogConfig", "getSystemLogs", "getUserActionLogs", "getAiCallLogs", "getErrorLogs", "resolveError", "exportLogs"], "method_count": 13}, "ModelManagementService": {"file": "ModelManagementService.php", "used_models": ["AiModel", "ModelUsage", "ModelFavorite", "ModelRating", "User"], "direct_db_tables": ["ai_model_configs"], "methods": ["getAvailableModels", "getModelDetail", "testModel", "getUserUsageStats", "manageFavorite", "getUserFavorites", "switchUserModel"], "method_count": 7}, "ModelService": {"file": "ModelService.php", "used_models": [], "direct_db_tables": {"0": "ai_models", "3": "model_invocations", "8": "user_model_permissions", "9": "user_model_quotas"}, "methods": ["getAvailableModels", "getModelDetails", "invokeModel", "getModelUsageStats", "updateModelConfig", "getUserModelUsage"], "method_count": 6}, "MusicService": {"file": "MusicService.php", "used_models": ["AiModelConfig", "AiGenerationTask"], "direct_db_tables": [], "methods": ["__construct", "generateMusic", "getMusicStatus", "getMusicResult", "batchGenerateMusic"], "method_count": 5}, "NotificationService": {"file": "NotificationService.php", "used_models": [], "direct_db_tables": [], "methods": ["sendNotification", "sendBulkNotifications", "getNotifications", "getNotification", "resendNotification", "getNotificationStats", "getTemplates", "createTemplate", "getNotificationConfig", "getUserNotifications", "mark<PERSON><PERSON><PERSON>", "markAllAsRead", "deleteNotification", "sendSystemNotification"], "method_count": 14}, "PermissionService": {"file": "PermissionService.php", "used_models": [], "direct_db_tables": [], "methods": ["checkPermission", "checkMultiplePermissions", "getUserPermissions", "getUserRoles", "getRolePermissions", "assignRole", "removeRole", "getRoles", "getPermissions", "createRole", "updateRolePermissions", "getPermissionStats", "checkResourceAccess", "getUserPermissionDetails", "grantPermissions", "revokePermissions", "getPermissionHistory"], "method_count": 17}, "PointsService": {"file": "PointsService.php", "used_models": ["User", "PointsTransaction", "PointsFreeze"], "direct_db_tables": {"0": "users", "1": "points_transactions", "3": "points_freeze"}, "methods": ["recharge", "freezePoints", "releasePoints", "consumePoints", "handleTimeoutTransactions", "checkPoints", "confirmPointsUsage", "refundPoints", "refundPointsByFreezeId"], "method_count": 9}, "PointsTransactionService": {"file": "PointsTransactionService.php", "used_models": ["User", "PointsTransaction"], "direct_db_tables": [], "methods": ["createFrozenTransaction", "handleSuccess", "handleFailure", "validateBalance", "getBalance", "getTransactions"], "method_count": 6}, "ProjectManagementService": {"file": "ProjectManagementService.php", "used_models": ["Project", "ProjectCollaborator"], "direct_db_tables": [], "methods": ["createTask", "manageCollaboration", "getProjectProgress", "assignResources", "getProjectStatistics", "getProjectMilestones"], "method_count": 6}, "ProjectService": {"file": "ProjectService.php", "used_models": ["Project", "StyleLibrary", "User"], "direct_db_tables": [], "methods": ["checkAntiSpam", "createWithStory", "confirmTitle", "updateStatus", "createSimpleProject", "updateProject", "deleteProject", "getUserProjects", "getProjectDetail", "getProjectList"], "method_count": 10}, "PublicationService": {"file": "PublicationService.php", "used_models": ["Publication", "Resource", "User"], "direct_db_tables": [], "methods": ["__construct", "publishWork", "getPublicationStatus", "updatePublication", "unpublishWork", "getUserPublications", "getPublicationPlaza", "getPublicationDetail"], "method_count": 8}, "RecommendationService": {"file": "RecommendationService.php", "used_models": ["User", "Publication", "Template", "RecommendationFeedback", "UserPreference"], "direct_db_tables": [], "methods": ["getContentRecommendations", "getUserRecommendations", "getTopicRecommendations", "submitFeedback", "getUserPreferences", "updateUserPreferences", "getRecommendationAnalytics", "getPersonalizedRecommendations"], "method_count": 8}, "ResourceManagementService": {"file": "ResourceManagementService.php", "used_models": ["Resource", "Project", "AiGenerationTask"], "direct_db_tables": [], "methods": ["__construct", "createGenerationTask", "getResourceStatus", "getResourceList", "deleteResource"], "method_count": 5}, "ReviewService": {"file": "ReviewService.php", "used_models": ["Publication", "Review", "ReviewAppeal"], "direct_db_tables": [], "methods": ["submitReview", "getReviewStatus", "submitAppeal", "getUserReviews", "getQueueStatus", "getReviewGuidelines", "perform<PERSON>reCheck"], "method_count": 7}, "SearchService": {"file": "SearchService.php", "used_models": ["CharacterLibrary", "Project", "UserFile", "AiGenerationTask"], "direct_db_tables": [], "methods": ["globalSearch", "getSearchSuggestions"], "method_count": 2}, "SocialService": {"file": "SocialService.php", "used_models": ["User", "Follow", "Like", "Comment", "Share", "SocialActivity", "Notification", "Publication", "Template"], "direct_db_tables": [], "methods": ["manageFollow", "getFollowList", "manageLike", "createComment", "getComments", "shareContent", "getSocialFeed", "getNotifications", "markNotificationsRead"], "method_count": 9}, "SoundService": {"file": "SoundService.php", "used_models": ["AiModelConfig", "AiGenerationTask"], "direct_db_tables": [], "methods": ["__construct", "generateSound", "getSoundStatus", "getSoundResult", "batchGenerateSounds"], "method_count": 5}, "StoryService": {"file": "StoryService.php", "used_models": ["AiModelConfig", "AiGenerationTask", "StyleLibrary"], "direct_db_tables": [], "methods": ["__construct", "generateStory", "getStoryStatus"], "method_count": 3}, "StyleService": {"file": "StyleService.php", "used_models": ["StyleLibrary"], "direct_db_tables": [], "methods": ["createStyle", "updateRating", "getRecommendedStyles", "searchStyles", "getCategoryStats"], "method_count": 5}, "TaskManagementService": {"file": "TaskManagementService.php", "used_models": ["AiGenerationTask"], "direct_db_tables": [], "methods": ["__construct", "cancelTask", "retryTask", "getBatchTaskStatus", "getBatchTaskStatusByBatchId", "getTimeoutConfig", "getRecoveryStatus"], "method_count": 7}, "TemplateService": {"file": "TemplateService.php", "used_models": ["Template", "Project", "Resource"], "direct_db_tables": [], "methods": ["createTemplate", "useTemplate", "getTemplateMarketplace", "getUserTemplates", "getTemplateDetail", "updateTemplate", "deleteTemplate"], "method_count": 7}, "UserGrowthService": {"file": "UserGrowthServiceFix.php", "used_models": ["User"], "direct_db_tables": [], "methods": ["getUserGrowthProfile"], "method_count": 1}, "UserService": {"file": "UserService.php", "used_models": ["User", "UserPreference"], "direct_db_tables": [], "methods": ["updatePreferences", "getUserStats", "updateProfile", "resetPreferences"], "method_count": 4}, "VersionControlService": {"file": "VersionControlService.php", "used_models": ["Resource", "ResourceVersion"], "direct_db_tables": [], "methods": ["__construct", "createVersion", "getVersionHistory", "getVersionDetail", "setCurrentVersion", "deleteVersion", "compareVersions"], "method_count": 7}, "VideoService": {"file": "VideoService.php", "used_models": ["AiModelConfig", "AiGenerationTask"], "direct_db_tables": [], "methods": ["__construct", "generateVideo", "getVideoStatus", "getVideoResult"], "method_count": 4}, "VoiceService": {"file": "VoiceService.php", "used_models": ["AiModelConfig", "AiGenerationTask", "CharacterLibrary"], "direct_db_tables": [], "methods": ["__construct", "synthesizeVoice", "getVoiceStatus", "batchSynthesizeVoices", "cloneVoice", "getVoiceCloneStatus", "customVoice", "getVoiceCustomStatus"], "method_count": 8}, "WebSocketEventService": {"file": "WebSocketEventService.php", "used_models": ["WebSocketSession", "AiGenerationTask", "User"], "direct_db_tables": [], "methods": ["__construct", "pushAiGenerationProgress", "pushAiGenerationCompleted", "pushAiGenerationFailed", "pushPointsChanged", "pushCustomEvent", "pushSystemNotification", "pushToMultipleUsers", "pushBroadcast"], "method_count": 9}, "WebSocketService": {"file": "WebSocketService.php", "used_models": ["WebSocketSession", "User"], "direct_db_tables": [], "methods": ["authenticateConnection", "getUserSessions", "disconnectSession", "getServerStatus", "pushMessage", "pushToUser", "cleanupTimeoutSessions", "generateAuthToken"], "method_count": 8}, "WebSocketTokenService": {"file": "WebSocketTokenService.php", "used_models": [], "direct_db_tables": ["users"], "methods": [], "method_count": 0}, "WorkPublishPermissionService": {"file": "WorkPublishPermissionService.php", "used_models": ["Resource", "ResourceVersion"], "direct_db_tables": [], "methods": ["checkPublishPermission", "checkResourcePublishPermission", "mapModuleTypeToWorkType"], "method_count": 3}, "WorkPublishService": {"file": "WorkPublishService.php", "used_models": ["WorkPlaza", "UserWork", "WorkShare", "WorkInteraction"], "direct_db_tables": [], "methods": ["__construct", "publishWork", "getMyWorks", "getGallery"], "method_count": 4}, "WorkflowService": {"file": "WorkflowService.php", "used_models": [], "direct_db_tables": [], "methods": ["createWorkflow", "executeWorkflow", "getExecutionStatus", "getWorkflows", "getWorkflow", "updateWorkflow", "deleteWorkflow", "pauseWorkflow", "resumeWorkflow", "getExecutionHistory", "getWorkflowStats", "getWorkflowDetail", "executeWorkflowWithData", "getExecutionStatusWithAuth", "provideStepInput", "cancelExecution", "getExecutionHistoryWithFilters"], "method_count": 17}}, "models": {"Achievement": {"table_name": "achievements", "fillable_fields": ["name", "description", "type", "difficulty", "icon", "badge_color", "requirements", "reward_experience", "reward_points", "status", "sort_order", "is_hidden", "unlock_condition", "metadata"], "relations": [{"method": "userAchievements", "type": "hasMany", "target": "UserAchievement::class"}, {"method": "completedUsers", "type": "belongsToMany", "target": "User::class, 'p_user_achievements'"}], "relation_count": 2}, "AiGenerationTask": {"table_name": "ai_generation_tasks", "fillable_fields": ["user_id", "project_id", "model_config_id", "task_type", "platform", "model_name", "status", "input_data", "output_data", "generation_params", "external_task_id", "cost", "tokens_used", "processing_time_ms", "started_at", "completed_at", "error_message", "metadata", "retry_count", "max_retries"], "relations": [], "relation_count": 0}, "AiModelConfig": {"table_name": "ai_model_configs", "fillable_fields": ["platform", "model_name", "model_type", "api_endpoint", "config_params", "capabilities", "is_active", "is_default", "priority", "cost_per_request", "max_tokens", "timeout_seconds", "rate_limits", "performance_metrics", "last_health_check", "health_status", "health_message"], "relations": [], "relation_count": 0}, "CharacterCategory": {"table_name": "character_categories", "fillable_fields": ["name", "slug", "description", "icon", "color", "parent_id", "is_active", "sort_order", "character_count", "metadata"], "relations": [], "relation_count": 0}, "CharacterLibrary": {"table_name": "character_library", "fillable_fields": ["name", "description", "category_id", "gender", "age_range", "personality", "background", "appearance", "avatar", "images", "voice_config", "style_preferences", "tags", "is_active", "is_premium", "is_featured", "sort_order", "binding_count", "rating", "rating_count", "created_by"], "relations": [], "relation_count": 0}, "Comment": {"table_name": "comments", "fillable_fields": ["user_id", "target_type", "target_id", "parent_id", "content", "like_count", "reply_count"], "relations": [], "relation_count": 0}, "DailyTask": {"table_name": "daily_tasks", "fillable_fields": ["name", "description", "type", "difficulty", "icon", "requirements", "reward_experience", "reward_points", "status", "sort_order", "repeat_type", "max_completions_per_day", "is_premium", "unlock_level", "start_date", "end_date", "metadata"], "relations": [{"method": "userDailyTasks", "type": "hasMany", "target": "UserDailyTask::class"}, {"method": "completedUsers", "type": "belongsToMany", "target": "User::class, 'p_user_daily_tasks'"}], "relation_count": 2}, "Follow": {"table_name": "p_follows", "fillable_fields": ["follower_id", "following_id", "status", "followed_at", "metadata"], "relations": [{"method": "follower", "type": "belongsTo", "target": "User::class, 'follower_id'"}, {"method": "following", "type": "belongsTo", "target": "User::class, 'following_id'"}], "relation_count": 2}, "GrowthHistory": {"table_name": "growth_histories", "fillable_fields": ["user_id", "event_type", "event_description", "experience_before", "experience_change", "experience_after", "level_before", "level_after", "points_before", "points_change", "points_after", "related_type", "related_id", "metadata"], "relations": [{"method": "user", "type": "belongsTo", "target": "User::class"}], "relation_count": 1}, "Like": {"table_name": "likes", "fillable_fields": ["user_id", "target_type", "target_id"], "relations": [], "relation_count": 0}, "Notification": {"table_name": "notifications", "fillable_fields": ["user_id", "actor_id", "type", "title", "message", "data", "is_read", "read_at"], "relations": [], "relation_count": 0}, "PlatformPerformanceMetric": {"table_name": "platform_performance_metrics", "fillable_fields": ["platform", "business_type", "response_time_avg", "success_rate", "cost_score", "quality_score", "total_requests", "failed_requests", "uptime_percentage", "detailed_metrics", "metric_date"], "relations": [], "relation_count": 0}, "PlatformUsageStatistic": {"table_name": "platform_usage_statistics", "fillable_fields": ["user_id", "task_type", "platform", "usage_count", "success_count", "failure_count", "total_cost", "average_response_time", "user_satisfaction_score", "user_rating_count", "performance_metrics", "error_details", "last_used_at"], "relations": [], "relation_count": 0}, "PointsFreeze": {"table_name": "points_freeze", "fillable_fields": ["user_id", "transaction_id", "amount", "status", "business_type", "business_id", "expires_at", "released_at", "reason"], "relations": [], "relation_count": 0}, "PointsTransaction": {"table_name": "points_transactions", "fillable_fields": ["user_id", "business_type", "business_id", "amount", "status", "ai_platform", "request_data", "response_data", "timeout_seconds", "completed_at", "failure_reason"], "relations": [], "relation_count": 0}, "Project": {"table_name": "projects", "fillable_fields": ["user_id", "title", "description", "style_id", "story_content", "ai_generated_title", "title_confirmed", "status", "project_config", "metadata", "last_accessed_at", "completed_at", "published_at", "view_count", "is_public"], "relations": [], "relation_count": 0}, "ProjectCollaborator": {"table_name": "project_collaborators", "fillable_fields": ["project_id", "user_id", "role", "permissions", "status", "invited_by"], "relations": [{"method": "project", "type": "belongsTo", "target": "Project::class"}, {"method": "user", "type": "belongsTo", "target": "User::class"}, {"method": "inviter", "type": "belongsTo", "target": "User::class, 'invited_by'"}], "relation_count": 3}, "Publication": {"table_name": "publications", "fillable_fields": ["user_id", "resource_id", "title", "description", "tags", "category", "visibility", "status", "review_status", "allow_comments", "allow_download", "featured", "view_count", "like_count", "comment_count", "download_count", "share_count", "thumbnail", "metadata", "published_at", "unpublished_at"], "relations": [{"method": "user", "type": "belongsTo", "target": "User::class"}, {"method": "resource", "type": "belongsTo", "target": "Resource::class"}, {"method": "reviews", "type": "hasMany", "target": "Review::class"}, {"method": "comments", "type": "hasMany", "target": "PublicationComment::class"}, {"method": "likes", "type": "hasMany", "target": "PublicationLike::class"}], "relation_count": 5}, "Recommendation": {"table_name": "p_recommendations", "fillable_fields": ["user_id", "recommended_type", "recommended_id", "recommendation_type", "algorithm_type", "score", "reason", "status", "expires_at", "metadata"], "relations": [{"method": "user", "type": "belongsTo", "target": "User::class"}, {"method": "feedbacks", "type": "hasMany", "target": "RecommendationFeedback::class"}], "relation_count": 2}, "RecommendationFeedback": {"table_name": "p_recommendation_feedbacks", "fillable_fields": ["user_id", "recommendation_id", "content_type", "content_id", "feedback_type", "status", "score", "comment", "context_data", "processed_at"], "relations": [{"method": "user", "type": "belongsTo", "target": "User::class"}, {"method": "recommendation", "type": "belongsTo", "target": "Recommendation::class"}], "relation_count": 2}, "Resource": {"table_name": "resources", "fillable_fields": ["resource_uuid", "user_id", "project_id", "resource_type", "status", "generation_config", "output_format", "quality_level", "batch_size", "estimated_cost", "actual_cost", "generation_task_id", "file_path", "file_size", "file_hash", "download_count", "processing_time_ms", "metadata", "completed_at"], "relations": [{"method": "user", "type": "belongsTo", "target": "User::class"}, {"method": "project", "type": "belongsTo", "target": "Project::class"}, {"method": "generationTasks", "type": "hasMany", "target": "AiGenerationTask::class, 'project_id', 'project_id'"}, {"method": "versions", "type": "hasMany", "target": "ResourceVersion::class"}, {"method": "currentVersion", "type": "belongsTo", "target": "ResourceVersion::class, 'current_version_id'"}, {"method": "exports", "type": "hasMany", "target": "ResourceExport::class"}, {"method": "downloads", "type": "hasMany", "target": "ResourceDownload::class"}], "relation_count": 7}, "ResourceDownload": {"table_name": "resource_downloads", "fillable_fields": ["user_id", "download_type", "target_id", "target_name", "file_path", "file_size", "download_token", "status", "download_count", "retry_count", "batch_id", "user_agent", "ip_address", "error_message", "metadata", "downloaded_at", "expires_at", "cancelled_at"], "relations": [{"method": "user", "type": "belongsTo", "target": "User::class"}, {"method": "target", "type": "belongsTo", "target": "ResourceVersion::class, 'target_id'"}], "relation_count": 2}, "ResourceExport": {"table_name": "resource_exports", "fillable_fields": ["user_id", "resource_ids", "export_format", "export_options", "include_metadata", "include_versions", "compression_level", "status", "progress", "resource_count", "processed_count", "estimated_size", "estimated_duration", "file_path", "file_size", "download_count", "processing_time_ms", "error_message", "metadata", "started_at", "completed_at", "cancelled_at", "expires_at"], "relations": [{"method": "user", "type": "belongsTo", "target": "User::class"}, {"method": "resources", "type": "belongsToMany", "target": "Resource::class, 'resource_export_items', 'export_id', 'resource_id'"}], "relation_count": 2}, "ResourceVersion": {"table_name": "resource_versions", "fillable_fields": ["resource_id", "user_id", "version_number", "version_name", "description", "status", "generation_config", "generation_task_id", "base_version_id", "file_path", "file_url", "file_size", "file_hash", "estimated_cost", "actual_cost", "processing_time_ms", "is_current", "metadata", "completed_at"], "relations": [{"method": "resource", "type": "belongsTo", "target": "Resource::class"}, {"method": "user", "type": "belongsTo", "target": "User::class"}, {"method": "generationTask", "type": "belongsTo", "target": "AiGenerationTask::class, 'generation_task_id'"}, {"method": "baseVersion", "type": "belongsTo", "target": "ResourceVersion::class, 'base_version_id'"}, {"method": "derivedVersions", "type": "hasMany", "target": "ResourceVersion::class, 'base_version_id'"}], "relation_count": 5}, "Review": {"table_name": "reviews", "fillable_fields": ["publication_id", "user_id", "reviewer_id", "review_type", "status", "priority", "review_score", "review_message", "review_criteria", "additional_info", "metadata", "reviewed_at"], "relations": [{"method": "publication", "type": "belongsTo", "target": "Publication::class"}, {"method": "user", "type": "belongsTo", "target": "User::class"}, {"method": "reviewer", "type": "belongsTo", "target": "User::class, 'reviewer_id'"}, {"method": "appeals", "type": "hasMany", "target": "ReviewAppeal::class"}], "relation_count": 4}, "ReviewAppeal": {"table_name": "review_appeals", "fillable_fields": ["review_id", "user_id", "appeal_reason", "additional_evidence", "status", "handler_id", "handler_message", "metadata", "handled_at"], "relations": [{"method": "review", "type": "belongsTo", "target": "Review::class"}, {"method": "user", "type": "belongsTo", "target": "User::class"}, {"method": "handler", "type": "belongsTo", "target": "User::class, 'handler_id'"}], "relation_count": 3}, "Share": {"table_name": "shares", "fillable_fields": ["user_id", "target_type", "target_id", "platform", "message", "share_url", "shared_at"], "relations": [], "relation_count": 0}, "SocialActivity": {"table_name": "social_activitys", "fillable_fields": ["user_id", "action", "target_type", "target_id", "metadata"], "relations": [], "relation_count": 0}, "StyleLibrary": {"table_name": "style_library", "fillable_fields": ["name", "description", "category", "style_config", "prompt_template", "thumbnail", "is_active", "is_premium", "sort_order", "usage_count", "rating", "tags", "created_by"], "relations": [], "relation_count": 0}, "SystemMonitor": {"table_name": "system_monitors", "fillable_fields": ["metric_type", "metric_name", "metric_value", "metric_unit", "metric_details", "source", "environment", "status", "alert_message", "collected_at"], "relations": [], "relation_count": 0}, "Template": {"table_name": "templates", "fillable_fields": ["user_id", "name", "description", "type", "category", "source_type", "source_id", "visibility", "status", "tags", "configuration", "usage_count", "rating", "review_count", "share_count", "like_count", "comment_count", "featured", "metadata"], "relations": [{"method": "user", "type": "belongsTo", "target": "User::class"}], "relation_count": 1}, "User": {"table_name": "users", "fillable_fields": ["username", "email", "password", "nickname", "avatar", "bio", "level", "experience", "follower_count", "following_count", "inviter_id", "remark", "status", "points", "frozen_points", "is_vip", "vip_expires_at", "last_login_ip", "last_login_at"], "relations": [], "relation_count": 0}, "UserAchievement": {"table_name": "user_achievements", "fillable_fields": ["user_id", "achievement_id", "unlocked_at", "progress", "current_value", "target_value", "is_notified", "notified_at", "metadata"], "relations": [{"method": "user", "type": "belongsTo", "target": "User::class"}, {"method": "achievement", "type": "belongsTo", "target": "Achievement::class"}], "relation_count": 2}, "UserCharacterBinding": {"table_name": "user_character_bindings", "fillable_fields": ["user_id", "character_id", "binding_name", "binding_reason", "custom_description", "custom_config", "is_active", "is_favorite", "usage_count", "last_used_at", "user_rating", "user_feedback", "usage_stats"], "relations": [], "relation_count": 0}, "UserDailyTask": {"table_name": "user_daily_tasks", "fillable_fields": ["user_id", "daily_task_id", "completed", "completed_at", "completion_count", "progress", "target_value", "current_value", "reward_claimed", "reward_claimed_at", "metadata"], "relations": [{"method": "user", "type": "belongsTo", "target": "User::class"}, {"method": "dailyTask", "type": "belongsTo", "target": "DailyTask::class"}], "relation_count": 2}, "UserFile": {"table_name": "user_files", "fillable_fields": ["user_id", "filename", "original_name", "file_path", "file_url", "file_type", "mime_type", "file_size", "file_hash", "storage_driver", "folder_path", "metadata", "thumbnails", "is_public", "is_temporary", "download_count", "last_accessed_at", "expires_at"], "relations": [], "relation_count": 0}, "UserLevel": {"table_name": "user_levels", "fillable_fields": ["level", "name", "description", "type", "required_experience", "icon", "color", "badge", "privileges", "rewards", "unlock_features", "is_active", "sort_order", "metadata"], "relations": [{"method": "users", "type": "hasMany", "target": "User::class, 'level', 'level'"}], "relation_count": 1}, "UserModelPreference": {"table_name": "user_model_preferences", "fillable_fields": ["user_id", "business_type", "preferred_platform", "platform_priorities", "selection_criteria", "auto_fallback", "cost_optimization", "custom_config", "usage_count", "last_used_at"], "relations": [], "relation_count": 0}, "UserPreference": {"table_name": "user_preferences", "fillable_fields": ["user_id", "language", "timezone", "email_notifications", "push_notifications", "ai_preferences", "ui_preferences", "workflow_preferences", "default_ai_model", "auto_save_interval", "show_tutorials"], "relations": [], "relation_count": 0}, "UserWork": {"table_name": "user_works", "fillable_fields": ["user_id", "work_title", "work_description", "work_file_path", "work_thumbnail", "publish_status", "work_metadata", "view_count", "like_count", "share_count", "published_at"], "relations": [], "relation_count": 0}, "WebSocketSession": {"table_name": "websocket_sessions", "fillable_fields": ["session_id", "user_id", "client_type", "client_version", "connection_ip", "user_agent", "status", "connection_info", "subscribed_events", "connected_at", "last_ping_at", "disconnected_at", "message_count", "disconnect_reason"], "relations": [], "relation_count": 0}, "WorkInteraction": {"table_name": "work_interactions", "fillable_fields": ["work_id", "user_id", "interaction_type", "interaction_content", "interaction_metadata"], "relations": [], "relation_count": 0}, "WorkPlaza": {"table_name": "work_plaza", "fillable_fields": ["work_uuid", "user_id", "work_type", "file_name", "file_extension", "file_size", "mime_type", "file_path", "work_title", "work_description", "work_tags", "source_resource_id", "source_module_id", "status", "auto_review_result", "auto_review_reason", "manual_reviewer_id", "manual_reviewed_at", "manual_review_notes", "publish_status", "published_at", "view_count", "like_count", "share_count", "download_count", "comment_count", "recommendation_score", "is_featured", "report_count"], "relations": [], "relation_count": 0}, "WorkShare": {"table_name": "work_shares", "fillable_fields": ["work_id", "share_token", "share_type", "share_password", "expires_at", "access_count", "max_access_count", "is_active"], "relations": [], "relation_count": 0}}, "table_usage": {"achievements": {"used_by_models": ["Achievement"], "used_by_controllers": [], "used_by_services": [], "direct_db_usage": []}, "ai_generation_tasks": {"used_by_models": ["AiGenerationTask"], "used_by_controllers": [], "used_by_services": ["AiGenerationService", "AiModelService", "AiTaskService", "AudioService", "CharacterService", "ImageService", "MusicService", "ResourceManagementService", "SearchService", "SoundService", "StoryService", "TaskManagementService", "VideoService", "VoiceService", "WebSocketEventService"], "direct_db_usage": []}, "ai_model_configs": {"used_by_models": ["AiModelConfig"], "used_by_controllers": [], "used_by_services": ["AiGenerationService", "AiLoadBalancingService", "AiModelService", "AiPlatformFallbackService", "AiPlatformHealthService", "AiPlatformSelectionService", "CharacterService", "ImageService", "MusicService", "SoundService", "StoryService", "VideoService", "VoiceService"], "direct_db_usage": ["ModelManagementService"]}, "character_categories": {"used_by_models": ["CharacterCategory"], "used_by_controllers": ["CharacterController"], "used_by_services": ["CharacterService"], "direct_db_usage": []}, "character_library": {"used_by_models": ["CharacterLibrary"], "used_by_controllers": ["CharacterController"], "used_by_services": ["CharacterService", "ImageService", "SearchService", "VoiceService"], "direct_db_usage": []}, "comments": {"used_by_models": ["Comment"], "used_by_controllers": [], "used_by_services": ["SocialService"], "direct_db_usage": []}, "daily_tasks": {"used_by_models": ["DailyTask"], "used_by_controllers": [], "used_by_services": [], "direct_db_usage": []}, "p_follows": {"used_by_models": ["Follow"], "used_by_controllers": [], "used_by_services": ["SocialService"], "direct_db_usage": []}, "growth_histories": {"used_by_models": ["GrowthHistory"], "used_by_controllers": [], "used_by_services": [], "direct_db_usage": []}, "likes": {"used_by_models": ["Like"], "used_by_controllers": [], "used_by_services": ["SocialService"], "direct_db_usage": []}, "notifications": {"used_by_models": ["Notification"], "used_by_controllers": [], "used_by_services": ["SocialService"], "direct_db_usage": []}, "platform_performance_metrics": {"used_by_models": ["PlatformPerformanceMetric"], "used_by_controllers": [], "used_by_services": ["AiLoadBalancingService", "AiPlatformHealthService", "AiPlatformSelectionService"], "direct_db_usage": []}, "platform_usage_statistics": {"used_by_models": ["PlatformUsageStatistic"], "used_by_controllers": [], "used_by_services": [], "direct_db_usage": []}, "points_freeze": {"used_by_models": ["PointsFreeze"], "used_by_controllers": [], "used_by_services": ["PointsService"], "direct_db_usage": ["PointsService"]}, "points_transactions": {"used_by_models": ["PointsTransaction"], "used_by_controllers": ["PointsController"], "used_by_services": ["PointsService", "PointsTransactionService"], "direct_db_usage": ["PointsService"]}, "projects": {"used_by_models": ["Project"], "used_by_controllers": ["ProjectController"], "used_by_services": ["ProjectManagementService", "ProjectService", "ResourceManagementService", "SearchService", "TemplateService"], "direct_db_usage": []}, "project_collaborators": {"used_by_models": ["ProjectCollaborator"], "used_by_controllers": [], "used_by_services": ["ProjectManagementService"], "direct_db_usage": []}, "publications": {"used_by_models": ["Publication"], "used_by_controllers": [], "used_by_services": ["PublicationService", "RecommendationService", "ReviewService", "SocialService"], "direct_db_usage": []}, "p_recommendations": {"used_by_models": ["Recommendation"], "used_by_controllers": [], "used_by_services": [], "direct_db_usage": []}, "p_recommendation_feedbacks": {"used_by_models": ["RecommendationFeedback"], "used_by_controllers": [], "used_by_services": ["RecommendationService"], "direct_db_usage": []}, "resources": {"used_by_models": ["Resource"], "used_by_controllers": [], "used_by_services": ["DownloadManagementService", "PublicationService", "ResourceManagementService", "TemplateService", "VersionControlService", "WorkPublishPermissionService"], "direct_db_usage": ["WorkPublishController"]}, "resource_downloads": {"used_by_models": ["ResourceDownload"], "used_by_controllers": [], "used_by_services": ["DownloadManagementService"], "direct_db_usage": []}, "resource_exports": {"used_by_models": ["ResourceExport"], "used_by_controllers": [], "used_by_services": ["DownloadManagementService"], "direct_db_usage": []}, "resource_versions": {"used_by_models": ["ResourceVersion"], "used_by_controllers": [], "used_by_services": ["DownloadManagementService", "VersionControlService", "WorkPublishPermissionService"], "direct_db_usage": []}, "reviews": {"used_by_models": ["Review"], "used_by_controllers": [], "used_by_services": ["ReviewService"], "direct_db_usage": []}, "review_appeals": {"used_by_models": ["ReviewAppeal"], "used_by_controllers": [], "used_by_services": ["ReviewService"], "direct_db_usage": []}, "shares": {"used_by_models": ["Share"], "used_by_controllers": [], "used_by_services": ["SocialService"], "direct_db_usage": []}, "social_activitys": {"used_by_models": ["SocialActivity"], "used_by_controllers": [], "used_by_services": ["SocialService"], "direct_db_usage": []}, "style_library": {"used_by_models": ["StyleLibrary"], "used_by_controllers": ["ProjectController", "StyleController"], "used_by_services": ["ProjectService", "StoryService", "StyleService"], "direct_db_usage": []}, "system_monitors": {"used_by_models": ["SystemMonitor"], "used_by_controllers": [], "used_by_services": [], "direct_db_usage": []}, "templates": {"used_by_models": ["Template"], "used_by_controllers": [], "used_by_services": ["RecommendationService", "SocialService", "TemplateService"], "direct_db_usage": []}, "users": {"used_by_models": ["User"], "used_by_controllers": ["PointsController"], "used_by_services": ["AiGenerationService", "ModelManagementService", "PointsService", "PointsTransactionService", "ProjectService", "PublicationService", "RecommendationService", "SocialService", "UserGrowthService", "UserService", "WebSocketEventService", "WebSocketService"], "direct_db_usage": ["AuthService", "PointsService", "WebSocketTokenService"]}, "user_achievements": {"used_by_models": ["UserAchievement"], "used_by_controllers": [], "used_by_services": [], "direct_db_usage": []}, "user_character_bindings": {"used_by_models": ["UserCharacterBinding"], "used_by_controllers": [], "used_by_services": ["CharacterService"], "direct_db_usage": []}, "user_daily_tasks": {"used_by_models": ["UserDailyTask"], "used_by_controllers": [], "used_by_services": [], "direct_db_usage": []}, "user_files": {"used_by_models": ["UserFile"], "used_by_controllers": [], "used_by_services": ["FileService", "SearchService"], "direct_db_usage": []}, "user_levels": {"used_by_models": ["UserLevel"], "used_by_controllers": [], "used_by_services": [], "direct_db_usage": []}, "user_model_preferences": {"used_by_models": ["UserModelPreference"], "used_by_controllers": [], "used_by_services": ["AiPlatformFallbackService", "AiPlatformSelectionService"], "direct_db_usage": []}, "user_preferences": {"used_by_models": ["UserPreference"], "used_by_controllers": ["UserController"], "used_by_services": ["AiModelService", "RecommendationService", "UserService"], "direct_db_usage": []}, "user_works": {"used_by_models": ["UserWork"], "used_by_controllers": [], "used_by_services": ["WorkPublishService"], "direct_db_usage": []}, "websocket_sessions": {"used_by_models": ["WebSocketSession"], "used_by_controllers": [], "used_by_services": ["WebSocketEventService", "WebSocketService"], "direct_db_usage": []}, "work_interactions": {"used_by_models": ["WorkInteraction"], "used_by_controllers": ["WorkPublishController"], "used_by_services": ["WorkPublishService"], "direct_db_usage": []}, "work_plaza": {"used_by_models": ["WorkPlaza"], "used_by_controllers": ["WorkPublishController"], "used_by_services": ["WorkPublishService"], "direct_db_usage": ["WorkPublishController"]}, "work_shares": {"used_by_models": ["WorkShare"], "used_by_controllers": [], "used_by_services": ["WorkPublishService"], "direct_db_usage": []}, "user_ad_logs": {"used_by_models": [], "used_by_controllers": [], "used_by_services": [], "direct_db_usage": ["AdService"]}, "user_assets": {"used_by_models": [], "used_by_controllers": [], "used_by_services": [], "direct_db_usage": ["AssetService"]}, "p_users": {"used_by_models": [], "used_by_controllers": [], "used_by_services": [], "direct_db_usage": ["AuthService"]}, "ai_models": {"used_by_models": [], "used_by_controllers": [], "used_by_services": [], "direct_db_usage": ["ModelService"]}, "model_invocations": {"used_by_models": [], "used_by_controllers": [], "used_by_services": [], "direct_db_usage": ["ModelService"]}, "user_model_permissions": {"used_by_models": [], "used_by_controllers": [], "used_by_services": [], "direct_db_usage": ["ModelService"]}, "user_model_quotas": {"used_by_models": [], "used_by_controllers": [], "used_by_services": [], "direct_db_usage": ["ModelService"]}}, "model_table_mapping": {"Achievement": "achievements", "AiGenerationTask": "ai_generation_tasks", "AiModelConfig": "ai_model_configs", "CharacterCategory": "character_categories", "CharacterLibrary": "character_library", "Comment": "comments", "DailyTask": "daily_tasks", "Follow": "p_follows", "GrowthHistory": "growth_histories", "Like": "likes", "Notification": "notifications", "PlatformPerformanceMetric": "platform_performance_metrics", "PlatformUsageStatistic": "platform_usage_statistics", "PointsFreeze": "points_freeze", "PointsTransaction": "points_transactions", "Project": "projects", "ProjectCollaborator": "project_collaborators", "Publication": "publications", "Recommendation": "p_recommendations", "RecommendationFeedback": "p_recommendation_feedbacks", "Resource": "resources", "ResourceDownload": "resource_downloads", "ResourceExport": "resource_exports", "ResourceVersion": "resource_versions", "Review": "reviews", "ReviewAppeal": "review_appeals", "Share": "shares", "SocialActivity": "social_activitys", "StyleLibrary": "style_library", "SystemMonitor": "system_monitors", "Template": "templates", "User": "users", "UserAchievement": "user_achievements", "UserCharacterBinding": "user_character_bindings", "UserDailyTask": "user_daily_tasks", "UserFile": "user_files", "UserLevel": "user_levels", "UserModelPreference": "user_model_preferences", "UserPreference": "user_preferences", "UserWork": "user_works", "WebSocketSession": "websocket_sessions", "WorkInteraction": "work_interactions", "WorkPlaza": "work_plaza", "WorkShare": "work_shares"}}