<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Recommendation extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * 数据表名称
     */
    protected $table = 'recommendations';

    /**
     * 推荐状态常量
     */
    const STATUS_ACTIVE = 'active';           // 活跃推荐
    const STATUS_INACTIVE = 'inactive';       // 非活跃推荐
    const STATUS_EXPIRED = 'expired';         // 已过期
    const STATUS_PROCESSED = 'processed';     // 已处理
    const STATUS_PENDING = 'pending';         // 待处理

    /**
     * 推荐类型常量
     */
    const TYPE_PROJECT = 'project';           // 项目推荐
    const TYPE_RESOURCE = 'resource';         // 资源推荐
    const TYPE_USER = 'user';                 // 用户推荐
    const TYPE_CONTENT = 'content';           // 内容推荐

    /**
     * 推荐算法类型
     */
    const ALGORITHM_COLLABORATIVE = 'collaborative';  // 协同过滤
    const ALGORITHM_CONTENT_BASED = 'content_based';  // 基于内容
    const ALGORITHM_HYBRID = 'hybrid';                // 混合算法
    const ALGORITHM_POPULAR = 'popular';              // 热门推荐

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'user_id',
        'recommended_type',
        'recommended_id',
        'recommendation_type',
        'algorithm_type',
        'score',
        'reason',
        'status',
        'expires_at',
        'metadata'
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'score' => 'decimal:4',
        'expires_at' => 'datetime',
        'metadata' => 'array'
    ];

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 推荐反馈
     */
    public function feedbacks()
    {
        return $this->hasMany(RecommendationFeedback::class);
    }

    /**
     * 多态关联 - 被推荐的内容
     */
    public function recommended()
    {
        return $this->morphTo();
    }

    /**
     * 作用域：活跃推荐
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * 作用域：未过期推荐
     */
    public function scopeNotExpired($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('expires_at')
              ->orWhere('expires_at', '>', now());
        });
    }

    /**
     * 作用域：按推荐类型
     */
    public function scopeByType($query, $type)
    {
        return $query->where('recommendation_type', $type);
    }

    /**
     * 作用域：按算法类型
     */
    public function scopeByAlgorithm($query, $algorithm)
    {
        return $query->where('algorithm_type', $algorithm);
    }

    /**
     * 作用域：高分推荐
     */
    public function scopeHighScore($query, $minScore = 0.7)
    {
        return $query->where('score', '>=', $minScore);
    }

    /**
     * 检查推荐是否已过期
     */
    public function isExpired()
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * 检查推荐是否活跃
     */
    public function isActive()
    {
        return $this->status === self::STATUS_ACTIVE && !$this->isExpired();
    }

    /**
     * 标记为已处理
     */
    public function markAsProcessed()
    {
        $this->update(['status' => self::STATUS_PROCESSED]);
    }

    /**
     * 获取推荐评分等级
     */
    public function getScoreGrade()
    {
        if ($this->score >= 0.9) return 'excellent';
        if ($this->score >= 0.7) return 'good';
        if ($this->score >= 0.5) return 'average';
        return 'poor';
    }

    /**
     * 获取所有推荐类型
     */
    public static function getRecommendationTypes()
    {
        return [
            self::TYPE_PROJECT => '项目推荐',
            self::TYPE_RESOURCE => '资源推荐',
            self::TYPE_USER => '用户推荐',
            self::TYPE_CONTENT => '内容推荐'
        ];
    }

    /**
     * 获取所有算法类型
     */
    public static function getAlgorithmTypes()
    {
        return [
            self::ALGORITHM_COLLABORATIVE => '协同过滤',
            self::ALGORITHM_CONTENT_BASED => '基于内容',
            self::ALGORITHM_HYBRID => '混合算法',
            self::ALGORITHM_POPULAR => '热门推荐'
        ];
    }

    /**
     * 获取所有状态
     */
    public static function getStatuses()
    {
        return [
            self::STATUS_ACTIVE => '活跃',
            self::STATUS_INACTIVE => '非活跃',
            self::STATUS_EXPIRED => '已过期',
            self::STATUS_PROCESSED => '已处理',
            self::STATUS_PENDING => '待处理'
        ];
    }
}