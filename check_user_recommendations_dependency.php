<?php
/**
 * 检查 RecommendationController 和 RecommendationService 是否依赖 user_recommendations 表
 * 分析是否可以安全删除文档中关于 user_recommendations 的设计
 */

echo "🔍 检查 user_recommendations 表依赖关系\n";
echo str_repeat("=", 60) . "\n\n";

// 要检查的文件
$filesToCheck = [
    'php/api/app/Http/Controllers/PyApi/RecommendationController.php',
    'php/api/app/Services/PyApi/RecommendationService.php',
    'php/api/app/Models/Recommendation.php',
    'php/api/app/Models/RecommendationFeedback.php'
];

$dependencyAnalysis = [
    'analysis_time' => date('Y-m-d H:i:s'),
    'files_checked' => [],
    'user_recommendations_references' => [],
    'actual_table_dependencies' => [],
    'cache_key_references' => [],
    'safe_to_remove' => true,
    'recommendations' => []
];

echo "📋 1. 文件依赖分析\n";
echo "-------------------\n";

foreach ($filesToCheck as $file) {
    if (!file_exists($file)) {
        echo "❌ 文件不存在: $file\n";
        continue;
    }
    
    $content = file_get_contents($file);
    $fileName = basename($file);
    
    echo "✅ 检查文件: $fileName\n";
    
    $fileAnalysis = [
        'file' => $file,
        'user_recommendations_mentions' => [],
        'table_references' => [],
        'model_uses' => [],
        'db_queries' => []
    ];
    
    // 1. 检查 user_recommendations 的直接引用
    if (preg_match_all('/user_recommendations/i', $content, $matches, PREG_OFFSET_CAPTURE)) {
        foreach ($matches[0] as $match) {
            $lineNumber = substr_count(substr($content, 0, $match[1]), "\n") + 1;
            $fileAnalysis['user_recommendations_mentions'][] = [
                'line' => $lineNumber,
                'context' => trim(explode("\n", $content)[$lineNumber - 1])
            ];
        }
    }
    
    // 2. 检查数据库表引用
    if (preg_match_all('/DB::table\([\'"]([^\'"]+)[\'"]\)/', $content, $matches)) {
        $fileAnalysis['table_references'] = array_unique($matches[1]);
    }
    
    // 3. 检查模型使用
    if (preg_match_all('/use App\\\\Models\\\\(\w+);/', $content, $matches)) {
        $fileAnalysis['model_uses'] = array_unique($matches[1]);
    }
    
    // 4. 检查直接的数据库查询
    if (preg_match_all('/->table\([\'"]([^\'"]+)[\'"]\)/', $content, $matches)) {
        $fileAnalysis['table_references'] = array_merge($fileAnalysis['table_references'], $matches[1]);
    }
    
    $dependencyAnalysis['files_checked'][] = $fileAnalysis;
    
    // 输出分析结果
    if (!empty($fileAnalysis['user_recommendations_mentions'])) {
        echo "  ⚠️  发现 user_recommendations 引用:\n";
        foreach ($fileAnalysis['user_recommendations_mentions'] as $mention) {
            echo "    - 第{$mention['line']}行: {$mention['context']}\n";
        }
        $dependencyAnalysis['user_recommendations_references'][] = $fileAnalysis;
    } else {
        echo "  ✅ 无 user_recommendations 表引用\n";
    }
    
    if (!empty($fileAnalysis['table_references'])) {
        echo "  📊 数据库表引用: " . implode(', ', array_unique($fileAnalysis['table_references'])) . "\n";
        $dependencyAnalysis['actual_table_dependencies'] = array_merge(
            $dependencyAnalysis['actual_table_dependencies'], 
            $fileAnalysis['table_references']
        );
    }
    
    if (!empty($fileAnalysis['model_uses'])) {
        echo "  🏗️  模型使用: " . implode(', ', $fileAnalysis['model_uses']) . "\n";
    }
    
    echo "\n";
}

// 去重实际的表依赖
$dependencyAnalysis['actual_table_dependencies'] = array_unique($dependencyAnalysis['actual_table_dependencies']);

echo "🔍 2. 详细依赖分析\n";
echo "-------------------\n";

// 分析 user_recommendations 引用的性质
$criticalDependencies = [];
$nonCriticalReferences = [];

foreach ($dependencyAnalysis['user_recommendations_references'] as $fileRef) {
    foreach ($fileRef['user_recommendations_mentions'] as $mention) {
        $context = strtolower($mention['context']);
        
        // 检查是否是关键依赖
        if (strpos($context, 'db::table') !== false || 
            strpos($context, '->table(') !== false ||
            strpos($context, 'model') !== false ||
            strpos($context, 'create') !== false ||
            strpos($context, 'insert') !== false ||
            strpos($context, 'update') !== false ||
            strpos($context, 'delete') !== false) {
            $criticalDependencies[] = [
                'file' => basename($fileRef['file']),
                'line' => $mention['line'],
                'context' => $mention['context'],
                'type' => 'database_operation'
            ];
        } else if (strpos($context, 'cache') !== false || 
                   strpos($context, 'key') !== false) {
            $nonCriticalReferences[] = [
                'file' => basename($fileRef['file']),
                'line' => $mention['line'],
                'context' => $mention['context'],
                'type' => 'cache_key'
            ];
        } else {
            $nonCriticalReferences[] = [
                'file' => basename($fileRef['file']),
                'line' => $mention['line'],
                'context' => $mention['context'],
                'type' => 'other'
            ];
        }
    }
}

echo "关键依赖 (数据库操作): " . count($criticalDependencies) . " 个\n";
foreach ($criticalDependencies as $dep) {
    echo "  ❌ {$dep['file']}:{$dep['line']} - {$dep['context']}\n";
}

echo "\n非关键引用 (缓存键等): " . count($nonCriticalReferences) . " 个\n";
foreach ($nonCriticalReferences as $ref) {
    echo "  ⚠️  {$ref['file']}:{$ref['line']} - {$ref['context']}\n";
}

echo "\n实际使用的数据库表:\n";
foreach ($dependencyAnalysis['actual_table_dependencies'] as $table) {
    echo "  📊 $table\n";
}

// 3. 安全性评估
echo "\n🛡️ 3. 安全性评估\n";
echo "-------------------\n";

$safeToRemove = count($criticalDependencies) === 0;
$dependencyAnalysis['safe_to_remove'] = $safeToRemove;

if ($safeToRemove) {
    echo "✅ 安全评估: 可以安全删除\n";
    echo "原因:\n";
    echo "  - 没有发现对 user_recommendations 表的直接数据库操作\n";
    echo "  - 所有引用都是非关键的（缓存键、变量名等）\n";
    echo "  - 实际功能使用的是 p_recommendations 和 p_recommendation_feedbacks 表\n";
} else {
    echo "❌ 安全评估: 不建议删除\n";
    echo "原因:\n";
    echo "  - 发现 " . count($criticalDependencies) . " 个关键依赖\n";
    echo "  - 需要先修改代码移除这些依赖\n";
}

// 4. 建议方案
echo "\n💡 4. 建议方案\n";
echo "-------------------\n";

if ($safeToRemove) {
    $recommendations = [
        "可以安全删除文档中的 user_recommendations 表设计",
        "当前代码使用的是更完善的 p_recommendations 表架构",
        "建议更新文档以反映实际的实现架构",
        "保留现有的推荐系统实现，无需修改代码"
    ];
    
    if (!empty($nonCriticalReferences)) {
        $recommendations[] = "可选：清理代码中的 user_recommendations 缓存键引用，使用更准确的命名";
    }
} else {
    $recommendations = [
        "暂时保留文档中的 user_recommendations 表设计",
        "先修改代码中的关键依赖",
        "将数据库操作迁移到 p_recommendations 表",
        "完成代码修改后再删除文档设计"
    ];
}

foreach ($recommendations as $i => $rec) {
    echo ($i + 1) . ". $rec\n";
}

$dependencyAnalysis['recommendations'] = $recommendations;

// 5. 具体的清理建议
if ($safeToRemove && !empty($nonCriticalReferences)) {
    echo "\n🧹 5. 可选的代码清理\n";
    echo "-------------------\n";
    echo "以下非关键引用可以进行清理:\n\n";
    
    foreach ($nonCriticalReferences as $ref) {
        if ($ref['type'] === 'cache_key') {
            echo "文件: {$ref['file']}\n";
            echo "行号: {$ref['line']}\n";
            echo "当前: {$ref['context']}\n";
            
            // 建议替换
            if (strpos($ref['context'], 'user_recommendations_') !== false) {
                $suggested = str_replace('user_recommendations_', 'user_content_recommendations_', $ref['context']);
                echo "建议: $suggested\n";
            }
            echo "\n";
        }
    }
}

// 保存分析结果
file_put_contents('user_recommendations_dependency_analysis.json', json_encode($dependencyAnalysis, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

echo "📄 详细分析结果已保存到: user_recommendations_dependency_analysis.json\n";
echo "\n" . str_repeat("=", 60) . "\n";

if ($safeToRemove) {
    echo "🎯 结论: ✅ 可以安全删除文档中的 user_recommendations 表设计\n";
    echo "💡 推荐系统已使用更完善的架构实现，无需 user_recommendations 表\n";
} else {
    echo "🎯 结论: ⚠️  建议先修改代码依赖，然后再删除文档设计\n";
}
