{"analysis_time": "2025-08-04 22:47:33", "total_missing_fields": 15, "fields_with_alternatives": 12, "fields_without_alternatives": 3, "detailed_analysis": {"p_users": {"available_points": {"alternatives": [{"field": "points", "type": "decimal(10,2)", "comment": "积分余额", "match_level": "exact_function"}], "recommended": "points", "has_alternative": true}}, "p_points_transactions": {"type": {"alternatives": [{"field": "business_type", "type": "<PERSON><PERSON><PERSON>(50)", "comment": "业务类型", "match_level": "similar_function"}], "recommended": "business_type", "has_alternative": true}, "description": {"alternatives": [], "recommended": null, "has_alternative": false}}, "p_points_freeze": {"consumed_at": {"alternatives": [], "recommended": null, "has_alternative": false}, "returned_at": {"alternatives": [{"field": "released_at", "type": "timestamp", "comment": "释放时间", "match_level": "name_similar", "similarity": 63.63636363636363}, {"field": "created_at", "type": "timestamp", "comment": "", "match_level": "name_similar", "similarity": 76.19047619047619}], "recommended": "created_at", "has_alternative": true}, "consume_reason": {"alternatives": [], "recommended": null, "has_alternative": false}, "return_reason": {"alternatives": [{"field": "reason", "type": "text", "comment": "冻结原因", "match_level": "name_similar", "similarity": 63.1578947368421}], "recommended": "reason", "has_alternative": true}}, "p_style_library": {"config": {"alternatives": [{"field": "style_config", "type": "json", "comment": "风格配置参数", "match_level": "exact_function"}], "recommended": "style_config", "has_alternative": true}}, "p_character_library": {"characteristics": {"alternatives": [{"field": "is_featured", "type": "tinyint(1)", "comment": "是否推荐角色", "match_level": "similar_function"}], "recommended": "is_featured", "has_alternative": true}}, "p_resource_versions": {"version_name": {"alternatives": [{"field": "version_uuid", "type": "<PERSON><PERSON><PERSON>(36)", "comment": "版本唯一标识", "match_level": "similar_function"}, {"field": "version_number", "type": "<PERSON><PERSON><PERSON>(20)", "comment": "版本号（v1.0, v1.1, v1.2...）", "match_level": "similar_function"}, {"field": "version_type", "type": "enum('original','modified','template_derived')", "comment": "版本类型", "match_level": "similar_function"}, {"field": "original_filename", "type": "<PERSON><PERSON><PERSON>(255)", "comment": "原始文件名", "match_level": "similar_function"}], "recommended": "version_uuid", "has_alternative": true}, "description": {"alternatives": [{"field": "review_notes", "type": "text", "comment": "审核备注", "match_level": "similar_function"}], "recommended": "review_notes", "has_alternative": true}, "generation_config": {"alternatives": [{"field": "generation_cost", "type": "int(11)", "comment": "生成此版本消耗的积分", "match_level": "exact_function"}, {"field": "generation_params", "type": "json", "comment": "生成参数配置", "match_level": "exact_function"}], "recommended": "generation_cost", "has_alternative": true}}, "p_ai_generation_tasks": {"progress": {"alternatives": [{"field": "status", "type": "enum('pending','processing','completed','failed','cancelled','timeout')", "comment": "任务状态", "match_level": "similar_function"}], "recommended": "status", "has_alternative": true}, "result_data": {"alternatives": [{"field": "input_data", "type": "json", "comment": "输入数据", "match_level": "exact_function"}, {"field": "output_data", "type": "json", "comment": "输出数据", "match_level": "exact_function"}, {"field": "metadata", "type": "json", "comment": "元数据", "match_level": "exact_function"}], "recommended": "input_data", "has_alternative": true}}, "p_ai_model_configs": {"provider": {"alternatives": [{"field": "platform", "type": "<PERSON><PERSON><PERSON>(50)", "comment": "AI平台名称", "match_level": "exact_function"}], "recommended": "platform", "has_alternative": true}}}}