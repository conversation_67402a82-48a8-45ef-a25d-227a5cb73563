<?php
/**
 * 生成差异分析报告
 * 对比现有数据库表与文档规划表的差异，并提供整合调整方案
 */

// 加载数据
$existingTables = json_decode(file_get_contents('database_structure_export.json'), true);
$plannedTables = json_decode(file_get_contents('planned_tables_analysis.json'), true);
$codeAnalysis = json_decode(file_get_contents('controllers_services_analysis.json'), true);

$report = [
    'report_time' => date('Y-m-d H:i:s'),
    'summary' => [],
    'existing_tables_count' => count($existingTables['tables']),
    'planned_tables_count' => count($plannedTables['planned_tables']),
    'code_used_tables_count' => count($codeAnalysis['table_usage']),
    'differences' => [],
    'missing_in_reality' => [],
    'missing_in_planning' => [],
    'name_conflicts' => [],
    'field_differences' => [],
    'recommendations' => []
];

// 创建现有表的映射（去掉p_前缀进行比较）
$existingTablesMap = [];
foreach ($existingTables['tables'] as $tableInfo) {
    $tableName = $tableInfo['name'];
    $cleanName = preg_replace('/^p_/', '', $tableName);

    // 转换字段格式
    $columns = [];
    foreach ($tableInfo['fields'] as $field) {
        $columns[$field['name']] = [
            'type' => $field['type'],
            'comment' => $field['comment'] ?? ''
        ];
    }

    $existingTablesMap[$cleanName] = [
        'original_name' => $tableName,
        'info' => [
            'columns' => $columns,
            'comment' => $tableInfo['comment'] ?? ''
        ]
    ];
}

// 创建规划表的映射
$plannedTablesMap = [];
foreach ($plannedTables['planned_tables'] as $tableName => $tableInfo) {
    $cleanName = preg_replace('/^p_/', '', $tableName);
    $plannedTablesMap[$cleanName] = [
        'original_name' => $tableName,
        'info' => $tableInfo
    ];
}

// 创建代码使用表的映射
$codeTablesMap = [];
foreach ($codeAnalysis['table_usage'] as $tableName => $usage) {
    $cleanName = preg_replace('/^p_/', '', $tableName);
    $codeTablesMap[$cleanName] = [
        'original_name' => $tableName,
        'usage' => $usage
    ];
}

// 分析差异

// 1. 规划中有但现实中没有的表
foreach ($plannedTablesMap as $cleanName => $plannedInfo) {
    if (!isset($existingTablesMap[$cleanName])) {
        $report['missing_in_reality'][] = [
            'planned_name' => $plannedInfo['original_name'],
            'description' => $plannedInfo['info']['description'],
            'category' => $plannedInfo['info']['category'],
            'source' => $plannedInfo['info']['source'],
            'has_code_implementation' => isset($codeTablesMap[$cleanName])
        ];
    }
}

// 2. 现实中有但规划中没有的表
foreach ($existingTablesMap as $cleanName => $existingInfo) {
    if (!isset($plannedTablesMap[$cleanName])) {
        $report['missing_in_planning'][] = [
            'existing_name' => $existingInfo['original_name'],
            'field_count' => count($existingInfo['info']['columns']),
            'has_code_usage' => isset($codeTablesMap[$cleanName]),
            'code_usage' => $codeTablesMap[$cleanName]['usage'] ?? null
        ];
    }
}

// 3. 名称冲突和字段差异分析
foreach ($plannedTablesMap as $cleanName => $plannedInfo) {
    if (isset($existingTablesMap[$cleanName])) {
        $existing = $existingTablesMap[$cleanName];
        $planned = $plannedInfo;
        
        $difference = [
            'table_name' => $cleanName,
            'existing_full_name' => $existing['original_name'],
            'planned_full_name' => $planned['original_name'],
            'name_match' => $existing['original_name'] === $planned['original_name'],
            'existing_fields' => array_keys($existing['info']['columns']),
            'planned_fields' => array_keys($planned['info']['fields']),
            'field_differences' => [],
            'has_code_usage' => isset($codeTablesMap[$cleanName])
        ];
        
        // 分析字段差异
        $existingFields = array_keys($existing['info']['columns']);
        $plannedFields = array_keys($planned['info']['fields']);
        
        $difference['field_differences'] = [
            'missing_in_existing' => array_diff($plannedFields, $existingFields),
            'missing_in_planned' => array_diff($existingFields, $plannedFields),
            'common_fields' => array_intersect($existingFields, $plannedFields)
        ];
        
        $report['field_differences'][] = $difference;
    }
}

// 4. 生成推荐方案
$recommendations = [];

// 对于规划中有但现实中没有的表
if (!empty($report['missing_in_reality'])) {
    $recommendations[] = [
        'type' => 'create_missing_tables',
        'priority' => 'high',
        'description' => '创建规划中缺失的数据表',
        'action' => '需要创建 ' . count($report['missing_in_reality']) . ' 个数据表',
        'tables' => array_column($report['missing_in_reality'], 'planned_name'),
        'impact' => '这些表在文档中有明确规划，但数据库中不存在，可能影响相关功能的实现'
    ];
}

// 对于现实中有但规划中没有的表
if (!empty($report['missing_in_planning'])) {
    $recommendations[] = [
        'type' => 'update_documentation',
        'priority' => 'medium',
        'description' => '更新文档以包含现有数据表',
        'action' => '需要在文档中补充 ' . count($report['missing_in_planning']) . ' 个数据表的规划',
        'tables' => array_column($report['missing_in_planning'], 'existing_name'),
        'impact' => '这些表已经存在并被代码使用，但文档中没有规划，需要补充文档'
    ];
}

// 对于字段差异
$tablesWithFieldDifferences = array_filter($report['field_differences'], function($diff) {
    return !empty($diff['field_differences']['missing_in_existing']) || 
           !empty($diff['field_differences']['missing_in_planned']);
});

if (!empty($tablesWithFieldDifferences)) {
    $recommendations[] = [
        'type' => 'sync_table_fields',
        'priority' => 'high',
        'description' => '同步数据表字段',
        'action' => '需要同步 ' . count($tablesWithFieldDifferences) . ' 个数据表的字段',
        'tables' => array_column($tablesWithFieldDifferences, 'table_name'),
        'impact' => '字段不匹配可能导致代码运行错误或功能缺失'
    ];
}

$report['recommendations'] = $recommendations;

// 生成摘要
$report['summary'] = [
    'total_existing_tables' => $existingTables['total_tables'],
    'total_planned_tables' => count($plannedTables['planned_tables']),
    'total_code_used_tables' => count($codeAnalysis['table_usage']),
    'missing_in_reality_count' => count($report['missing_in_reality']),
    'missing_in_planning_count' => count($report['missing_in_planning']),
    'tables_with_field_differences' => count($tablesWithFieldDifferences),
    'total_recommendations' => count($recommendations),
    'critical_issues' => count($report['missing_in_reality']) + count($tablesWithFieldDifferences)
];

// 保存报告
file_put_contents('database_difference_report.json', json_encode($report, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

// 生成可读性报告
$readableReport = generateReadableReport($report);
file_put_contents('database_difference_report.md', $readableReport);

echo "差异分析报告生成完成！\n";
echo "现有数据表: " . $report['summary']['total_existing_tables'] . " 个\n";
echo "规划数据表: " . $report['summary']['total_planned_tables'] . " 个\n";
echo "代码使用表: " . $report['summary']['total_code_used_tables'] . " 个\n";
echo "规划中缺失: " . $report['summary']['missing_in_reality_count'] . " 个\n";
echo "现实中缺失: " . $report['summary']['missing_in_planning_count'] . " 个\n";
echo "字段差异表: " . $report['summary']['tables_with_field_differences'] . " 个\n";
echo "关键问题: " . $report['summary']['critical_issues'] . " 个\n";
echo "\n报告已保存到:\n";
echo "- database_difference_report.json (详细数据)\n";
echo "- database_difference_report.md (可读性报告)\n";

function generateReadableReport($report) {
    $md = "# 数据库表差异分析报告\n\n";
    $md .= "**生成时间**: " . $report['report_time'] . "\n\n";

    // 摘要
    $md .= "## 📊 分析摘要\n\n";
    $md .= "| 项目 | 数量 |\n";
    $md .= "|------|------|\n";
    $md .= "| 现有数据表 | " . $report['summary']['total_existing_tables'] . " |\n";
    $md .= "| 规划数据表 | " . $report['summary']['total_planned_tables'] . " |\n";
    $md .= "| 代码使用表 | " . $report['summary']['total_code_used_tables'] . " |\n";
    $md .= "| 规划中缺失 | " . $report['summary']['missing_in_reality_count'] . " |\n";
    $md .= "| 文档中缺失 | " . $report['summary']['missing_in_planning_count'] . " |\n";
    $md .= "| 字段差异表 | " . $report['summary']['tables_with_field_differences'] . " |\n";
    $md .= "| **关键问题** | **" . $report['summary']['critical_issues'] . "** |\n\n";

    // 规划中有但现实中没有的表
    if (!empty($report['missing_in_reality'])) {
        $md .= "## ⚠️ 规划中有但数据库中缺失的表\n\n";
        $md .= "以下表在文档中有明确规划，但数据库中不存在：\n\n";
        foreach ($report['missing_in_reality'] as $table) {
            $md .= "### `" . $table['planned_name'] . "`\n";
            $md .= "- **描述**: " . $table['description'] . "\n";
            $md .= "- **分类**: " . $table['category'] . "\n";
            $md .= "- **来源文档**: " . $table['source'] . "\n";
            $md .= "- **代码实现**: " . ($table['has_code_implementation'] ? '✅ 已有代码使用' : '❌ 无代码使用') . "\n\n";
        }
    }

    // 现实中有但规划中没有的表
    if (!empty($report['missing_in_planning'])) {
        $md .= "## 📝 现有数据表但文档中缺失规划\n\n";
        $md .= "以下表已存在于数据库中，但文档中没有相应规划：\n\n";
        foreach ($report['missing_in_planning'] as $table) {
            $md .= "### `" . $table['existing_name'] . "`\n";
            $md .= "- **字段数量**: " . $table['field_count'] . "\n";
            $md .= "- **代码使用**: " . ($table['has_code_usage'] ? '✅ 被代码使用' : '❌ 无代码使用') . "\n";
            if ($table['has_code_usage'] && $table['code_usage']) {
                $usage = $table['code_usage'];
                if (!empty($usage['used_by_models'])) {
                    $md .= "  - **模型**: " . implode(', ', $usage['used_by_models']) . "\n";
                }
                if (!empty($usage['used_by_controllers'])) {
                    $md .= "  - **控制器**: " . implode(', ', array_unique($usage['used_by_controllers'])) . "\n";
                }
                if (!empty($usage['used_by_services'])) {
                    $md .= "  - **服务**: " . implode(', ', array_unique($usage['used_by_services'])) . "\n";
                }
            }
            $md .= "\n";
        }
    }

    // 字段差异
    if (!empty($report['field_differences'])) {
        $md .= "## 🔄 字段差异分析\n\n";
        foreach ($report['field_differences'] as $diff) {
            if (!empty($diff['field_differences']['missing_in_existing']) ||
                !empty($diff['field_differences']['missing_in_planned'])) {

                $md .= "### `" . $diff['table_name'] . "`\n";
                $md .= "- **现有表名**: `" . $diff['existing_full_name'] . "`\n";
                $md .= "- **规划表名**: `" . $diff['planned_full_name'] . "`\n";
                $md .= "- **名称匹配**: " . ($diff['name_match'] ? '✅' : '❌') . "\n";
                $md .= "- **代码使用**: " . ($diff['has_code_usage'] ? '✅' : '❌') . "\n";

                if (!empty($diff['field_differences']['missing_in_existing'])) {
                    $md .= "- **现有表中缺失字段**: " . implode(', ', $diff['field_differences']['missing_in_existing']) . "\n";
                }
                if (!empty($diff['field_differences']['missing_in_planned'])) {
                    $md .= "- **规划中缺失字段**: " . implode(', ', $diff['field_differences']['missing_in_planned']) . "\n";
                }
                if (!empty($diff['field_differences']['common_fields'])) {
                    $md .= "- **共同字段**: " . implode(', ', $diff['field_differences']['common_fields']) . "\n";
                }
                $md .= "\n";
            }
        }
    }

    // 推荐方案
    if (!empty($report['recommendations'])) {
        $md .= "## 🎯 整合调整方案\n\n";
        foreach ($report['recommendations'] as $i => $rec) {
            $priority = $rec['priority'] === 'high' ? '🔴 高优先级' : '🟡 中优先级';
            $md .= "### " . ($i + 1) . ". " . $rec['description'] . " " . $priority . "\n\n";
            $md .= "**操作**: " . $rec['action'] . "\n\n";
            $md .= "**影响**: " . $rec['impact'] . "\n\n";
            if (!empty($rec['tables'])) {
                $md .= "**涉及表**: " . implode(', ', $rec['tables']) . "\n\n";
            }
        }
    }

    $md .= "## 📋 总结\n\n";
    $md .= "本次分析发现了 **" . $report['summary']['critical_issues'] . "** 个关键问题需要处理：\n\n";
    $md .= "1. **数据表创建**: 需要创建 " . $report['summary']['missing_in_reality_count'] . " 个规划中的数据表\n";
    $md .= "2. **文档更新**: 需要在文档中补充 " . $report['summary']['missing_in_planning_count'] . " 个现有数据表的规划\n";
    $md .= "3. **字段同步**: 需要同步 " . $report['summary']['tables_with_field_differences'] . " 个数据表的字段差异\n\n";
    $md .= "建议按照推荐方案的优先级顺序进行整合调整，确保数据库结构与文档规划保持一致。\n";

    return $md;
}
