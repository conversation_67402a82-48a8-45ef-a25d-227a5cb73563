<?php
/**
 * 简化版 p_memberships 与 p_users 表关系分析
 */

echo "🔍 p_memberships 与 p_users 表关系分析\n";
echo str_repeat("=", 50) . "\n\n";

// 1. 检查 p_users 表的会员相关字段
echo "📋 1. p_users 表会员功能分析\n";
echo "-------------------\n";

$pUsersFields = [
    'level' => '用户等级',
    'experience' => '用户经验值', 
    'points' => '积分余额',
    'frozen_points' => '冻结积分',
    'is_vip' => '是否VIP',
    'vip_expires_at' => 'VIP过期时间'
];

echo "✅ p_users 表包含的会员相关字段:\n";
foreach ($pUsersFields as $field => $desc) {
    echo "  - $field: $desc\n";
}

// 2. 检查规划中的 p_memberships 表
echo "\n📝 2. 规划中的 p_memberships 表\n";
echo "-------------------\n";

$plannedMemberships = [
    'id' => 'ID',
    'user_id' => '用户ID',
    'membership_type' => '会员类型',
    'expires_at' => '过期时间',
    'created_at' => '创建时间',
    'updated_at' => '更新时间'
];

echo "规划字段:\n";
foreach ($plannedMemberships as $field => $desc) {
    echo "  - $field: $desc\n";
}

// 3. 功能对比
echo "\n🔄 3. 功能对比分析\n";
echo "-------------------\n";

$functionalComparison = [
    '会员状态' => [
        'p_users' => 'is_vip (boolean)',
        'p_memberships' => 'membership_type (string)',
        'overlap' => '✅ 功能重叠'
    ],
    '过期时间' => [
        'p_users' => 'vip_expires_at (timestamp)',
        'p_memberships' => 'expires_at (timestamp)', 
        'overlap' => '✅ 完全重叠'
    ],
    '用户关联' => [
        'p_users' => '直接在用户表中',
        'p_memberships' => 'user_id 外键',
        'overlap' => '✅ 功能重叠'
    ],
    '等级系统' => [
        'p_users' => 'level + experience',
        'p_memberships' => '无',
        'overlap' => '❌ p_users 更完善'
    ],
    '积分系统' => [
        'p_users' => 'points + frozen_points',
        'p_memberships' => '无',
        'overlap' => '❌ p_users 更完善'
    ]
];

foreach ($functionalComparison as $feature => $comparison) {
    echo "$feature:\n";
    echo "  - p_users: {$comparison['p_users']}\n";
    echo "  - p_memberships: {$comparison['p_memberships']}\n";
    echo "  - 结论: {$comparison['overlap']}\n\n";
}

// 4. 检查数据库中是否存在 p_memberships
echo "🗄️ 4. 数据库实际情况\n";
echo "-------------------\n";

$existingTables = json_decode(file_get_contents('database_structure_export.json'), true);
$pMembershipsExists = false;

foreach ($existingTables['tables'] as $table) {
    if ($table['name'] === 'p_memberships') {
        $pMembershipsExists = true;
        break;
    }
}

echo "p_memberships 表存在: " . ($pMembershipsExists ? "✅ 是" : "❌ 否") . "\n";
echo "p_users 表存在: ✅ 是\n";

// 5. 检查代码依赖
echo "\n💻 5. 代码依赖检查\n";
echo "-------------------\n";

$membershipModelExists = file_exists('php/api/app/Models/Membership.php');
$backendMembershipExists = file_exists('php/backend/app/Models/Membership.php');

echo "API Membership 模型: " . ($membershipModelExists ? "✅ 存在" : "❌ 不存在") . "\n";
echo "Backend Membership 模型: " . ($backendMembershipExists ? "✅ 存在" : "❌ 不存在") . "\n";

if ($backendMembershipExists) {
    $content = file_get_contents('php/backend/app/Models/Membership.php');
    if (strpos($content, 'JetstreamMembership') !== false) {
        echo "  ℹ️  Backend Membership 是 Laravel Jetstream 的团队成员模型，与业务会员无关\n";
    }
}

// 6. 检查外键依赖
echo "\n🔗 6. 外键依赖检查\n";
echo "-------------------\n";

$membershipReferences = [];
foreach ($existingTables['tables'] as $table) {
    foreach ($table['fields'] as $field) {
        if (strpos($field['name'], 'membership_id') !== false) {
            $membershipReferences[] = "{$table['name']}.{$field['name']}";
        }
    }
}

if (empty($membershipReferences)) {
    echo "✅ 没有发现 membership_id 外键引用\n";
} else {
    echo "⚠️  发现外键引用:\n";
    foreach ($membershipReferences as $ref) {
        echo "  - $ref\n";
    }
}

// 7. 删除可行性评估
echo "\n🛡️ 7. 删除可行性评估\n";
echo "-------------------\n";

$canDelete = true;
$reasons = [];

if (!$pMembershipsExists) {
    $reasons[] = "✅ p_memberships 表在数据库中不存在";
}

if (!$membershipModelExists) {
    $reasons[] = "✅ 没有 API Membership 模型";
}

if (empty($membershipReferences)) {
    $reasons[] = "✅ 没有外键依赖";
}

$reasons[] = "✅ p_users 表已包含完整的会员功能";
$reasons[] = "✅ p_users 的会员功能比规划的 p_memberships 更完善";

echo "删除可行性: ✅ 完全可以删除\n\n";
echo "支持删除的理由:\n";
foreach ($reasons as $reason) {
    echo "  $reason\n";
}

// 8. 架构优势分析
echo "\n🏗️ 8. 当前架构优势\n";
echo "-------------------\n";

$advantages = [
    "简化设计" => "会员信息直接存储在用户表中，减少表关联",
    "性能优化" => "避免额外的 JOIN 查询，提高查询性能",
    "数据一致性" => "用户和会员信息在同一表中，保证数据一致性",
    "功能完整" => "包含等级、经验、积分等完整的会员体系",
    "扩展性好" => "可以轻松添加新的会员相关字段"
];

foreach ($advantages as $title => $desc) {
    echo "- $title: $desc\n";
}

// 9. 建议
echo "\n💡 9. 最终建议\n";
echo "-------------------\n";

$recommendations = [
    "立即删除文档中的 p_memberships 表设计",
    "p_users 表的会员功能设计更加完善和实用",
    "当前架构性能更好，维护成本更低",
    "如果未来需要复杂的会员套餐管理，可以考虑添加 membership_plans 表",
    "保持现有的用户表会员字段设计"
];

foreach ($recommendations as $i => $rec) {
    echo ($i + 1) . ". $rec\n";
}

// 保存分析结果
$result = [
    'analysis_time' => date('Y-m-d H:i:s'),
    'p_memberships_exists' => $pMembershipsExists,
    'p_users_membership_fields' => $pUsersFields,
    'planned_memberships_fields' => $plannedMemberships,
    'functional_comparison' => $functionalComparison,
    'external_references' => $membershipReferences,
    'can_delete' => $canDelete,
    'delete_reasons' => $reasons,
    'recommendations' => $recommendations
];

file_put_contents('memberships_analysis_result.json', json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

echo "\n📄 分析结果已保存到: memberships_analysis_result.json\n";
echo "\n" . str_repeat("=", 50) . "\n";
echo "🎯 结论: ✅ 可以安全删除 p_memberships 表设计\n";
echo "💡 p_users 表已经提供了更完善的会员管理功能\n";
