<?php
/**
 * 分析 p_memberships 和 p_users 表的关系
 * 检查功能重叠和是否可以删除 p_memberships 表
 */

echo "🔍 分析 p_memberships 与 p_users 表关系\n";
echo str_repeat("=", 60) . "\n\n";

// 1. 加载现有数据库结构
$existingTables = json_decode(file_get_contents('database_structure_export.json'), true);
$plannedTables = json_decode(file_get_contents('planned_tables_analysis.json'), true);

// 2. 查找相关表
$pUsersTable = null;
$pMembershipsTable = null;
$relatedTables = [];

foreach ($existingTables['tables'] as $table) {
    $tableName = $table['name'];
    
    if ($tableName === 'p_users') {
        $pUsersTable = $table;
    } elseif ($tableName === 'p_memberships') {
        $pMembershipsTable = $table;
    } elseif (strpos($tableName, 'member') !== false || 
              strpos($tableName, 'subscription') !== false ||
              strpos($tableName, 'vip') !== false ||
              strpos($tableName, 'premium') !== false) {
        $relatedTables[] = $table;
    }
}

echo "📋 1. 表结构对比分析\n";
echo "-------------------\n";

// 分析 p_users 表
if ($pUsersTable) {
    echo "✅ p_users 表存在\n";
    echo "字段数量: " . count($pUsersTable['fields']) . "\n";
    
    $membershipRelatedFields = [];
    foreach ($pUsersTable['fields'] as $field) {
        $fieldName = strtolower($field['name']);
        if (strpos($fieldName, 'member') !== false ||
            strpos($fieldName, 'vip') !== false ||
            strpos($fieldName, 'premium') !== false ||
            strpos($fieldName, 'subscription') !== false ||
            strpos($fieldName, 'level') !== false ||
            strpos($fieldName, 'tier') !== false ||
            strpos($fieldName, 'expire') !== false) {
            $membershipRelatedFields[] = $field;
        }
    }
    
    echo "会员相关字段: " . count($membershipRelatedFields) . " 个\n";
    foreach ($membershipRelatedFields as $field) {
        echo "  - {$field['name']}: {$field['comment']}\n";
    }
} else {
    echo "❌ p_users 表不存在\n";
}

echo "\n";

// 分析 p_memberships 表
if ($pMembershipsTable) {
    echo "✅ p_memberships 表存在\n";
    echo "字段数量: " . count($pMembershipsTable['fields']) . "\n";
    echo "表字段:\n";
    foreach ($pMembershipsTable['fields'] as $field) {
        echo "  - {$field['name']}: {$field['comment']}\n";
    }
} else {
    echo "❌ p_memberships 表不存在\n";
    
    // 检查规划中的 p_memberships
    if (isset($plannedTables['planned_tables']['p_memberships'])) {
        echo "📝 规划中的 p_memberships 表:\n";
        $plannedMemberships = $plannedTables['planned_tables']['p_memberships'];
        echo "描述: {$plannedMemberships['description']}\n";
        echo "规划字段:\n";
        foreach ($plannedMemberships['fields'] as $field => $desc) {
            echo "  - $field: $desc\n";
        }
    }
}

echo "\n相关表:\n";
foreach ($relatedTables as $table) {
    echo "- {$table['name']}\n";
}

// 3. 代码使用分析
echo "\n💻 2. 代码使用分析\n";
echo "-------------------\n";

// 检查是否有 Membership 模型
$membershipModelFile = 'php/api/app/Models/Membership.php';
$userModelFile = 'php/api/app/Models/User.php';

$codeUsage = [
    'membership_model_exists' => file_exists($membershipModelFile),
    'user_model_exists' => file_exists($userModelFile),
    'membership_controllers' => [],
    'membership_services' => [],
    'membership_references' => []
];

// 检查控制器中的会员相关代码
$controllerDir = 'php/api/app/Http/Controllers/PyApi';
if (is_dir($controllerDir)) {
    $controllers = glob($controllerDir . '/*.php');
    foreach ($controllers as $controller) {
        $content = file_get_contents($controller);
        $fileName = basename($controller);
        
        if (preg_match_all('/membership|member|vip|premium|subscription/i', $content, $matches)) {
            $codeUsage['membership_references'][] = [
                'file' => $fileName,
                'matches' => count($matches[0])
            ];
        }
    }
}

// 检查服务层中的会员相关代码
$serviceDir = 'php/api/app/Services/PyApi';
if (is_dir($serviceDir)) {
    $services = glob($serviceDir . '/*.php');
    foreach ($services as $service) {
        $content = file_get_contents($service);
        $fileName = basename($service);
        
        if (preg_match_all('/membership|member|vip|premium|subscription/i', $content, $matches)) {
            $codeUsage['membership_references'][] = [
                'file' => $fileName,
                'matches' => count($matches[0])
            ];
        }
    }
}

echo "Membership 模型: " . ($codeUsage['membership_model_exists'] ? "✅ 存在" : "❌ 不存在") . "\n";
echo "User 模型: " . ($codeUsage['user_model_exists'] ? "✅ 存在" : "❌ 不存在") . "\n";

if (!empty($codeUsage['membership_references'])) {
    echo "\n会员相关代码引用:\n";
    foreach ($codeUsage['membership_references'] as $ref) {
        echo "  - {$ref['file']}: {$ref['matches']} 处引用\n";
    }
} else {
    echo "\n❌ 未发现会员相关代码引用\n";
}

// 4. 功能重叠分析
echo "\n🔄 3. 功能重叠分析\n";
echo "-------------------\n";

$overlapAnalysis = [
    'user_has_membership_fields' => false,
    'overlapping_fields' => [],
    'unique_membership_fields' => [],
    'membership_functionality_in_user' => []
];

if ($pUsersTable && isset($membershipRelatedFields)) {
    $overlapAnalysis['user_has_membership_fields'] = !empty($membershipRelatedFields);
    
    if (!empty($membershipRelatedFields)) {
        echo "✅ p_users 表包含会员相关字段:\n";
        foreach ($membershipRelatedFields as $field) {
            echo "  - {$field['name']}: {$field['comment']}\n";
            
            // 分析字段功能
            $fieldName = strtolower($field['name']);
            if (strpos($fieldName, 'level') !== false) {
                $overlapAnalysis['membership_functionality_in_user'][] = '会员等级';
            }
            if (strpos($fieldName, 'vip') !== false) {
                $overlapAnalysis['membership_functionality_in_user'][] = 'VIP状态';
            }
            if (strpos($fieldName, 'expire') !== false) {
                $overlapAnalysis['membership_functionality_in_user'][] = '过期时间';
            }
        }
    }
}

// 5. 业务流程分析
echo "\n📊 4. 业务流程分析\n";
echo "-------------------\n";

// 检查是否有会员相关的业务流程
$businessProcesses = [
    'payment_related' => [],
    'subscription_related' => [],
    'privilege_related' => [],
    'upgrade_related' => []
];

// 检查支付相关表
foreach ($existingTables['tables'] as $table) {
    $tableName = strtolower($table['name']);
    if (strpos($tableName, 'payment') !== false ||
        strpos($tableName, 'order') !== false ||
        strpos($tableName, 'transaction') !== false) {
        $businessProcesses['payment_related'][] = $table['name'];
    }
    if (strpos($tableName, 'subscription') !== false) {
        $businessProcesses['subscription_related'][] = $table['name'];
    }
    if (strpos($tableName, 'privilege') !== false ||
        strpos($tableName, 'permission') !== false) {
        $businessProcesses['privilege_related'][] = $table['name'];
    }
}

echo "支付相关表: " . count($businessProcesses['payment_related']) . " 个\n";
foreach ($businessProcesses['payment_related'] as $table) {
    echo "  - $table\n";
}

echo "订阅相关表: " . count($businessProcesses['subscription_related']) . " 个\n";
foreach ($businessProcesses['subscription_related'] as $table) {
    echo "  - $table\n";
}

echo "权限相关表: " . count($businessProcesses['privilege_related']) . " 个\n";
foreach ($businessProcesses['privilege_related'] as $table) {
    echo "  - $table\n";
}

// 6. 删除可行性评估
echo "\n🛡️ 5. 删除可行性评估\n";
echo "-------------------\n";

$canDelete = true;
$deleteReasons = [];
$keepReasons = [];

// 检查是否存在表
if (!$pMembershipsTable) {
    $deleteReasons[] = "p_memberships 表在数据库中不存在";
}

// 检查代码依赖
if (!$codeUsage['membership_model_exists']) {
    $deleteReasons[] = "没有 Membership 模型";
}

if (empty($codeUsage['membership_references'])) {
    $deleteReasons[] = "代码中没有会员相关引用";
}

// 检查功能重叠
if ($overlapAnalysis['user_has_membership_fields']) {
    $deleteReasons[] = "p_users 表已包含会员相关功能";
}

// 检查业务流程依赖
if (empty($businessProcesses['subscription_related']) && 
    empty($businessProcesses['payment_related'])) {
    $deleteReasons[] = "没有复杂的会员业务流程依赖";
}

// 如果表存在但有外键依赖
if ($pMembershipsTable) {
    // 检查外键依赖
    $hasReferences = false;
    foreach ($existingTables['tables'] as $table) {
        foreach ($table['fields'] as $field) {
            if (strpos($field['name'], 'membership_id') !== false) {
                $hasReferences = true;
                $keepReasons[] = "存在外键依赖: {$table['name']}.{$field['name']}";
            }
        }
    }
    
    if ($hasReferences) {
        $canDelete = false;
    }
}

echo "删除可行性: " . ($canDelete ? "✅ 可以删除" : "❌ 不建议删除") . "\n\n";

echo "支持删除的理由:\n";
foreach ($deleteReasons as $reason) {
    echo "  ✅ $reason\n";
}

if (!empty($keepReasons)) {
    echo "\n保留的理由:\n";
    foreach ($keepReasons as $reason) {
        echo "  ❌ $reason\n";
    }
}

// 7. 建议方案
echo "\n💡 6. 建议方案\n";
echo "-------------------\n";

if ($canDelete) {
    $recommendations = [
        "可以安全删除文档中的 p_memberships 表设计",
        "p_users 表已经包含了会员管理的核心功能",
        "当前架构使用用户表的字段来管理会员状态，更简洁高效",
        "如果未来需要复杂的会员管理，可以重新设计专门的会员表"
    ];
} else {
    $recommendations = [
        "暂时保留 p_memberships 表设计",
        "先处理外键依赖关系",
        "评估是否可以将会员功能合并到 p_users 表",
        "制定数据迁移计划"
    ];
}

foreach ($recommendations as $i => $rec) {
    echo ($i + 1) . ". $rec\n";
}

// 保存分析结果
$analysisResult = [
    'analysis_time' => date('Y-m-d H:i:s'),
    'tables_found' => [
        'p_users' => $pUsersTable !== null,
        'p_memberships' => $pMembershipsTable !== null
    ],
    'overlap_analysis' => $overlapAnalysis,
    'code_usage' => $codeUsage,
    'business_processes' => $businessProcesses,
    'can_delete' => $canDelete,
    'delete_reasons' => $deleteReasons,
    'keep_reasons' => $keepReasons,
    'recommendations' => $recommendations
];

file_put_contents('memberships_vs_users_analysis.json', json_encode($analysisResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

echo "\n📄 详细分析结果已保存到: memberships_vs_users_analysis.json\n";
echo "\n" . str_repeat("=", 60) . "\n";

if ($canDelete) {
    echo "🎯 结论: ✅ 可以安全删除 p_memberships 表设计\n";
    echo "💡 p_users 表已经包含了会员管理功能，无需单独的会员表\n";
} else {
    echo "🎯 结论: ⚠️  建议先处理依赖关系，然后考虑删除\n";
}
