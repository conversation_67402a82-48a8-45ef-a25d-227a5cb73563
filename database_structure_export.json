{"export_time": "2025-08-04 21:31:22", "database": "ai_tool", "total_tables": 31, "tables": [{"name": "p_achievements", "comment": "", "fields": [{"name": "id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "", "extra": "auto_increment", "key": "PRI"}, {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "nullable": false, "default": null, "comment": "成就名称", "extra": "", "key": ""}, {"name": "description", "type": "text", "nullable": false, "default": null, "comment": "成就描述", "extra": "", "key": ""}, {"name": "type", "type": "enum('daily','weekly','monthly','milestone','special','social','creative','learning')", "nullable": false, "default": null, "comment": "成就类型", "extra": "", "key": "MUL"}, {"name": "status", "type": "enum('active','inactive','archived')", "nullable": false, "default": "active", "comment": "状态", "extra": "", "key": ""}, {"name": "difficulty", "type": "enum('easy','medium','hard','legendary')", "nullable": false, "default": "easy", "comment": "难度", "extra": "", "key": "MUL"}, {"name": "icon", "type": "<PERSON><PERSON><PERSON>(255)", "nullable": true, "default": null, "comment": "成就图标", "extra": "", "key": ""}, {"name": "badge", "type": "<PERSON><PERSON><PERSON>(255)", "nullable": true, "default": null, "comment": "成就徽章", "extra": "", "key": ""}, {"name": "reward_experience", "type": "int(11)", "nullable": false, "default": "0", "comment": "奖励经验", "extra": "", "key": ""}, {"name": "reward_points", "type": "int(11)", "nullable": false, "default": "0", "comment": "奖励积分", "extra": "", "key": ""}, {"name": "reward_items", "type": "json", "nullable": true, "default": null, "comment": "奖励物品", "extra": "", "key": ""}, {"name": "conditions", "type": "json", "nullable": false, "default": null, "comment": "完成条件", "extra": "", "key": ""}, {"name": "max_progress", "type": "int(11)", "nullable": false, "default": "1", "comment": "最大进度值", "extra": "", "key": ""}, {"name": "is_hidden", "type": "tinyint(1)", "nullable": false, "default": "0", "comment": "是否隐藏", "extra": "", "key": "MUL"}, {"name": "is_repeatable", "type": "tinyint(1)", "nullable": false, "default": "0", "comment": "是否可重复", "extra": "", "key": ""}, {"name": "sort_order", "type": "int(11)", "nullable": false, "default": "0", "comment": "排序", "extra": "", "key": "MUL"}, {"name": "start_time", "type": "timestamp", "nullable": true, "default": null, "comment": "开始时间", "extra": "", "key": ""}, {"name": "end_time", "type": "timestamp", "nullable": true, "default": null, "comment": "结束时间", "extra": "", "key": ""}, {"name": "metadata", "type": "json", "nullable": true, "default": null, "comment": "元数据", "extra": "", "key": ""}, {"name": "created_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}, {"name": "updated_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}], "indexes": [{"name": "PRIMARY", "unique": true, "type": "BTREE", "columns": ["id"]}, {"name": "achievements_type_status_index", "unique": false, "type": "BTREE", "columns": ["type", "status"]}, {"name": "achievements_difficulty_status_index", "unique": false, "type": "BTREE", "columns": ["difficulty", "status"]}, {"name": "achievements_is_hidden_status_index", "unique": false, "type": "BTREE", "columns": ["is_hidden", "status"]}, {"name": "achievements_sort_order_index", "unique": false, "type": "BTREE", "columns": ["sort_order"]}], "foreign_keys": [], "row_count": 0, "engine": "InnoDB", "charset": "utf8mb4_unicode_ci", "created_at": "2025-07-27 18:08:10"}, {"name": "p_ai_generation_tasks", "comment": "", "fields": [{"name": "id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "", "extra": "auto_increment", "key": "PRI"}, {"name": "user_id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "用户ID", "extra": "", "key": "MUL"}, {"name": "project_id", "type": "bigint(20) unsigned", "nullable": true, "default": null, "comment": "关联项目ID", "extra": "", "key": "MUL"}, {"name": "model_config_id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "AI模型配置ID", "extra": "", "key": "MUL"}, {"name": "task_type", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": false, "default": null, "comment": "任务类型", "extra": "", "key": "MUL"}, {"name": "platform", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": false, "default": null, "comment": "AI平台", "extra": "", "key": "MUL"}, {"name": "model_name", "type": "<PERSON><PERSON><PERSON>(100)", "nullable": false, "default": null, "comment": "使用的模型名称", "extra": "", "key": ""}, {"name": "status", "type": "enum('pending','processing','completed','failed','cancelled','timeout')", "nullable": false, "default": "pending", "comment": "任务状态", "extra": "", "key": "MUL"}, {"name": "input_data", "type": "json", "nullable": false, "default": null, "comment": "输入数据", "extra": "", "key": ""}, {"name": "output_data", "type": "json", "nullable": true, "default": null, "comment": "输出数据", "extra": "", "key": ""}, {"name": "generation_params", "type": "json", "nullable": true, "default": null, "comment": "生成参数", "extra": "", "key": ""}, {"name": "external_task_id", "type": "<PERSON><PERSON><PERSON>(255)", "nullable": true, "default": null, "comment": "外部任务ID", "extra": "", "key": "MUL"}, {"name": "cost", "type": "decimal(8,4)", "nullable": false, "default": "0.0000", "comment": "任务成本", "extra": "", "key": ""}, {"name": "tokens_used", "type": "int(11)", "nullable": true, "default": null, "comment": "使用的token数", "extra": "", "key": ""}, {"name": "processing_time_ms", "type": "int(11)", "nullable": true, "default": null, "comment": "处理时间(毫秒)", "extra": "", "key": ""}, {"name": "started_at", "type": "timestamp", "nullable": true, "default": null, "comment": "开始时间", "extra": "", "key": ""}, {"name": "completed_at", "type": "timestamp", "nullable": true, "default": null, "comment": "完成时间", "extra": "", "key": ""}, {"name": "error_message", "type": "text", "nullable": true, "default": null, "comment": "错误信息", "extra": "", "key": ""}, {"name": "metadata", "type": "json", "nullable": true, "default": null, "comment": "元数据", "extra": "", "key": ""}, {"name": "retry_count", "type": "int(11)", "nullable": false, "default": "0", "comment": "重试次数", "extra": "", "key": ""}, {"name": "max_retries", "type": "int(11)", "nullable": false, "default": "3", "comment": "最大重试次数", "extra": "", "key": ""}, {"name": "created_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": "MUL"}, {"name": "updated_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}], "indexes": [{"name": "PRIMARY", "unique": true, "type": "BTREE", "columns": ["id"]}, {"name": "ai_generation_tasks_user_id_index", "unique": false, "type": "BTREE", "columns": ["user_id"]}, {"name": "ai_generation_tasks_project_id_index", "unique": false, "type": "BTREE", "columns": ["project_id"]}, {"name": "ai_generation_tasks_model_config_id_index", "unique": false, "type": "BTREE", "columns": ["model_config_id"]}, {"name": "ai_generation_tasks_task_type_index", "unique": false, "type": "BTREE", "columns": ["task_type"]}, {"name": "ai_generation_tasks_platform_index", "unique": false, "type": "BTREE", "columns": ["platform"]}, {"name": "ai_generation_tasks_status_index", "unique": false, "type": "BTREE", "columns": ["status"]}, {"name": "ai_generation_tasks_external_task_id_index", "unique": false, "type": "BTREE", "columns": ["external_task_id"]}, {"name": "ai_generation_tasks_created_at_index", "unique": false, "type": "BTREE", "columns": ["created_at"]}, {"name": "ai_generation_tasks_user_id_status_index", "unique": false, "type": "BTREE", "columns": ["user_id", "status"]}, {"name": "ai_generation_tasks_task_type_status_index", "unique": false, "type": "BTREE", "columns": ["task_type", "status"]}, {"name": "ai_generation_tasks_platform_status_index", "unique": false, "type": "BTREE", "columns": ["platform", "status"]}, {"name": "ai_generation_tasks_status_created_at_index", "unique": false, "type": "BTREE", "columns": ["status", "created_at"]}], "foreign_keys": [{"constraint_name": "ai_generation_tasks_model_config_id_foreign", "column": "model_config_id", "referenced_table": "p_ai_model_configs", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}, {"constraint_name": "ai_generation_tasks_project_id_foreign", "column": "project_id", "referenced_table": "p_projects", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "SET NULL"}, {"constraint_name": "ai_generation_tasks_user_id_foreign", "column": "user_id", "referenced_table": "p_users", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}], "row_count": 0, "engine": "InnoDB", "charset": "utf8mb4_unicode_ci", "created_at": "2025-07-27 18:05:25"}, {"name": "p_ai_model_configs", "comment": "", "fields": [{"name": "id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "", "extra": "auto_increment", "key": "PRI"}, {"name": "platform", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": false, "default": null, "comment": "AI平台名称", "extra": "", "key": "MUL"}, {"name": "model_name", "type": "<PERSON><PERSON><PERSON>(100)", "nullable": false, "default": null, "comment": "模型名称", "extra": "", "key": ""}, {"name": "model_type", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": false, "default": null, "comment": "模型类型", "extra": "", "key": "MUL"}, {"name": "api_endpoint", "type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "comment": "API端点", "extra": "", "key": ""}, {"name": "config_params", "type": "json", "nullable": true, "default": null, "comment": "配置参数", "extra": "", "key": ""}, {"name": "capabilities", "type": "json", "nullable": true, "default": null, "comment": "模型能力", "extra": "", "key": ""}, {"name": "is_active", "type": "tinyint(1)", "nullable": false, "default": "1", "comment": "是否启用", "extra": "", "key": "MUL"}, {"name": "is_default", "type": "tinyint(1)", "nullable": false, "default": "0", "comment": "是否默认模型", "extra": "", "key": "MUL"}, {"name": "priority", "type": "int(11)", "nullable": false, "default": "0", "comment": "优先级", "extra": "", "key": "MUL"}, {"name": "cost_per_request", "type": "decimal(8,4)", "nullable": false, "default": "0.0000", "comment": "每次请求成本", "extra": "", "key": ""}, {"name": "max_tokens", "type": "int(11)", "nullable": true, "default": null, "comment": "最大token数", "extra": "", "key": ""}, {"name": "timeout_seconds", "type": "int(11)", "nullable": false, "default": "30", "comment": "超时时间(秒)", "extra": "", "key": ""}, {"name": "rate_limits", "type": "json", "nullable": true, "default": null, "comment": "速率限制配置", "extra": "", "key": ""}, {"name": "performance_metrics", "type": "json", "nullable": true, "default": null, "comment": "性能指标", "extra": "", "key": ""}, {"name": "last_health_check", "type": "timestamp", "nullable": true, "default": null, "comment": "最后健康检查时间", "extra": "", "key": ""}, {"name": "health_status", "type": "enum('healthy','degraded','unhealthy','unknown')", "nullable": false, "default": "unknown", "comment": "健康状态", "extra": "", "key": "MUL"}, {"name": "health_message", "type": "text", "nullable": true, "default": null, "comment": "健康状态消息", "extra": "", "key": ""}, {"name": "created_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}, {"name": "updated_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}], "indexes": [{"name": "PRIMARY", "unique": true, "type": "BTREE", "columns": ["id"]}, {"name": "ai_model_configs_platform_model_name_unique", "unique": true, "type": "BTREE", "columns": ["platform", "model_name"]}, {"name": "ai_model_configs_platform_index", "unique": false, "type": "BTREE", "columns": ["platform"]}, {"name": "ai_model_configs_model_type_index", "unique": false, "type": "BTREE", "columns": ["model_type"]}, {"name": "ai_model_configs_is_active_index", "unique": false, "type": "BTREE", "columns": ["is_active"]}, {"name": "ai_model_configs_is_default_index", "unique": false, "type": "BTREE", "columns": ["is_default"]}, {"name": "ai_model_configs_priority_index", "unique": false, "type": "BTREE", "columns": ["priority"]}, {"name": "ai_model_configs_health_status_index", "unique": false, "type": "BTREE", "columns": ["health_status"]}, {"name": "ai_model_configs_platform_model_type_index", "unique": false, "type": "BTREE", "columns": ["platform", "model_type"]}, {"name": "ai_model_configs_is_active_priority_index", "unique": false, "type": "BTREE", "columns": ["is_active", "priority"]}], "foreign_keys": [], "row_count": 8, "engine": "InnoDB", "charset": "utf8mb4_unicode_ci", "created_at": "2025-07-27 18:05:25"}, {"name": "p_ai_resources", "comment": "", "fields": [{"name": "id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "资源ID", "extra": "auto_increment", "key": "PRI"}, {"name": "resource_uuid", "type": "<PERSON><PERSON><PERSON>(36)", "nullable": false, "default": null, "comment": "资源唯一标识", "extra": "", "key": "UNI"}, {"name": "user_id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "归属用户ID，0表示系统资源", "extra": "", "key": "MUL"}, {"name": "project_id", "type": "bigint(20) unsigned", "nullable": true, "default": null, "comment": "项目ID", "extra": "", "key": "MUL"}, {"name": "owner_type", "type": "enum('system','user')", "nullable": false, "default": "user", "comment": "归属类型", "extra": "", "key": ""}, {"name": "module_type", "type": "enum('character','style','voice_library','sound_library','music_library','video_project')", "nullable": false, "default": null, "comment": "归属模块", "extra": "", "key": "MUL"}, {"name": "module_id", "type": "bigint(20) unsigned", "nullable": true, "default": null, "comment": "对应模块的内容ID", "extra": "", "key": ""}, {"name": "resource_type", "type": "enum('story','image','voice','video','music','sound')", "nullable": false, "default": null, "comment": "资源类型", "extra": "", "key": "MUL"}, {"name": "generation_config", "type": "json", "nullable": true, "default": null, "comment": "生成配置", "extra": "", "key": ""}, {"name": "output_format", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": true, "default": null, "comment": "输出格式", "extra": "", "key": ""}, {"name": "quality_level", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": true, "default": null, "comment": "质量级别", "extra": "", "key": ""}, {"name": "batch_size", "type": "int(11)", "nullable": false, "default": "1", "comment": "批量大小", "extra": "", "key": ""}, {"name": "estimated_cost", "type": "decimal(8,4)", "nullable": false, "default": "0.0000", "comment": "预估成本", "extra": "", "key": ""}, {"name": "actual_cost", "type": "decimal(8,4)", "nullable": false, "default": "0.0000", "comment": "实际成本", "extra": "", "key": ""}, {"name": "generation_task_id", "type": "bigint(20) unsigned", "nullable": true, "default": null, "comment": "生成任务ID", "extra": "", "key": "MUL"}, {"name": "file_path", "type": "var<PERSON><PERSON>(500)", "nullable": true, "default": null, "comment": "文件路径", "extra": "", "key": ""}, {"name": "file_size", "type": "bigint(20)", "nullable": true, "default": null, "comment": "文件大小", "extra": "", "key": ""}, {"name": "file_hash", "type": "<PERSON><PERSON><PERSON>(64)", "nullable": true, "default": null, "comment": "文件哈希", "extra": "", "key": "MUL"}, {"name": "download_count", "type": "int(11)", "nullable": false, "default": "0", "comment": "下载次数", "extra": "", "key": ""}, {"name": "processing_time_ms", "type": "int(11)", "nullable": true, "default": null, "comment": "处理时间(毫秒)", "extra": "", "key": ""}, {"name": "metadata", "type": "json", "nullable": true, "default": null, "comment": "元数据", "extra": "", "key": ""}, {"name": "completed_at", "type": "timestamp", "nullable": true, "default": null, "comment": "完成时间", "extra": "", "key": "MUL"}, {"name": "file_metadata", "type": "json", "nullable": true, "default": null, "comment": "文件元数据", "extra": "", "key": ""}, {"name": "project_context", "type": "json", "nullable": true, "default": null, "comment": "生成时的项目上下文信息", "extra": "", "key": ""}, {"name": "downloaded_by_python", "type": "tinyint(1)", "nullable": false, "default": "0", "comment": "Python工具是否已下载", "extra": "", "key": ""}, {"name": "python_downloaded_at", "type": "timestamp", "nullable": true, "default": null, "comment": "Python工具下载时间", "extra": "", "key": ""}, {"name": "python_local_info", "type": "json", "nullable": true, "default": null, "comment": "Python工具本地信息", "extra": "", "key": ""}, {"name": "status", "type": "enum('pending','processing','completed','failed','cancelled')", "nullable": false, "default": "pending", "comment": "资源状态", "extra": "", "key": ""}, {"name": "current_version_id", "type": "bigint(20) unsigned", "nullable": true, "default": null, "comment": "当前使用的版本ID", "extra": "", "key": "MUL"}, {"name": "created_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}, {"name": "updated_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}, {"name": "deleted_at", "type": "timestamp", "nullable": true, "default": null, "comment": "软删除时间", "extra": "", "key": ""}], "indexes": [{"name": "PRIMARY", "unique": true, "type": "BTREE", "columns": ["id"]}, {"name": "ai_resources_resource_uuid_unique", "unique": true, "type": "BTREE", "columns": ["resource_uuid"]}, {"name": "resource_uuid", "unique": true, "type": "BTREE", "columns": ["resource_uuid"]}, {"name": "ai_resources_user_id_status_index", "unique": false, "type": "BTREE", "columns": ["user_id", "status"]}, {"name": "ai_resources_resource_type_index", "unique": false, "type": "BTREE", "columns": ["resource_type"]}, {"name": "ai_resources_module_type_module_id_index", "unique": false, "type": "BTREE", "columns": ["module_type", "module_id"]}, {"name": "ai_resources_current_version_id_index", "unique": false, "type": "BTREE", "columns": ["current_version_id"]}, {"name": "ai_resources_resource_uuid_index", "unique": false, "type": "BTREE", "columns": ["resource_uuid"]}, {"name": "ai_resources_project_id_foreign", "unique": false, "type": "BTREE", "columns": ["project_id"]}, {"name": "ai_resources_generation_task_id_index", "unique": false, "type": "BTREE", "columns": ["generation_task_id"]}, {"name": "ai_resources_file_hash_index", "unique": false, "type": "BTREE", "columns": ["file_hash"]}, {"name": "ai_resources_completed_at_index", "unique": false, "type": "BTREE", "columns": ["completed_at"]}], "foreign_keys": [{"constraint_name": "ai_resources_project_id_foreign", "column": "project_id", "referenced_table": "p_projects", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "SET NULL"}, {"constraint_name": "ai_resources_user_id_foreign", "column": "user_id", "referenced_table": "p_users", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}], "row_count": 0, "engine": "InnoDB", "charset": "utf8mb4_unicode_ci", "created_at": "2025-07-27 18:05:29"}, {"name": "p_character_categories", "comment": "", "fields": [{"name": "id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "", "extra": "auto_increment", "key": "PRI"}, {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "nullable": false, "default": null, "comment": "分类名称", "extra": "", "key": "MUL"}, {"name": "slug", "type": "<PERSON><PERSON><PERSON>(100)", "nullable": false, "default": null, "comment": "分类标识", "extra": "", "key": "UNI"}, {"name": "description", "type": "text", "nullable": true, "default": null, "comment": "分类描述", "extra": "", "key": ""}, {"name": "icon", "type": "<PERSON><PERSON><PERSON>(255)", "nullable": true, "default": null, "comment": "分类图标URL", "extra": "", "key": ""}, {"name": "color", "type": "<PERSON><PERSON><PERSON>(20)", "nullable": true, "default": null, "comment": "分类颜色", "extra": "", "key": ""}, {"name": "parent_id", "type": "bigint(20) unsigned", "nullable": true, "default": null, "comment": "父分类ID", "extra": "", "key": "MUL"}, {"name": "is_active", "type": "tinyint(1)", "nullable": false, "default": "1", "comment": "是否启用", "extra": "", "key": "MUL"}, {"name": "sort_order", "type": "int(11)", "nullable": false, "default": "0", "comment": "排序权重", "extra": "", "key": "MUL"}, {"name": "character_count", "type": "int(11)", "nullable": false, "default": "0", "comment": "角色数量", "extra": "", "key": ""}, {"name": "metadata", "type": "json", "nullable": true, "default": null, "comment": "元数据", "extra": "", "key": ""}, {"name": "created_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}, {"name": "updated_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}], "indexes": [{"name": "PRIMARY", "unique": true, "type": "BTREE", "columns": ["id"]}, {"name": "character_categories_slug_unique", "unique": true, "type": "BTREE", "columns": ["slug"]}, {"name": "character_categories_name_index", "unique": false, "type": "BTREE", "columns": ["name"]}, {"name": "character_categories_slug_index", "unique": false, "type": "BTREE", "columns": ["slug"]}, {"name": "character_categories_parent_id_index", "unique": false, "type": "BTREE", "columns": ["parent_id"]}, {"name": "character_categories_is_active_index", "unique": false, "type": "BTREE", "columns": ["is_active"]}, {"name": "character_categories_sort_order_index", "unique": false, "type": "BTREE", "columns": ["sort_order"]}, {"name": "character_categories_is_active_sort_order_index", "unique": false, "type": "BTREE", "columns": ["is_active", "sort_order"]}, {"name": "character_categories_parent_id_is_active_index", "unique": false, "type": "BTREE", "columns": ["parent_id", "is_active"]}], "foreign_keys": [{"constraint_name": "character_categories_parent_id_foreign", "column": "parent_id", "referenced_table": "p_character_categories", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "SET NULL"}], "row_count": 0, "engine": "InnoDB", "charset": "utf8mb4_unicode_ci", "created_at": "2025-07-27 18:05:26"}, {"name": "p_character_library", "comment": "", "fields": [{"name": "id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "", "extra": "auto_increment", "key": "PRI"}, {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "nullable": false, "default": null, "comment": "角色名称", "extra": "", "key": "MUL"}, {"name": "description", "type": "text", "nullable": false, "default": null, "comment": "角色描述", "extra": "", "key": ""}, {"name": "category_id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "分类ID", "extra": "", "key": "MUL"}, {"name": "gender", "type": "<PERSON><PERSON><PERSON>(20)", "nullable": true, "default": null, "comment": "性别", "extra": "", "key": "MUL"}, {"name": "age_range", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": true, "default": null, "comment": "年龄范围", "extra": "", "key": ""}, {"name": "personality", "type": "var<PERSON><PERSON>(500)", "nullable": true, "default": null, "comment": "性格特点", "extra": "", "key": ""}, {"name": "background", "type": "<PERSON><PERSON><PERSON>(1000)", "nullable": true, "default": null, "comment": "背景故事", "extra": "", "key": ""}, {"name": "appearance", "type": "var<PERSON><PERSON>(500)", "nullable": true, "default": null, "comment": "外貌描述", "extra": "", "key": ""}, {"name": "avatar", "type": "<PERSON><PERSON><PERSON>(255)", "nullable": true, "default": null, "comment": "头像URL", "extra": "", "key": ""}, {"name": "images", "type": "json", "nullable": true, "default": null, "comment": "角色图片集", "extra": "", "key": ""}, {"name": "voice_config", "type": "json", "nullable": true, "default": null, "comment": "语音配置", "extra": "", "key": ""}, {"name": "style_preferences", "type": "json", "nullable": true, "default": null, "comment": "风格偏好", "extra": "", "key": ""}, {"name": "tags", "type": "json", "nullable": true, "default": null, "comment": "标签", "extra": "", "key": ""}, {"name": "is_active", "type": "tinyint(1)", "nullable": false, "default": "1", "comment": "是否启用", "extra": "", "key": "MUL"}, {"name": "is_premium", "type": "tinyint(1)", "nullable": false, "default": "0", "comment": "是否高级角色", "extra": "", "key": "MUL"}, {"name": "is_featured", "type": "tinyint(1)", "nullable": false, "default": "0", "comment": "是否推荐角色", "extra": "", "key": "MUL"}, {"name": "sort_order", "type": "int(11)", "nullable": false, "default": "0", "comment": "排序权重", "extra": "", "key": "MUL"}, {"name": "binding_count", "type": "int(11)", "nullable": false, "default": "0", "comment": "绑定次数", "extra": "", "key": "MUL"}, {"name": "rating", "type": "decimal(3,2)", "nullable": false, "default": "0.00", "comment": "评分", "extra": "", "key": "MUL"}, {"name": "rating_count", "type": "int(11)", "nullable": false, "default": "0", "comment": "评分次数", "extra": "", "key": ""}, {"name": "created_by", "type": "bigint(20) unsigned", "nullable": true, "default": null, "comment": "创建者ID", "extra": "", "key": "MUL"}, {"name": "created_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}, {"name": "updated_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}], "indexes": [{"name": "PRIMARY", "unique": true, "type": "BTREE", "columns": ["id"]}, {"name": "character_library_name_index", "unique": false, "type": "BTREE", "columns": ["name"]}, {"name": "character_library_category_id_index", "unique": false, "type": "BTREE", "columns": ["category_id"]}, {"name": "character_library_gender_index", "unique": false, "type": "BTREE", "columns": ["gender"]}, {"name": "character_library_is_active_index", "unique": false, "type": "BTREE", "columns": ["is_active"]}, {"name": "character_library_is_premium_index", "unique": false, "type": "BTREE", "columns": ["is_premium"]}, {"name": "character_library_is_featured_index", "unique": false, "type": "BTREE", "columns": ["is_featured"]}, {"name": "character_library_sort_order_index", "unique": false, "type": "BTREE", "columns": ["sort_order"]}, {"name": "character_library_binding_count_index", "unique": false, "type": "BTREE", "columns": ["binding_count"]}, {"name": "character_library_rating_index", "unique": false, "type": "BTREE", "columns": ["rating"]}, {"name": "character_library_created_by_index", "unique": false, "type": "BTREE", "columns": ["created_by"]}, {"name": "character_library_category_id_is_active_index", "unique": false, "type": "BTREE", "columns": ["category_id", "is_active"]}, {"name": "character_library_is_active_is_featured_index", "unique": false, "type": "BTREE", "columns": ["is_active", "is_featured"]}, {"name": "character_library_is_active_sort_order_index", "unique": false, "type": "BTREE", "columns": ["is_active", "sort_order"]}, {"name": "character_library_rating_rating_count_index", "unique": false, "type": "BTREE", "columns": ["rating", "rating_count"]}], "foreign_keys": [{"constraint_name": "character_library_category_id_foreign", "column": "category_id", "referenced_table": "p_character_categories", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}, {"constraint_name": "character_library_created_by_foreign", "column": "created_by", "referenced_table": "p_users", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "SET NULL"}], "row_count": 0, "engine": "InnoDB", "charset": "utf8mb4_unicode_ci", "created_at": "2025-07-27 18:05:26"}, {"name": "p_daily_tasks", "comment": "", "fields": [{"name": "id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "", "extra": "auto_increment", "key": "PRI"}, {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "nullable": false, "default": null, "comment": "任务名称", "extra": "", "key": ""}, {"name": "description", "type": "text", "nullable": false, "default": null, "comment": "任务描述", "extra": "", "key": ""}, {"name": "type", "type": "enum('login','share','create','interact','learn','social','creative','system')", "nullable": false, "default": null, "comment": "任务类型", "extra": "", "key": "MUL"}, {"name": "status", "type": "enum('active','inactive','archived')", "nullable": false, "default": "active", "comment": "状态", "extra": "", "key": ""}, {"name": "difficulty", "type": "enum('easy','medium','hard')", "nullable": false, "default": "easy", "comment": "难度", "extra": "", "key": "MUL"}, {"name": "repeat_type", "type": "enum('daily','weekly','monthly','once')", "nullable": false, "default": "daily", "comment": "重复类型", "extra": "", "key": "MUL"}, {"name": "icon", "type": "<PERSON><PERSON><PERSON>(255)", "nullable": true, "default": null, "comment": "任务图标", "extra": "", "key": ""}, {"name": "reward_experience", "type": "int(11)", "nullable": false, "default": "0", "comment": "奖励经验", "extra": "", "key": ""}, {"name": "reward_points", "type": "int(11)", "nullable": false, "default": "0", "comment": "奖励积分", "extra": "", "key": ""}, {"name": "reward_items", "type": "json", "nullable": true, "default": null, "comment": "奖励物品", "extra": "", "key": ""}, {"name": "conditions", "type": "json", "nullable": false, "default": null, "comment": "完成条件", "extra": "", "key": ""}, {"name": "target_value", "type": "int(11)", "nullable": false, "default": "1", "comment": "目标值", "extra": "", "key": ""}, {"name": "target_unit", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": true, "default": null, "comment": "目标单位", "extra": "", "key": ""}, {"name": "sort_order", "type": "int(11)", "nullable": false, "default": "0", "comment": "排序", "extra": "", "key": "MUL"}, {"name": "reset_time", "type": "time", "nullable": false, "default": "00:00:00", "comment": "重置时间", "extra": "", "key": ""}, {"name": "start_time", "type": "timestamp", "nullable": true, "default": null, "comment": "开始时间", "extra": "", "key": ""}, {"name": "end_time", "type": "timestamp", "nullable": true, "default": null, "comment": "结束时间", "extra": "", "key": ""}, {"name": "metadata", "type": "json", "nullable": true, "default": null, "comment": "元数据", "extra": "", "key": ""}, {"name": "created_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}, {"name": "updated_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}], "indexes": [{"name": "PRIMARY", "unique": true, "type": "BTREE", "columns": ["id"]}, {"name": "daily_tasks_type_status_index", "unique": false, "type": "BTREE", "columns": ["type", "status"]}, {"name": "daily_tasks_difficulty_status_index", "unique": false, "type": "BTREE", "columns": ["difficulty", "status"]}, {"name": "daily_tasks_repeat_type_status_index", "unique": false, "type": "BTREE", "columns": ["repeat_type", "status"]}, {"name": "daily_tasks_sort_order_index", "unique": false, "type": "BTREE", "columns": ["sort_order"]}], "foreign_keys": [], "row_count": 0, "engine": "InnoDB", "charset": "utf8mb4_unicode_ci", "created_at": "2025-07-27 18:08:10"}, {"name": "p_data_exports", "comment": "", "fields": [{"name": "id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "", "extra": "auto_increment", "key": "PRI"}, {"name": "user_id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "用户ID", "extra": "", "key": "MUL"}, {"name": "export_type", "type": "<PERSON><PERSON><PERSON>(100)", "nullable": false, "default": null, "comment": "导出类型", "extra": "", "key": "MUL"}, {"name": "export_format", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": false, "default": null, "comment": "导出格式", "extra": "", "key": "MUL"}, {"name": "export_params", "type": "json", "nullable": true, "default": null, "comment": "导出参数", "extra": "", "key": ""}, {"name": "export_filters", "type": "json", "nullable": true, "default": null, "comment": "导出筛选条件", "extra": "", "key": ""}, {"name": "status", "type": "enum('pending','processing','completed','failed','expired')", "nullable": false, "default": "pending", "comment": "导出状态", "extra": "", "key": "MUL"}, {"name": "file_path", "type": "var<PERSON><PERSON>(500)", "nullable": true, "default": null, "comment": "导出文件路径", "extra": "", "key": ""}, {"name": "file_url", "type": "var<PERSON><PERSON>(500)", "nullable": true, "default": null, "comment": "导出文件URL", "extra": "", "key": ""}, {"name": "file_size", "type": "bigint(20)", "nullable": true, "default": null, "comment": "文件大小(字节)", "extra": "", "key": ""}, {"name": "record_count", "type": "int(11)", "nullable": true, "default": null, "comment": "导出记录数", "extra": "", "key": ""}, {"name": "started_at", "type": "timestamp", "nullable": true, "default": null, "comment": "开始时间", "extra": "", "key": ""}, {"name": "completed_at", "type": "timestamp", "nullable": true, "default": null, "comment": "完成时间", "extra": "", "key": "MUL"}, {"name": "expires_at", "type": "timestamp", "nullable": true, "default": null, "comment": "过期时间", "extra": "", "key": "MUL"}, {"name": "error_message", "type": "text", "nullable": true, "default": null, "comment": "错误信息", "extra": "", "key": ""}, {"name": "progress_info", "type": "json", "nullable": true, "default": null, "comment": "进度信息", "extra": "", "key": ""}, {"name": "download_count", "type": "int(11)", "nullable": false, "default": "0", "comment": "下载次数", "extra": "", "key": ""}, {"name": "last_downloaded_at", "type": "timestamp", "nullable": true, "default": null, "comment": "最后下载时间", "extra": "", "key": ""}, {"name": "created_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": "MUL"}, {"name": "updated_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}], "indexes": [{"name": "PRIMARY", "unique": true, "type": "BTREE", "columns": ["id"]}, {"name": "data_exports_user_id_index", "unique": false, "type": "BTREE", "columns": ["user_id"]}, {"name": "data_exports_export_type_index", "unique": false, "type": "BTREE", "columns": ["export_type"]}, {"name": "data_exports_export_format_index", "unique": false, "type": "BTREE", "columns": ["export_format"]}, {"name": "data_exports_status_index", "unique": false, "type": "BTREE", "columns": ["status"]}, {"name": "data_exports_created_at_index", "unique": false, "type": "BTREE", "columns": ["created_at"]}, {"name": "data_exports_expires_at_index", "unique": false, "type": "BTREE", "columns": ["expires_at"]}, {"name": "data_exports_completed_at_index", "unique": false, "type": "BTREE", "columns": ["completed_at"]}, {"name": "data_exports_user_id_export_type_index", "unique": false, "type": "BTREE", "columns": ["user_id", "export_type"]}, {"name": "data_exports_user_id_status_index", "unique": false, "type": "BTREE", "columns": ["user_id", "status"]}, {"name": "data_exports_status_created_at_index", "unique": false, "type": "BTREE", "columns": ["status", "created_at"]}, {"name": "data_exports_expires_at_status_index", "unique": false, "type": "BTREE", "columns": ["expires_at", "status"]}], "foreign_keys": [{"constraint_name": "data_exports_user_id_foreign", "column": "user_id", "referenced_table": "p_users", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}], "row_count": 0, "engine": "InnoDB", "charset": "utf8mb4_unicode_ci", "created_at": "2025-07-27 18:05:28"}, {"name": "p_growth_histories", "comment": "", "fields": [{"name": "id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "", "extra": "auto_increment", "key": "PRI"}, {"name": "user_id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "用户ID", "extra": "", "key": "MUL"}, {"name": "event_type", "type": "enum('achievement_unlocked','task_completed','level_up','points_earned','experience_gained','milestone_reached','badge_earned','streak_achieved','challenge_completed','social_interaction','content_created','skill_improved','goal_achieved','reward_claimed','system_bonus')", "nullable": false, "default": null, "comment": "事件类型", "extra": "", "key": "MUL"}, {"name": "description", "type": "text", "nullable": false, "default": null, "comment": "事件描述", "extra": "", "key": ""}, {"name": "experience_before", "type": "int(11)", "nullable": false, "default": "0", "comment": "事件前经验值", "extra": "", "key": ""}, {"name": "experience_change", "type": "int(11)", "nullable": false, "default": "0", "comment": "经验值变化", "extra": "", "key": "MUL"}, {"name": "experience_after", "type": "int(11)", "nullable": false, "default": "0", "comment": "事件后经验值", "extra": "", "key": ""}, {"name": "level_before", "type": "int(11)", "nullable": false, "default": "1", "comment": "事件前等级", "extra": "", "key": ""}, {"name": "level_after", "type": "int(11)", "nullable": false, "default": "1", "comment": "事件后等级", "extra": "", "key": ""}, {"name": "points_before", "type": "int(11)", "nullable": false, "default": "0", "comment": "事件前积分", "extra": "", "key": ""}, {"name": "points_change", "type": "int(11)", "nullable": false, "default": "0", "comment": "积分变化", "extra": "", "key": "MUL"}, {"name": "points_after", "type": "int(11)", "nullable": false, "default": "0", "comment": "事件后积分", "extra": "", "key": ""}, {"name": "related_type", "type": "<PERSON><PERSON><PERSON>(100)", "nullable": true, "default": null, "comment": "关联对象类型", "extra": "", "key": "MUL"}, {"name": "related_id", "type": "bigint(20) unsigned", "nullable": true, "default": null, "comment": "关联对象ID", "extra": "", "key": ""}, {"name": "event_data", "type": "json", "nullable": true, "default": null, "comment": "事件数据", "extra": "", "key": ""}, {"name": "rewards", "type": "json", "nullable": true, "default": null, "comment": "获得奖励", "extra": "", "key": ""}, {"name": "metadata", "type": "json", "nullable": true, "default": null, "comment": "元数据", "extra": "", "key": ""}, {"name": "created_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}, {"name": "updated_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}], "indexes": [{"name": "PRIMARY", "unique": true, "type": "BTREE", "columns": ["id"]}, {"name": "growth_histories_user_id_created_at_index", "unique": false, "type": "BTREE", "columns": ["user_id", "created_at"]}, {"name": "growth_histories_event_type_created_at_index", "unique": false, "type": "BTREE", "columns": ["event_type", "created_at"]}, {"name": "growth_histories_related_type_related_id_index", "unique": false, "type": "BTREE", "columns": ["related_type", "related_id"]}, {"name": "growth_histories_experience_change_index", "unique": false, "type": "BTREE", "columns": ["experience_change"]}, {"name": "growth_histories_points_change_index", "unique": false, "type": "BTREE", "columns": ["points_change"]}], "foreign_keys": [{"constraint_name": "growth_histories_user_id_foreign", "column": "user_id", "referenced_table": "p_users", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}], "row_count": 0, "engine": "InnoDB", "charset": "utf8mb4_unicode_ci", "created_at": "2025-07-27 18:09:09"}, {"name": "p_migrations", "comment": "", "fields": [{"name": "id", "type": "int(10) unsigned", "nullable": false, "default": null, "comment": "", "extra": "auto_increment", "key": "PRI"}, {"name": "migration", "type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "comment": "", "extra": "", "key": ""}, {"name": "batch", "type": "int(11)", "nullable": false, "default": null, "comment": "", "extra": "", "key": ""}], "indexes": [{"name": "PRIMARY", "unique": true, "type": "BTREE", "columns": ["id"]}], "foreign_keys": [], "row_count": 35, "engine": "InnoDB", "charset": "utf8mb4_unicode_ci", "created_at": "2025-07-27 18:05:23"}, {"name": "p_platform_performance_metrics", "comment": "", "fields": [{"name": "id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "主键ID", "extra": "auto_increment", "key": "PRI"}, {"name": "platform", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": false, "default": null, "comment": "平台标识", "extra": "", "key": "MUL"}, {"name": "business_type", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": false, "default": null, "comment": "业务类型", "extra": "", "key": "MUL"}, {"name": "response_time_avg", "type": "decimal(8,3)", "nullable": false, "default": null, "comment": "平均响应时间(秒)", "extra": "", "key": ""}, {"name": "success_rate", "type": "decimal(5,4)", "nullable": false, "default": null, "comment": "成功率", "extra": "", "key": "MUL"}, {"name": "cost_score", "type": "decimal(5,2)", "nullable": false, "default": null, "comment": "成本评分", "extra": "", "key": ""}, {"name": "quality_score", "type": "decimal(5,2)", "nullable": false, "default": null, "comment": "质量评分", "extra": "", "key": ""}, {"name": "total_requests", "type": "int(11)", "nullable": false, "default": "0", "comment": "总请求数", "extra": "", "key": ""}, {"name": "failed_requests", "type": "int(11)", "nullable": false, "default": "0", "comment": "失败请求数", "extra": "", "key": ""}, {"name": "uptime_percentage", "type": "decimal(5,2)", "nullable": false, "default": "100.00", "comment": "可用性百分比", "extra": "", "key": "MUL"}, {"name": "detailed_metrics", "type": "json", "nullable": true, "default": null, "comment": "详细指标", "extra": "", "key": ""}, {"name": "metric_date", "type": "date", "nullable": false, "default": null, "comment": "指标日期", "extra": "", "key": ""}, {"name": "created_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}, {"name": "updated_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}], "indexes": [{"name": "PRIMARY", "unique": true, "type": "BTREE", "columns": ["id"]}, {"name": "uk_platform_business_date", "unique": true, "type": "BTREE", "columns": ["platform", "business_type", "metric_date"]}, {"name": "idx_platform_date", "unique": false, "type": "BTREE", "columns": ["platform", "metric_date"]}, {"name": "idx_business_date", "unique": false, "type": "BTREE", "columns": ["business_type", "metric_date"]}, {"name": "idx_success_rate", "unique": false, "type": "BTREE", "columns": ["success_rate"]}, {"name": "idx_uptime", "unique": false, "type": "BTREE", "columns": ["uptime_percentage"]}], "foreign_keys": [], "row_count": 0, "engine": "InnoDB", "charset": "utf8mb4_unicode_ci", "created_at": "2025-07-29 03:04:47"}, {"name": "p_points_freeze", "comment": "", "fields": [{"name": "id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "", "extra": "auto_increment", "key": "PRI"}, {"name": "user_id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "用户ID", "extra": "", "key": "MUL"}, {"name": "transaction_id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "关联交易ID", "extra": "", "key": "MUL"}, {"name": "amount", "type": "decimal(10,2)", "nullable": false, "default": null, "comment": "冻结积分数量", "extra": "", "key": ""}, {"name": "status", "type": "enum('frozen','released','consumed')", "nullable": false, "default": "frozen", "comment": "状态：frozen=冻结中，released=已释放，consumed=已消费", "extra": "", "key": "MUL"}, {"name": "business_type", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": false, "default": null, "comment": "业务类型", "extra": "", "key": "MUL"}, {"name": "business_id", "type": "<PERSON><PERSON><PERSON>(100)", "nullable": true, "default": null, "comment": "业务ID", "extra": "", "key": ""}, {"name": "expires_at", "type": "timestamp", "nullable": true, "default": null, "comment": "过期时间", "extra": "", "key": "MUL"}, {"name": "released_at", "type": "timestamp", "nullable": true, "default": null, "comment": "释放时间", "extra": "", "key": ""}, {"name": "reason", "type": "text", "nullable": true, "default": null, "comment": "冻结原因", "extra": "", "key": ""}, {"name": "created_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}, {"name": "updated_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}], "indexes": [{"name": "PRIMARY", "unique": true, "type": "BTREE", "columns": ["id"]}, {"name": "points_freeze_user_id_index", "unique": false, "type": "BTREE", "columns": ["user_id"]}, {"name": "points_freeze_transaction_id_index", "unique": false, "type": "BTREE", "columns": ["transaction_id"]}, {"name": "points_freeze_status_index", "unique": false, "type": "BTREE", "columns": ["status"]}, {"name": "points_freeze_business_type_index", "unique": false, "type": "BTREE", "columns": ["business_type"]}, {"name": "points_freeze_expires_at_index", "unique": false, "type": "BTREE", "columns": ["expires_at"]}, {"name": "points_freeze_user_id_status_index", "unique": false, "type": "BTREE", "columns": ["user_id", "status"]}, {"name": "points_freeze_business_type_status_index", "unique": false, "type": "BTREE", "columns": ["business_type", "status"]}], "foreign_keys": [{"constraint_name": "points_freeze_transaction_id_foreign", "column": "transaction_id", "referenced_table": "p_points_transactions", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}, {"constraint_name": "points_freeze_user_id_foreign", "column": "user_id", "referenced_table": "p_users", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}], "row_count": 0, "engine": "InnoDB", "charset": "utf8mb4_unicode_ci", "created_at": "2025-07-27 18:05:24"}, {"name": "p_points_transactions", "comment": "", "fields": [{"name": "id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "", "extra": "auto_increment", "key": "PRI"}, {"name": "user_id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "用户ID", "extra": "", "key": "MUL"}, {"name": "business_type", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": false, "default": null, "comment": "业务类型", "extra": "", "key": "MUL"}, {"name": "business_id", "type": "<PERSON><PERSON><PERSON>(100)", "nullable": true, "default": null, "comment": "业务ID", "extra": "", "key": ""}, {"name": "amount", "type": "decimal(10,2)", "nullable": false, "default": null, "comment": "积分数量", "extra": "", "key": ""}, {"name": "status", "type": "enum('frozen','success','failed','refunded')", "nullable": false, "default": "frozen", "comment": "状态", "extra": "", "key": "MUL"}, {"name": "ai_platform", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": true, "default": null, "comment": "AI平台", "extra": "", "key": "MUL"}, {"name": "request_data", "type": "json", "nullable": true, "default": null, "comment": "请求数据", "extra": "", "key": ""}, {"name": "response_data", "type": "json", "nullable": true, "default": null, "comment": "响应数据", "extra": "", "key": ""}, {"name": "timeout_seconds", "type": "int(11)", "nullable": false, "default": "300", "comment": "超时时间(秒)", "extra": "", "key": ""}, {"name": "completed_at", "type": "timestamp", "nullable": true, "default": null, "comment": "完成时间", "extra": "", "key": ""}, {"name": "failure_reason", "type": "text", "nullable": true, "default": null, "comment": "失败原因", "extra": "", "key": ""}, {"name": "created_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": "MUL"}, {"name": "updated_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}], "indexes": [{"name": "PRIMARY", "unique": true, "type": "BTREE", "columns": ["id"]}, {"name": "points_transactions_user_id_index", "unique": false, "type": "BTREE", "columns": ["user_id"]}, {"name": "points_transactions_business_type_index", "unique": false, "type": "BTREE", "columns": ["business_type"]}, {"name": "points_transactions_status_index", "unique": false, "type": "BTREE", "columns": ["status"]}, {"name": "points_transactions_ai_platform_index", "unique": false, "type": "BTREE", "columns": ["ai_platform"]}, {"name": "points_transactions_created_at_index", "unique": false, "type": "BTREE", "columns": ["created_at"]}, {"name": "points_transactions_user_id_status_index", "unique": false, "type": "BTREE", "columns": ["user_id", "status"]}, {"name": "points_transactions_business_type_status_index", "unique": false, "type": "BTREE", "columns": ["business_type", "status"]}], "foreign_keys": [{"constraint_name": "points_transactions_user_id_foreign", "column": "user_id", "referenced_table": "p_users", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}], "row_count": 0, "engine": "InnoDB", "charset": "utf8mb4_unicode_ci", "created_at": "2025-07-27 18:05:24"}, {"name": "p_projects", "comment": "", "fields": [{"name": "id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "", "extra": "auto_increment", "key": "PRI"}, {"name": "user_id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "用户ID", "extra": "", "key": "MUL"}, {"name": "title", "type": "<PERSON><PERSON><PERSON>(200)", "nullable": false, "default": null, "comment": "项目标题", "extra": "", "key": ""}, {"name": "description", "type": "text", "nullable": true, "default": null, "comment": "项目描述", "extra": "", "key": ""}, {"name": "style_id", "type": "bigint(20) unsigned", "nullable": true, "default": null, "comment": "使用的风格ID", "extra": "", "key": "MUL"}, {"name": "story_content", "type": "text", "nullable": true, "default": null, "comment": "剧情内容", "extra": "", "key": ""}, {"name": "ai_generated_title", "type": "text", "nullable": true, "default": null, "comment": "AI生成的标题", "extra": "", "key": ""}, {"name": "title_confirmed", "type": "tinyint(1)", "nullable": false, "default": "0", "comment": "标题是否已确认", "extra": "", "key": "MUL"}, {"name": "status", "type": "enum('draft','in_progress','completed','published','archived')", "nullable": false, "default": "draft", "comment": "项目状态", "extra": "", "key": "MUL"}, {"name": "project_config", "type": "json", "nullable": true, "default": null, "comment": "项目配置", "extra": "", "key": ""}, {"name": "metadata", "type": "json", "nullable": true, "default": null, "comment": "元数据", "extra": "", "key": ""}, {"name": "last_accessed_at", "type": "timestamp", "nullable": true, "default": null, "comment": "最后访问时间", "extra": "", "key": "MUL"}, {"name": "completed_at", "type": "timestamp", "nullable": true, "default": null, "comment": "完成时间", "extra": "", "key": ""}, {"name": "published_at", "type": "timestamp", "nullable": true, "default": null, "comment": "发布时间", "extra": "", "key": ""}, {"name": "view_count", "type": "int(11)", "nullable": false, "default": "0", "comment": "查看次数", "extra": "", "key": ""}, {"name": "is_public", "type": "tinyint(1)", "nullable": false, "default": "0", "comment": "是否公开", "extra": "", "key": "MUL"}, {"name": "created_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": "MUL"}, {"name": "updated_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}], "indexes": [{"name": "PRIMARY", "unique": true, "type": "BTREE", "columns": ["id"]}, {"name": "projects_user_id_index", "unique": false, "type": "BTREE", "columns": ["user_id"]}, {"name": "projects_style_id_index", "unique": false, "type": "BTREE", "columns": ["style_id"]}, {"name": "projects_status_index", "unique": false, "type": "BTREE", "columns": ["status"]}, {"name": "projects_title_confirmed_index", "unique": false, "type": "BTREE", "columns": ["title_confirmed"]}, {"name": "projects_is_public_index", "unique": false, "type": "BTREE", "columns": ["is_public"]}, {"name": "projects_last_accessed_at_index", "unique": false, "type": "BTREE", "columns": ["last_accessed_at"]}, {"name": "projects_created_at_index", "unique": false, "type": "BTREE", "columns": ["created_at"]}, {"name": "projects_user_id_status_index", "unique": false, "type": "BTREE", "columns": ["user_id", "status"]}, {"name": "projects_status_is_public_index", "unique": false, "type": "BTREE", "columns": ["status", "is_public"]}], "foreign_keys": [{"constraint_name": "projects_style_id_foreign", "column": "style_id", "referenced_table": "p_style_library", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "SET NULL"}, {"constraint_name": "projects_user_id_foreign", "column": "user_id", "referenced_table": "p_users", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}], "row_count": 0, "engine": "InnoDB", "charset": "utf8mb4_unicode_ci", "created_at": "2025-07-27 18:05:25"}, {"name": "p_publications", "comment": "", "fields": [{"name": "id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "", "extra": "auto_increment", "key": "PRI"}, {"name": "user_id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "用户ID", "extra": "", "key": "MUL"}, {"name": "resource_id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "资源ID", "extra": "", "key": "MUL"}, {"name": "title", "type": "<PERSON><PERSON><PERSON>(200)", "nullable": false, "default": null, "comment": "作品标题", "extra": "", "key": ""}, {"name": "description", "type": "text", "nullable": true, "default": null, "comment": "作品描述", "extra": "", "key": ""}, {"name": "tags", "type": "json", "nullable": true, "default": null, "comment": "标签", "extra": "", "key": ""}, {"name": "category", "type": "enum('story','image','voice','video','music','sound','mixed')", "nullable": false, "default": null, "comment": "作品分类", "extra": "", "key": "MUL"}, {"name": "visibility", "type": "enum('public','private','unlisted')", "nullable": false, "default": "public", "comment": "可见性", "extra": "", "key": "MUL"}, {"name": "status", "type": "enum('pending_review','published','rejected','unpublished')", "nullable": false, "default": "pending_review", "comment": "发布状态", "extra": "", "key": "MUL"}, {"name": "review_status", "type": "enum('pending','approved','rejected','appealing')", "nullable": false, "default": "pending", "comment": "审核状态", "extra": "", "key": "MUL"}, {"name": "allow_comments", "type": "tinyint(1)", "nullable": false, "default": "1", "comment": "允许评论", "extra": "", "key": ""}, {"name": "allow_download", "type": "tinyint(1)", "nullable": false, "default": "1", "comment": "允许下载", "extra": "", "key": ""}, {"name": "featured", "type": "tinyint(1)", "nullable": false, "default": "0", "comment": "是否精选", "extra": "", "key": "MUL"}, {"name": "view_count", "type": "int(11)", "nullable": false, "default": "0", "comment": "查看次数", "extra": "", "key": ""}, {"name": "like_count", "type": "int(11)", "nullable": false, "default": "0", "comment": "点赞次数", "extra": "", "key": ""}, {"name": "comment_count", "type": "int(11)", "nullable": false, "default": "0", "comment": "评论次数", "extra": "", "key": ""}, {"name": "download_count", "type": "int(11)", "nullable": false, "default": "0", "comment": "下载次数", "extra": "", "key": ""}, {"name": "share_count", "type": "int(11)", "nullable": false, "default": "0", "comment": "分享次数", "extra": "", "key": ""}, {"name": "thumbnail", "type": "<PERSON><PERSON><PERSON>(255)", "nullable": true, "default": null, "comment": "缩略图", "extra": "", "key": ""}, {"name": "metadata", "type": "json", "nullable": true, "default": null, "comment": "元数据", "extra": "", "key": ""}, {"name": "published_at", "type": "timestamp", "nullable": true, "default": null, "comment": "发布时间", "extra": "", "key": "MUL"}, {"name": "unpublished_at", "type": "timestamp", "nullable": true, "default": null, "comment": "下架时间", "extra": "", "key": ""}, {"name": "created_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}, {"name": "updated_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}, {"name": "deleted_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}], "indexes": [{"name": "PRIMARY", "unique": true, "type": "BTREE", "columns": ["id"]}, {"name": "publications_user_id_index", "unique": false, "type": "BTREE", "columns": ["user_id"]}, {"name": "publications_resource_id_index", "unique": false, "type": "BTREE", "columns": ["resource_id"]}, {"name": "publications_category_index", "unique": false, "type": "BTREE", "columns": ["category"]}, {"name": "publications_visibility_index", "unique": false, "type": "BTREE", "columns": ["visibility"]}, {"name": "publications_status_index", "unique": false, "type": "BTREE", "columns": ["status"]}, {"name": "publications_review_status_index", "unique": false, "type": "BTREE", "columns": ["review_status"]}, {"name": "publications_featured_index", "unique": false, "type": "BTREE", "columns": ["featured"]}, {"name": "publications_published_at_index", "unique": false, "type": "BTREE", "columns": ["published_at"]}, {"name": "publications_user_id_status_index", "unique": false, "type": "BTREE", "columns": ["user_id", "status"]}, {"name": "publications_category_status_index", "unique": false, "type": "BTREE", "columns": ["category", "status"]}, {"name": "publications_status_published_at_index", "unique": false, "type": "BTREE", "columns": ["status", "published_at"]}, {"name": "publications_featured_status_index", "unique": false, "type": "BTREE", "columns": ["featured", "status"]}], "foreign_keys": [{"constraint_name": "publications_resource_id_foreign", "column": "resource_id", "referenced_table": "p_ai_resources", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}, {"constraint_name": "publications_user_id_foreign", "column": "user_id", "referenced_table": "p_users", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}], "row_count": 0, "engine": "InnoDB", "charset": "utf8mb4_unicode_ci", "created_at": "2025-07-27 18:05:30"}, {"name": "p_resource_versions", "comment": "", "fields": [{"name": "id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "版本ID", "extra": "auto_increment", "key": "PRI"}, {"name": "version_uuid", "type": "<PERSON><PERSON><PERSON>(36)", "nullable": false, "default": null, "comment": "版本唯一标识", "extra": "", "key": "UNI"}, {"name": "resource_id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "关联的资源ID", "extra": "", "key": "MUL"}, {"name": "user_id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "创建用户ID", "extra": "", "key": "MUL"}, {"name": "version_number", "type": "<PERSON><PERSON><PERSON>(20)", "nullable": false, "default": null, "comment": "版本号（v1.0, v1.1, v1.2...）", "extra": "", "key": "MUL"}, {"name": "version_type", "type": "enum('original','modified','template_derived')", "nullable": false, "default": "original", "comment": "版本类型", "extra": "", "key": ""}, {"name": "resource_url", "type": "<PERSON><PERSON><PERSON>(1000)", "nullable": false, "default": null, "comment": "此版本对应的资源URL（AI平台返回）", "extra": "", "key": ""}, {"name": "original_filename", "type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "comment": "原始文件名", "extra": "", "key": ""}, {"name": "file_size", "type": "bigint(20)", "nullable": true, "default": null, "comment": "文件大小（字节）- 视频资源可能无法获取准确大小", "extra": "", "key": ""}, {"name": "mime_type", "type": "<PERSON><PERSON><PERSON>(100)", "nullable": false, "default": null, "comment": "MIME类型", "extra": "", "key": ""}, {"name": "generation_cost", "type": "int(11)", "nullable": false, "default": "0", "comment": "生成此版本消耗的积分", "extra": "", "key": ""}, {"name": "cost_transaction_id", "type": "bigint(20) unsigned", "nullable": true, "default": null, "comment": "关联的积分交易ID", "extra": "", "key": "MUL"}, {"name": "prompt_text", "type": "text", "nullable": false, "default": null, "comment": "生成此版本使用的提示词", "extra": "", "key": ""}, {"name": "negative_prompt", "type": "text", "nullable": true, "default": null, "comment": "负面提示词", "extra": "", "key": ""}, {"name": "ai_platform", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": false, "default": null, "comment": "生成此版本使用的AI平台", "extra": "", "key": "MUL"}, {"name": "ai_model", "type": "<PERSON><PERSON><PERSON>(100)", "nullable": true, "default": null, "comment": "使用的AI模型", "extra": "", "key": ""}, {"name": "generation_params", "type": "json", "nullable": true, "default": null, "comment": "生成参数配置", "extra": "", "key": ""}, {"name": "ai_platform_metadata", "type": "json", "nullable": true, "default": null, "comment": "AI平台返回的完整元数据", "extra": "", "key": ""}, {"name": "generated_at", "type": "timestamp", "nullable": false, "default": null, "comment": "AI生成时间", "extra": "", "key": "MUL"}, {"name": "url_expires_at", "type": "timestamp", "nullable": true, "default": null, "comment": "AI平台URL过期时间", "extra": "", "key": ""}, {"name": "resource_status", "type": "enum('generated','downloaded','exported','ready_for_publish')", "nullable": false, "default": "generated", "comment": "资源状态", "extra": "", "key": "MUL"}, {"name": "review_status", "type": "enum('not_reviewed','auto_approved','manual_pending','approved','rejected','flagged')", "nullable": false, "default": "not_reviewed", "comment": "审核状态（仅发布时需要）", "extra": "", "key": "MUL"}, {"name": "review_notes", "type": "text", "nullable": true, "default": null, "comment": "审核备注", "extra": "", "key": ""}, {"name": "is_downloaded_locally", "type": "tinyint(1)", "nullable": false, "default": "0", "comment": "是否已下载到本地", "extra": "", "key": ""}, {"name": "local_save_path", "type": "<PERSON><PERSON><PERSON>(255)", "nullable": true, "default": null, "comment": "本地保存路径", "extra": "", "key": ""}, {"name": "downloaded_at", "type": "timestamp", "nullable": true, "default": null, "comment": "下载完成时间", "extra": "", "key": ""}, {"name": "reviewer_id", "type": "bigint(20) unsigned", "nullable": true, "default": null, "comment": "审核人ID", "extra": "", "key": "MUL"}, {"name": "reviewed_at", "type": "timestamp", "nullable": true, "default": null, "comment": "审核时间", "extra": "", "key": ""}, {"name": "status", "type": "enum('active','inactive','deprecated')", "nullable": false, "default": "active", "comment": "版本状态", "extra": "", "key": ""}, {"name": "created_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}, {"name": "updated_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}], "indexes": [{"name": "PRIMARY", "unique": true, "type": "BTREE", "columns": ["id"]}, {"name": "resource_versions_version_uuid_unique", "unique": true, "type": "BTREE", "columns": ["version_uuid"]}, {"name": "resource_versions_resource_id_status_index", "unique": false, "type": "BTREE", "columns": ["resource_id", "status"]}, {"name": "resource_versions_user_id_index", "unique": false, "type": "BTREE", "columns": ["user_id"]}, {"name": "resource_versions_version_number_index", "unique": false, "type": "BTREE", "columns": ["version_number"]}, {"name": "resource_versions_ai_platform_index", "unique": false, "type": "BTREE", "columns": ["ai_platform"]}, {"name": "resource_versions_resource_status_index", "unique": false, "type": "BTREE", "columns": ["resource_status"]}, {"name": "resource_versions_review_status_index", "unique": false, "type": "BTREE", "columns": ["review_status"]}, {"name": "resource_versions_generated_at_index", "unique": false, "type": "BTREE", "columns": ["generated_at"]}, {"name": "resource_versions_cost_transaction_id_foreign", "unique": false, "type": "BTREE", "columns": ["cost_transaction_id"]}, {"name": "resource_versions_reviewer_id_foreign", "unique": false, "type": "BTREE", "columns": ["reviewer_id"]}], "foreign_keys": [{"constraint_name": "resource_versions_cost_transaction_id_foreign", "column": "cost_transaction_id", "referenced_table": "p_points_transactions", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "SET NULL"}, {"constraint_name": "resource_versions_resource_id_foreign", "column": "resource_id", "referenced_table": "p_ai_resources", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}, {"constraint_name": "resource_versions_reviewer_id_foreign", "column": "reviewer_id", "referenced_table": "p_users", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "SET NULL"}, {"constraint_name": "resource_versions_user_id_foreign", "column": "user_id", "referenced_table": "p_users", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}], "row_count": 0, "engine": "InnoDB", "charset": "utf8mb4_unicode_ci", "created_at": "2025-07-27 18:05:28"}, {"name": "p_style_library", "comment": "", "fields": [{"name": "id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "", "extra": "auto_increment", "key": "PRI"}, {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "nullable": false, "default": null, "comment": "风格名称", "extra": "", "key": "MUL"}, {"name": "description", "type": "text", "nullable": true, "default": null, "comment": "风格描述", "extra": "", "key": ""}, {"name": "category", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": false, "default": "general", "comment": "风格分类", "extra": "", "key": "MUL"}, {"name": "style_config", "type": "json", "nullable": true, "default": null, "comment": "风格配置参数", "extra": "", "key": ""}, {"name": "prompt_template", "type": "text", "nullable": true, "default": null, "comment": "提示词模板", "extra": "", "key": ""}, {"name": "thumbnail", "type": "<PERSON><PERSON><PERSON>(255)", "nullable": true, "default": null, "comment": "缩略图URL", "extra": "", "key": ""}, {"name": "is_active", "type": "tinyint(1)", "nullable": false, "default": "1", "comment": "是否启用", "extra": "", "key": "MUL"}, {"name": "is_premium", "type": "tinyint(1)", "nullable": false, "default": "0", "comment": "是否高级风格", "extra": "", "key": "MUL"}, {"name": "sort_order", "type": "int(11)", "nullable": false, "default": "0", "comment": "排序权重", "extra": "", "key": "MUL"}, {"name": "usage_count", "type": "int(11)", "nullable": false, "default": "0", "comment": "使用次数", "extra": "", "key": "MUL"}, {"name": "rating", "type": "decimal(3,2)", "nullable": false, "default": "0.00", "comment": "评分", "extra": "", "key": "MUL"}, {"name": "tags", "type": "json", "nullable": true, "default": null, "comment": "标签", "extra": "", "key": ""}, {"name": "created_by", "type": "bigint(20) unsigned", "nullable": true, "default": null, "comment": "创建者ID", "extra": "", "key": "MUL"}, {"name": "created_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}, {"name": "updated_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}], "indexes": [{"name": "PRIMARY", "unique": true, "type": "BTREE", "columns": ["id"]}, {"name": "style_library_name_index", "unique": false, "type": "BTREE", "columns": ["name"]}, {"name": "style_library_category_index", "unique": false, "type": "BTREE", "columns": ["category"]}, {"name": "style_library_is_active_index", "unique": false, "type": "BTREE", "columns": ["is_active"]}, {"name": "style_library_is_premium_index", "unique": false, "type": "BTREE", "columns": ["is_premium"]}, {"name": "style_library_sort_order_index", "unique": false, "type": "BTREE", "columns": ["sort_order"]}, {"name": "style_library_usage_count_index", "unique": false, "type": "BTREE", "columns": ["usage_count"]}, {"name": "style_library_rating_index", "unique": false, "type": "BTREE", "columns": ["rating"]}, {"name": "style_library_created_by_index", "unique": false, "type": "BTREE", "columns": ["created_by"]}, {"name": "style_library_category_is_active_index", "unique": false, "type": "BTREE", "columns": ["category", "is_active"]}, {"name": "style_library_is_active_sort_order_index", "unique": false, "type": "BTREE", "columns": ["is_active", "sort_order"]}], "foreign_keys": [{"constraint_name": "style_library_created_by_foreign", "column": "created_by", "referenced_table": "p_users", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "SET NULL"}], "row_count": 0, "engine": "InnoDB", "charset": "utf8mb4_unicode_ci", "created_at": "2025-07-27 18:05:25"}, {"name": "p_system_monitors", "comment": "", "fields": [{"name": "id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "", "extra": "auto_increment", "key": "PRI"}, {"name": "metric_type", "type": "<PERSON><PERSON><PERSON>(100)", "nullable": false, "default": null, "comment": "指标类型", "extra": "", "key": "MUL"}, {"name": "metric_name", "type": "<PERSON><PERSON><PERSON>(100)", "nullable": false, "default": null, "comment": "指标名称", "extra": "", "key": "MUL"}, {"name": "metric_value", "type": "decimal(15,4)", "nullable": false, "default": null, "comment": "指标值", "extra": "", "key": ""}, {"name": "metric_unit", "type": "<PERSON><PERSON><PERSON>(20)", "nullable": true, "default": null, "comment": "指标单位", "extra": "", "key": ""}, {"name": "metric_details", "type": "json", "nullable": true, "default": null, "comment": "指标详情", "extra": "", "key": ""}, {"name": "source", "type": "<PERSON><PERSON><PERSON>(100)", "nullable": false, "default": null, "comment": "数据源", "extra": "", "key": "MUL"}, {"name": "environment", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": false, "default": "production", "comment": "环境", "extra": "", "key": "MUL"}, {"name": "status", "type": "enum('normal','warning','critical','unknown')", "nullable": false, "default": "normal", "comment": "状态", "extra": "", "key": "MUL"}, {"name": "alert_message", "type": "text", "nullable": true, "default": null, "comment": "告警信息", "extra": "", "key": ""}, {"name": "collected_at", "type": "timestamp", "nullable": false, "default": null, "comment": "采集时间", "extra": "", "key": "MUL"}, {"name": "created_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}, {"name": "updated_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}], "indexes": [{"name": "PRIMARY", "unique": true, "type": "BTREE", "columns": ["id"]}, {"name": "system_monitors_metric_type_index", "unique": false, "type": "BTREE", "columns": ["metric_type"]}, {"name": "system_monitors_metric_name_index", "unique": false, "type": "BTREE", "columns": ["metric_name"]}, {"name": "system_monitors_source_index", "unique": false, "type": "BTREE", "columns": ["source"]}, {"name": "system_monitors_environment_index", "unique": false, "type": "BTREE", "columns": ["environment"]}, {"name": "system_monitors_status_index", "unique": false, "type": "BTREE", "columns": ["status"]}, {"name": "system_monitors_collected_at_index", "unique": false, "type": "BTREE", "columns": ["collected_at"]}, {"name": "system_monitors_metric_type_metric_name_index", "unique": false, "type": "BTREE", "columns": ["metric_type", "metric_name"]}, {"name": "system_monitors_source_collected_at_index", "unique": false, "type": "BTREE", "columns": ["source", "collected_at"]}, {"name": "system_monitors_status_collected_at_index", "unique": false, "type": "BTREE", "columns": ["status", "collected_at"]}, {"name": "system_monitors_environment_metric_type_index", "unique": false, "type": "BTREE", "columns": ["environment", "metric_type"]}], "foreign_keys": [], "row_count": 0, "engine": "InnoDB", "charset": "utf8mb4_unicode_ci", "created_at": "2025-07-27 18:05:27"}, {"name": "p_user_achievements", "comment": "", "fields": [{"name": "id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "", "extra": "auto_increment", "key": "PRI"}, {"name": "user_id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "用户ID", "extra": "", "key": "MUL"}, {"name": "achievement_id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "成就ID", "extra": "", "key": "MUL"}, {"name": "current_progress", "type": "int(11)", "nullable": false, "default": "0", "comment": "当前进度", "extra": "", "key": ""}, {"name": "is_unlocked", "type": "tinyint(1)", "nullable": false, "default": "0", "comment": "是否已解锁", "extra": "", "key": ""}, {"name": "is_notified", "type": "tinyint(1)", "nullable": false, "default": "0", "comment": "是否已通知", "extra": "", "key": ""}, {"name": "unlocked_at", "type": "timestamp", "nullable": true, "default": null, "comment": "解锁时间", "extra": "", "key": "MUL"}, {"name": "notified_at", "type": "timestamp", "nullable": true, "default": null, "comment": "通知时间", "extra": "", "key": ""}, {"name": "progress_data", "type": "json", "nullable": true, "default": null, "comment": "进度数据", "extra": "", "key": ""}, {"name": "metadata", "type": "json", "nullable": true, "default": null, "comment": "元数据", "extra": "", "key": ""}, {"name": "created_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}, {"name": "updated_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}], "indexes": [{"name": "PRIMARY", "unique": true, "type": "BTREE", "columns": ["id"]}, {"name": "user_achievements_user_id_achievement_id_unique", "unique": true, "type": "BTREE", "columns": ["user_id", "achievement_id"]}, {"name": "user_achievements_user_id_is_unlocked_index", "unique": false, "type": "BTREE", "columns": ["user_id", "is_unlocked"]}, {"name": "user_achievements_achievement_id_is_unlocked_index", "unique": false, "type": "BTREE", "columns": ["achievement_id", "is_unlocked"]}, {"name": "user_achievements_unlocked_at_index", "unique": false, "type": "BTREE", "columns": ["unlocked_at"]}], "foreign_keys": [{"constraint_name": "user_achievements_achievement_id_foreign", "column": "achievement_id", "referenced_table": "p_achievements", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}, {"constraint_name": "user_achievements_user_id_foreign", "column": "user_id", "referenced_table": "p_users", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}], "row_count": 0, "engine": "InnoDB", "charset": "utf8mb4_unicode_ci", "created_at": "2025-07-27 18:09:08"}, {"name": "p_user_character_bindings", "comment": "", "fields": [{"name": "id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "", "extra": "auto_increment", "key": "PRI"}, {"name": "user_id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "用户ID", "extra": "", "key": "MUL"}, {"name": "character_id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "角色ID", "extra": "", "key": "MUL"}, {"name": "binding_name", "type": "<PERSON><PERSON><PERSON>(100)", "nullable": true, "default": null, "comment": "绑定名称", "extra": "", "key": ""}, {"name": "binding_reason", "type": "<PERSON><PERSON><PERSON>(200)", "nullable": true, "default": null, "comment": "绑定原因", "extra": "", "key": ""}, {"name": "custom_description", "type": "text", "nullable": true, "default": null, "comment": "自定义描述", "extra": "", "key": ""}, {"name": "custom_config", "type": "json", "nullable": true, "default": null, "comment": "自定义配置", "extra": "", "key": ""}, {"name": "is_active", "type": "tinyint(1)", "nullable": false, "default": "1", "comment": "是否启用", "extra": "", "key": "MUL"}, {"name": "is_favorite", "type": "tinyint(1)", "nullable": false, "default": "0", "comment": "是否收藏", "extra": "", "key": "MUL"}, {"name": "usage_count", "type": "int(11)", "nullable": false, "default": "0", "comment": "使用次数", "extra": "", "key": "MUL"}, {"name": "last_used_at", "type": "timestamp", "nullable": true, "default": null, "comment": "最后使用时间", "extra": "", "key": "MUL"}, {"name": "user_rating", "type": "decimal(3,2)", "nullable": true, "default": null, "comment": "用户评分", "extra": "", "key": "MUL"}, {"name": "user_feedback", "type": "text", "nullable": true, "default": null, "comment": "用户反馈", "extra": "", "key": ""}, {"name": "usage_stats", "type": "json", "nullable": true, "default": null, "comment": "使用统计", "extra": "", "key": ""}, {"name": "created_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}, {"name": "updated_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}], "indexes": [{"name": "PRIMARY", "unique": true, "type": "BTREE", "columns": ["id"]}, {"name": "user_character_bindings_user_id_character_id_unique", "unique": true, "type": "BTREE", "columns": ["user_id", "character_id"]}, {"name": "user_character_bindings_user_id_index", "unique": false, "type": "BTREE", "columns": ["user_id"]}, {"name": "user_character_bindings_character_id_index", "unique": false, "type": "BTREE", "columns": ["character_id"]}, {"name": "user_character_bindings_is_active_index", "unique": false, "type": "BTREE", "columns": ["is_active"]}, {"name": "user_character_bindings_is_favorite_index", "unique": false, "type": "BTREE", "columns": ["is_favorite"]}, {"name": "user_character_bindings_usage_count_index", "unique": false, "type": "BTREE", "columns": ["usage_count"]}, {"name": "user_character_bindings_last_used_at_index", "unique": false, "type": "BTREE", "columns": ["last_used_at"]}, {"name": "user_character_bindings_user_rating_index", "unique": false, "type": "BTREE", "columns": ["user_rating"]}, {"name": "user_character_bindings_user_id_is_active_index", "unique": false, "type": "BTREE", "columns": ["user_id", "is_active"]}, {"name": "user_character_bindings_user_id_is_favorite_index", "unique": false, "type": "BTREE", "columns": ["user_id", "is_favorite"]}, {"name": "user_character_bindings_character_id_is_active_index", "unique": false, "type": "BTREE", "columns": ["character_id", "is_active"]}, {"name": "user_character_bindings_usage_count_last_used_at_index", "unique": false, "type": "BTREE", "columns": ["usage_count", "last_used_at"]}, {"name": "idx_user_binding_reason", "unique": false, "type": "BTREE", "columns": ["user_id", "binding_reason"]}], "foreign_keys": [{"constraint_name": "user_character_bindings_character_id_foreign", "column": "character_id", "referenced_table": "p_character_library", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}, {"constraint_name": "user_character_bindings_user_id_foreign", "column": "user_id", "referenced_table": "p_users", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}], "row_count": 0, "engine": "InnoDB", "charset": "utf8mb4_unicode_ci", "created_at": "2025-07-27 18:05:30"}, {"name": "p_user_daily_tasks", "comment": "", "fields": [{"name": "id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "", "extra": "auto_increment", "key": "PRI"}, {"name": "user_id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "用户ID", "extra": "", "key": "MUL"}, {"name": "daily_task_id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "每日任务ID", "extra": "", "key": "MUL"}, {"name": "current_progress", "type": "int(11)", "nullable": false, "default": "0", "comment": "当前进度", "extra": "", "key": ""}, {"name": "target_progress", "type": "int(11)", "nullable": false, "default": null, "comment": "目标进度", "extra": "", "key": ""}, {"name": "is_completed", "type": "tinyint(1)", "nullable": false, "default": "0", "comment": "是否完成", "extra": "", "key": "MUL"}, {"name": "is_reward_claimed", "type": "tinyint(1)", "nullable": false, "default": "0", "comment": "是否已领取奖励", "extra": "", "key": "MUL"}, {"name": "completed_at", "type": "timestamp", "nullable": true, "default": null, "comment": "完成时间", "extra": "", "key": ""}, {"name": "reward_claimed_at", "type": "timestamp", "nullable": true, "default": null, "comment": "奖励领取时间", "extra": "", "key": ""}, {"name": "task_date", "type": "date", "nullable": false, "default": null, "comment": "任务日期", "extra": "", "key": ""}, {"name": "progress_data", "type": "json", "nullable": true, "default": null, "comment": "进度数据", "extra": "", "key": ""}, {"name": "metadata", "type": "json", "nullable": true, "default": null, "comment": "元数据", "extra": "", "key": ""}, {"name": "created_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}, {"name": "updated_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}], "indexes": [{"name": "PRIMARY", "unique": true, "type": "BTREE", "columns": ["id"]}, {"name": "user_daily_tasks_user_id_daily_task_id_task_date_unique", "unique": true, "type": "BTREE", "columns": ["user_id", "daily_task_id", "task_date"]}, {"name": "user_daily_tasks_user_id_task_date_index", "unique": false, "type": "BTREE", "columns": ["user_id", "task_date"]}, {"name": "user_daily_tasks_daily_task_id_task_date_index", "unique": false, "type": "BTREE", "columns": ["daily_task_id", "task_date"]}, {"name": "user_daily_tasks_is_completed_task_date_index", "unique": false, "type": "BTREE", "columns": ["is_completed", "task_date"]}, {"name": "user_daily_tasks_is_reward_claimed_task_date_index", "unique": false, "type": "BTREE", "columns": ["is_reward_claimed", "task_date"]}], "foreign_keys": [{"constraint_name": "user_daily_tasks_daily_task_id_foreign", "column": "daily_task_id", "referenced_table": "p_daily_tasks", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}, {"constraint_name": "user_daily_tasks_user_id_foreign", "column": "user_id", "referenced_table": "p_users", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}], "row_count": 0, "engine": "InnoDB", "charset": "utf8mb4_unicode_ci", "created_at": "2025-07-27 18:09:08"}, {"name": "p_user_files", "comment": "", "fields": [{"name": "id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "", "extra": "auto_increment", "key": "PRI"}, {"name": "user_id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "用户ID", "extra": "", "key": "MUL"}, {"name": "filename", "type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "comment": "文件名", "extra": "", "key": ""}, {"name": "original_name", "type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "comment": "原始文件名", "extra": "", "key": ""}, {"name": "file_path", "type": "var<PERSON><PERSON>(500)", "nullable": false, "default": null, "comment": "文件路径", "extra": "", "key": ""}, {"name": "file_url", "type": "var<PERSON><PERSON>(500)", "nullable": true, "default": null, "comment": "文件访问URL", "extra": "", "key": ""}, {"name": "file_type", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": false, "default": null, "comment": "文件类型", "extra": "", "key": "MUL"}, {"name": "mime_type", "type": "<PERSON><PERSON><PERSON>(100)", "nullable": false, "default": null, "comment": "MIME类型", "extra": "", "key": "MUL"}, {"name": "file_size", "type": "bigint(20)", "nullable": false, "default": null, "comment": "文件大小(字节)", "extra": "", "key": ""}, {"name": "file_hash", "type": "<PERSON><PERSON><PERSON>(64)", "nullable": true, "default": null, "comment": "文件哈希值", "extra": "", "key": "MUL"}, {"name": "storage_driver", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": false, "default": "local", "comment": "存储驱动", "extra": "", "key": "MUL"}, {"name": "folder_path", "type": "var<PERSON><PERSON>(500)", "nullable": true, "default": null, "comment": "文件夹路径", "extra": "", "key": "MUL"}, {"name": "metadata", "type": "json", "nullable": true, "default": null, "comment": "文件元数据", "extra": "", "key": ""}, {"name": "thumbnails", "type": "json", "nullable": true, "default": null, "comment": "缩略图信息", "extra": "", "key": ""}, {"name": "is_public", "type": "tinyint(1)", "nullable": false, "default": "0", "comment": "是否公开", "extra": "", "key": "MUL"}, {"name": "is_temporary", "type": "tinyint(1)", "nullable": false, "default": "0", "comment": "是否临时文件", "extra": "", "key": "MUL"}, {"name": "download_count", "type": "int(11)", "nullable": false, "default": "0", "comment": "下载次数", "extra": "", "key": ""}, {"name": "last_accessed_at", "type": "timestamp", "nullable": true, "default": null, "comment": "最后访问时间", "extra": "", "key": ""}, {"name": "expires_at", "type": "timestamp", "nullable": true, "default": null, "comment": "过期时间", "extra": "", "key": "MUL"}, {"name": "created_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": "MUL"}, {"name": "updated_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}], "indexes": [{"name": "PRIMARY", "unique": true, "type": "BTREE", "columns": ["id"]}, {"name": "user_files_user_id_index", "unique": false, "type": "BTREE", "columns": ["user_id"]}, {"name": "user_files_file_type_index", "unique": false, "type": "BTREE", "columns": ["file_type"]}, {"name": "user_files_mime_type_index", "unique": false, "type": "BTREE", "columns": ["mime_type"]}, {"name": "user_files_file_hash_index", "unique": false, "type": "BTREE", "columns": ["file_hash"]}, {"name": "user_files_storage_driver_index", "unique": false, "type": "BTREE", "columns": ["storage_driver"]}, {"name": "user_files_folder_path_index", "unique": false, "type": "BTREE", "columns": ["folder_path"]}, {"name": "user_files_is_public_index", "unique": false, "type": "BTREE", "columns": ["is_public"]}, {"name": "user_files_is_temporary_index", "unique": false, "type": "BTREE", "columns": ["is_temporary"]}, {"name": "user_files_created_at_index", "unique": false, "type": "BTREE", "columns": ["created_at"]}, {"name": "user_files_expires_at_index", "unique": false, "type": "BTREE", "columns": ["expires_at"]}, {"name": "user_files_user_id_file_type_index", "unique": false, "type": "BTREE", "columns": ["user_id", "file_type"]}, {"name": "user_files_user_id_folder_path_index", "unique": false, "type": "BTREE", "columns": ["user_id", "folder_path"]}, {"name": "user_files_is_temporary_expires_at_index", "unique": false, "type": "BTREE", "columns": ["is_temporary", "expires_at"]}], "foreign_keys": [{"constraint_name": "user_files_user_id_foreign", "column": "user_id", "referenced_table": "p_users", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}], "row_count": 0, "engine": "InnoDB", "charset": "utf8mb4_unicode_ci", "created_at": "2025-07-27 18:05:27"}, {"name": "p_user_levels", "comment": "", "fields": [{"name": "id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "", "extra": "auto_increment", "key": "PRI"}, {"name": "level", "type": "int(11)", "nullable": false, "default": null, "comment": "等级数值", "extra": "", "key": "UNI"}, {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "nullable": false, "default": null, "comment": "等级名称", "extra": "", "key": ""}, {"name": "description", "type": "text", "nullable": true, "default": null, "comment": "等级描述", "extra": "", "key": ""}, {"name": "type", "type": "enum('normal','vip','special','milestone')", "nullable": false, "default": "normal", "comment": "等级类型", "extra": "", "key": "MUL"}, {"name": "required_experience", "type": "bigint(20)", "nullable": false, "default": "0", "comment": "所需经验值", "extra": "", "key": "MUL"}, {"name": "icon", "type": "<PERSON><PERSON><PERSON>(255)", "nullable": true, "default": null, "comment": "等级图标", "extra": "", "key": ""}, {"name": "color", "type": "<PERSON><PERSON><PERSON>(20)", "nullable": true, "default": null, "comment": "等级颜色", "extra": "", "key": ""}, {"name": "badge", "type": "<PERSON><PERSON><PERSON>(255)", "nullable": true, "default": null, "comment": "等级徽章", "extra": "", "key": ""}, {"name": "privileges", "type": "json", "nullable": true, "default": null, "comment": "等级特权", "extra": "", "key": ""}, {"name": "rewards", "type": "json", "nullable": true, "default": null, "comment": "等级奖励", "extra": "", "key": ""}, {"name": "unlock_features", "type": "json", "nullable": true, "default": null, "comment": "解锁功能", "extra": "", "key": ""}, {"name": "is_active", "type": "tinyint(1)", "nullable": false, "default": "1", "comment": "是否启用", "extra": "", "key": ""}, {"name": "sort_order", "type": "int(11)", "nullable": false, "default": "0", "comment": "排序", "extra": "", "key": ""}, {"name": "metadata", "type": "json", "nullable": true, "default": null, "comment": "元数据", "extra": "", "key": ""}, {"name": "created_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}, {"name": "updated_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}], "indexes": [{"name": "PRIMARY", "unique": true, "type": "BTREE", "columns": ["id"]}, {"name": "user_levels_level_unique", "unique": true, "type": "BTREE", "columns": ["level"]}, {"name": "user_levels_level_is_active_index", "unique": false, "type": "BTREE", "columns": ["level", "is_active"]}, {"name": "user_levels_required_experience_index", "unique": false, "type": "BTREE", "columns": ["required_experience"]}, {"name": "user_levels_type_index", "unique": false, "type": "BTREE", "columns": ["type"]}], "foreign_keys": [], "row_count": 0, "engine": "InnoDB", "charset": "utf8mb4_unicode_ci", "created_at": "2025-07-27 18:08:10"}, {"name": "p_user_model_preferences", "comment": "", "fields": [{"name": "id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "主键ID", "extra": "auto_increment", "key": "PRI"}, {"name": "user_id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "用户ID", "extra": "", "key": "MUL"}, {"name": "business_type", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": false, "default": null, "comment": "业务类型", "extra": "", "key": ""}, {"name": "preferred_platform", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": false, "default": null, "comment": "首选平台", "extra": "", "key": "MUL"}, {"name": "platform_priorities", "type": "json", "nullable": false, "default": null, "comment": "平台优先级配置", "extra": "", "key": ""}, {"name": "selection_criteria", "type": "json", "nullable": false, "default": null, "comment": "选择标准", "extra": "", "key": ""}, {"name": "auto_fallback", "type": "tinyint(1)", "nullable": false, "default": "1", "comment": "是否自动降级", "extra": "", "key": ""}, {"name": "cost_optimization", "type": "tinyint(1)", "nullable": false, "default": "0", "comment": "是否成本优化", "extra": "", "key": ""}, {"name": "custom_config", "type": "json", "nullable": true, "default": null, "comment": "自定义配置", "extra": "", "key": ""}, {"name": "usage_count", "type": "int(11)", "nullable": false, "default": "0", "comment": "使用次数", "extra": "", "key": ""}, {"name": "last_used_at", "type": "timestamp", "nullable": true, "default": null, "comment": "最后使用时间", "extra": "", "key": "MUL"}, {"name": "deleted_at", "type": "timestamp", "nullable": true, "default": null, "comment": "软删除时间", "extra": "", "key": "MUL"}, {"name": "created_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}, {"name": "updated_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}], "indexes": [{"name": "PRIMARY", "unique": true, "type": "BTREE", "columns": ["id"]}, {"name": "uk_user_business", "unique": true, "type": "BTREE", "columns": ["user_id", "business_type"]}, {"name": "idx_preferred_platform", "unique": false, "type": "BTREE", "columns": ["preferred_platform"]}, {"name": "idx_last_used", "unique": false, "type": "BTREE", "columns": ["last_used_at"]}, {"name": "idx_deleted_at", "unique": false, "type": "BTREE", "columns": ["deleted_at"]}], "foreign_keys": [{"constraint_name": "user_model_preferences_user_id_foreign", "column": "user_id", "referenced_table": "p_users", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}], "row_count": 0, "engine": "InnoDB", "charset": "utf8mb4_unicode_ci", "created_at": "2025-07-29 03:24:18"}, {"name": "p_user_preferences", "comment": "", "fields": [{"name": "id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "", "extra": "auto_increment", "key": "PRI"}, {"name": "user_id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "用户ID", "extra": "", "key": "UNI"}, {"name": "language", "type": "<PERSON><PERSON><PERSON>(10)", "nullable": false, "default": "zh-CN", "comment": "语言偏好", "extra": "", "key": "MUL"}, {"name": "timezone", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": false, "default": "Asia/Shanghai", "comment": "时区", "extra": "", "key": ""}, {"name": "email_notifications", "type": "tinyint(1)", "nullable": false, "default": "1", "comment": "邮件通知", "extra": "", "key": ""}, {"name": "push_notifications", "type": "tinyint(1)", "nullable": false, "default": "1", "comment": "推送通知", "extra": "", "key": ""}, {"name": "ai_preferences", "type": "json", "nullable": true, "default": null, "comment": "AI偏好设置", "extra": "", "key": ""}, {"name": "ui_preferences", "type": "json", "nullable": true, "default": null, "comment": "UI偏好设置", "extra": "", "key": ""}, {"name": "workflow_preferences", "type": "json", "nullable": true, "default": null, "comment": "工作流偏好", "extra": "", "key": ""}, {"name": "default_ai_model", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": true, "default": null, "comment": "默认AI模型", "extra": "", "key": "MUL"}, {"name": "auto_save_interval", "type": "int(11)", "nullable": false, "default": "30", "comment": "自动保存间隔(秒)", "extra": "", "key": ""}, {"name": "show_tutorials", "type": "tinyint(1)", "nullable": false, "default": "1", "comment": "显示教程", "extra": "", "key": ""}, {"name": "created_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}, {"name": "updated_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}], "indexes": [{"name": "PRIMARY", "unique": true, "type": "BTREE", "columns": ["id"]}, {"name": "user_preferences_user_id_unique", "unique": true, "type": "BTREE", "columns": ["user_id"]}, {"name": "user_preferences_language_index", "unique": false, "type": "BTREE", "columns": ["language"]}, {"name": "user_preferences_default_ai_model_index", "unique": false, "type": "BTREE", "columns": ["default_ai_model"]}], "foreign_keys": [{"constraint_name": "user_preferences_user_id_foreign", "column": "user_id", "referenced_table": "p_users", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}], "row_count": 0, "engine": "InnoDB", "charset": "utf8mb4_unicode_ci", "created_at": "2025-07-27 18:05:24"}, {"name": "p_user_works", "comment": "", "fields": [{"name": "id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "", "extra": "auto_increment", "key": "PRI"}, {"name": "user_id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "用户ID", "extra": "", "key": "MUL"}, {"name": "work_title", "type": "<PERSON><PERSON><PERSON>(200)", "nullable": false, "default": null, "comment": "作品标题", "extra": "", "key": ""}, {"name": "work_description", "type": "text", "nullable": true, "default": null, "comment": "作品描述", "extra": "", "key": ""}, {"name": "work_file_path", "type": "var<PERSON><PERSON>(500)", "nullable": false, "default": null, "comment": "作品文件路径", "extra": "", "key": ""}, {"name": "work_thumbnail", "type": "var<PERSON><PERSON>(500)", "nullable": true, "default": null, "comment": "作品缩略图", "extra": "", "key": ""}, {"name": "publish_status", "type": "enum('draft','published','private')", "nullable": false, "default": "draft", "comment": "", "extra": "", "key": ""}, {"name": "work_metadata", "type": "json", "nullable": true, "default": null, "comment": "作品元数据", "extra": "", "key": ""}, {"name": "view_count", "type": "int(11)", "nullable": false, "default": "0", "comment": "观看次数", "extra": "", "key": ""}, {"name": "like_count", "type": "int(11)", "nullable": false, "default": "0", "comment": "点赞次数", "extra": "", "key": ""}, {"name": "share_count", "type": "int(11)", "nullable": false, "default": "0", "comment": "分享次数", "extra": "", "key": ""}, {"name": "published_at", "type": "timestamp", "nullable": true, "default": null, "comment": "发布时间", "extra": "", "key": "MUL"}, {"name": "created_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}, {"name": "updated_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}], "indexes": [{"name": "PRIMARY", "unique": true, "type": "BTREE", "columns": ["id"]}, {"name": "user_works_user_id_publish_status_index", "unique": false, "type": "BTREE", "columns": ["user_id", "publish_status"]}, {"name": "user_works_published_at_view_count_index", "unique": false, "type": "BTREE", "columns": ["published_at", "view_count"]}], "foreign_keys": [{"constraint_name": "user_works_user_id_foreign", "column": "user_id", "referenced_table": "p_users", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}], "row_count": 0, "engine": "InnoDB", "charset": "utf8mb4_unicode_ci", "created_at": "2025-07-27 18:05:30"}, {"name": "p_users", "comment": "", "fields": [{"name": "id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "", "extra": "auto_increment", "key": "PRI"}, {"name": "username", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": false, "default": null, "comment": "用户名", "extra": "", "key": "UNI"}, {"name": "password", "type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "comment": "密码", "extra": "", "key": ""}, {"name": "email", "type": "<PERSON><PERSON><PERSON>(100)", "nullable": true, "default": null, "comment": "邮箱", "extra": "", "key": "UNI"}, {"name": "nickname", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": true, "default": null, "comment": "昵称", "extra": "", "key": ""}, {"name": "level", "type": "int(11)", "nullable": true, "default": "1", "comment": "用户等级", "extra": "", "key": ""}, {"name": "experience", "type": "int(11)", "nullable": true, "default": "0", "comment": "用户经验值", "extra": "", "key": ""}, {"name": "avatar", "type": "<PERSON><PERSON><PERSON>(255)", "nullable": true, "default": null, "comment": "头像URL", "extra": "", "key": ""}, {"name": "bio", "type": "text", "nullable": true, "default": null, "comment": "用户简介", "extra": "", "key": ""}, {"name": "follower_count", "type": "int(11)", "nullable": true, "default": "0", "comment": "粉丝数量", "extra": "", "key": ""}, {"name": "following_count", "type": "int(11)", "nullable": true, "default": "0", "comment": "关注数量", "extra": "", "key": ""}, {"name": "inviter_id", "type": "bigint(20) unsigned", "nullable": true, "default": null, "comment": "邀请人ID", "extra": "", "key": "MUL"}, {"name": "remark", "type": "<PERSON><PERSON><PERSON>(255)", "nullable": true, "default": null, "comment": "备注", "extra": "", "key": ""}, {"name": "status", "type": "<PERSON><PERSON>(4)", "nullable": false, "default": "1", "comment": "状态：0=禁用，1=启用", "extra": "", "key": "MUL"}, {"name": "points", "type": "decimal(10,2)", "nullable": false, "default": "0.00", "comment": "积分余额", "extra": "", "key": ""}, {"name": "frozen_points", "type": "decimal(10,2)", "nullable": false, "default": "0.00", "comment": "冻结积分", "extra": "", "key": ""}, {"name": "is_vip", "type": "tinyint(1)", "nullable": false, "default": "0", "comment": "是否VIP", "extra": "", "key": "MUL"}, {"name": "vip_expires_at", "type": "timestamp", "nullable": true, "default": null, "comment": "VIP过期时间", "extra": "", "key": ""}, {"name": "last_login_ip", "type": "<PERSON><PERSON><PERSON>(45)", "nullable": true, "default": null, "comment": "最后登录IP", "extra": "", "key": ""}, {"name": "last_login_at", "type": "timestamp", "nullable": true, "default": null, "comment": "最后登录时间", "extra": "", "key": ""}, {"name": "created_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": "MUL"}, {"name": "updated_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}], "indexes": [{"name": "PRIMARY", "unique": true, "type": "BTREE", "columns": ["id"]}, {"name": "users_username_unique", "unique": true, "type": "BTREE", "columns": ["username"]}, {"name": "users_email_unique", "unique": true, "type": "BTREE", "columns": ["email"]}, {"name": "users_username_index", "unique": false, "type": "BTREE", "columns": ["username"]}, {"name": "users_email_index", "unique": false, "type": "BTREE", "columns": ["email"]}, {"name": "users_status_index", "unique": false, "type": "BTREE", "columns": ["status"]}, {"name": "users_is_vip_index", "unique": false, "type": "BTREE", "columns": ["is_vip"]}, {"name": "users_inviter_id_index", "unique": false, "type": "BTREE", "columns": ["inviter_id"]}, {"name": "users_created_at_index", "unique": false, "type": "BTREE", "columns": ["created_at"]}], "foreign_keys": [], "row_count": 12, "engine": "InnoDB", "charset": "utf8mb4_unicode_ci", "created_at": "2025-08-03 13:59:50"}, {"name": "p_websocket_sessions", "comment": "", "fields": [{"name": "id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "", "extra": "auto_increment", "key": "PRI"}, {"name": "session_id", "type": "<PERSON><PERSON><PERSON>(100)", "nullable": false, "default": null, "comment": "会话ID", "extra": "", "key": "UNI"}, {"name": "user_id", "type": "bigint(20) unsigned", "nullable": true, "default": null, "comment": "用户ID", "extra": "", "key": "MUL"}, {"name": "client_type", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": false, "default": "unknown", "comment": "客户端类型", "extra": "", "key": "MUL"}, {"name": "client_version", "type": "<PERSON><PERSON><PERSON>(20)", "nullable": true, "default": null, "comment": "客户端版本", "extra": "", "key": ""}, {"name": "connection_ip", "type": "<PERSON><PERSON><PERSON>(45)", "nullable": false, "default": null, "comment": "连接IP", "extra": "", "key": ""}, {"name": "user_agent", "type": "var<PERSON><PERSON>(500)", "nullable": true, "default": null, "comment": "用户代理", "extra": "", "key": ""}, {"name": "status", "type": "enum('connected','disconnected','timeout')", "nullable": false, "default": "connected", "comment": "连接状态", "extra": "", "key": "MUL"}, {"name": "connection_info", "type": "json", "nullable": true, "default": null, "comment": "连接信息", "extra": "", "key": ""}, {"name": "subscribed_events", "type": "json", "nullable": true, "default": null, "comment": "订阅的事件类型", "extra": "", "key": ""}, {"name": "connected_at", "type": "timestamp", "nullable": false, "default": null, "comment": "连接时间", "extra": "", "key": "MUL"}, {"name": "last_ping_at", "type": "timestamp", "nullable": true, "default": null, "comment": "最后心跳时间", "extra": "", "key": "MUL"}, {"name": "disconnected_at", "type": "timestamp", "nullable": true, "default": null, "comment": "断开连接时间", "extra": "", "key": ""}, {"name": "message_count", "type": "int(11)", "nullable": false, "default": "0", "comment": "消息数量", "extra": "", "key": ""}, {"name": "disconnect_reason", "type": "text", "nullable": true, "default": null, "comment": "断开原因", "extra": "", "key": ""}, {"name": "created_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}, {"name": "updated_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}], "indexes": [{"name": "PRIMARY", "unique": true, "type": "BTREE", "columns": ["id"]}, {"name": "websocket_sessions_session_id_unique", "unique": true, "type": "BTREE", "columns": ["session_id"]}, {"name": "websocket_sessions_session_id_index", "unique": false, "type": "BTREE", "columns": ["session_id"]}, {"name": "websocket_sessions_user_id_index", "unique": false, "type": "BTREE", "columns": ["user_id"]}, {"name": "websocket_sessions_client_type_index", "unique": false, "type": "BTREE", "columns": ["client_type"]}, {"name": "websocket_sessions_status_index", "unique": false, "type": "BTREE", "columns": ["status"]}, {"name": "websocket_sessions_connected_at_index", "unique": false, "type": "BTREE", "columns": ["connected_at"]}, {"name": "websocket_sessions_last_ping_at_index", "unique": false, "type": "BTREE", "columns": ["last_ping_at"]}, {"name": "websocket_sessions_user_id_status_index", "unique": false, "type": "BTREE", "columns": ["user_id", "status"]}, {"name": "websocket_sessions_client_type_status_index", "unique": false, "type": "BTREE", "columns": ["client_type", "status"]}, {"name": "websocket_sessions_status_connected_at_index", "unique": false, "type": "BTREE", "columns": ["status", "connected_at"]}], "foreign_keys": [{"constraint_name": "websocket_sessions_user_id_foreign", "column": "user_id", "referenced_table": "p_users", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "SET NULL"}], "row_count": 0, "engine": "InnoDB", "charset": "utf8mb4_unicode_ci", "created_at": "2025-07-27 18:05:26"}, {"name": "p_work_interactions", "comment": "", "fields": [{"name": "id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "", "extra": "auto_increment", "key": "PRI"}, {"name": "work_id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "作品ID", "extra": "", "key": "MUL"}, {"name": "user_id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "用户ID", "extra": "", "key": "MUL"}, {"name": "interaction_type", "type": "enum('like','view','share','comment')", "nullable": false, "default": null, "comment": "互动类型", "extra": "", "key": ""}, {"name": "interaction_content", "type": "text", "nullable": true, "default": null, "comment": "互动内容", "extra": "", "key": ""}, {"name": "interaction_metadata", "type": "var<PERSON><PERSON>(500)", "nullable": true, "default": null, "comment": "互动元数据", "extra": "", "key": ""}, {"name": "created_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}, {"name": "updated_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}], "indexes": [{"name": "PRIMARY", "unique": true, "type": "BTREE", "columns": ["id"]}, {"name": "unique_work_user_interaction", "unique": true, "type": "BTREE", "columns": ["work_id", "user_id", "interaction_type"]}, {"name": "work_interactions_work_id_interaction_type_index", "unique": false, "type": "BTREE", "columns": ["work_id", "interaction_type"]}, {"name": "work_interactions_user_id_interaction_type_index", "unique": false, "type": "BTREE", "columns": ["user_id", "interaction_type"]}], "foreign_keys": [{"constraint_name": "work_interactions_work_id_foreign", "column": "work_id", "referenced_table": "p_work_plaza", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}, {"constraint_name": "work_interactions_user_id_foreign", "column": "user_id", "referenced_table": "p_users", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}], "row_count": 0, "engine": "InnoDB", "charset": "utf8mb4_unicode_ci", "created_at": "2025-07-27 18:05:30"}, {"name": "p_work_plaza", "comment": "", "fields": [{"name": "id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "", "extra": "auto_increment", "key": "PRI"}, {"name": "work_uuid", "type": "<PERSON><PERSON><PERSON>(36)", "nullable": false, "default": null, "comment": "作品唯一标识", "extra": "", "key": "UNI"}, {"name": "user_id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "发布用户ID", "extra": "", "key": "MUL"}, {"name": "work_type", "type": "enum('character','style','background_music','voice_tone','scene_sound','composite_video')", "nullable": false, "default": null, "comment": "作品类型：角色/风格/背景音乐/角色配音音色/场景事件音效/合成视频", "extra": "", "key": "MUL"}, {"name": "file_name", "type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "comment": "原始文件名", "extra": "", "key": ""}, {"name": "file_extension", "type": "<PERSON><PERSON><PERSON>(10)", "nullable": false, "default": null, "comment": "文件扩展名", "extra": "", "key": ""}, {"name": "file_size", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "文件大小(字节)", "extra": "", "key": ""}, {"name": "mime_type", "type": "<PERSON><PERSON><PERSON>(100)", "nullable": false, "default": null, "comment": "MIME类型", "extra": "", "key": ""}, {"name": "file_path", "type": "var<PERSON><PERSON>(500)", "nullable": false, "default": null, "comment": "文件存储路径", "extra": "", "key": ""}, {"name": "work_title", "type": "<PERSON><PERSON><PERSON>(200)", "nullable": false, "default": null, "comment": "作品标题", "extra": "", "key": ""}, {"name": "work_description", "type": "text", "nullable": true, "default": null, "comment": "作品描述", "extra": "", "key": ""}, {"name": "work_tags", "type": "json", "nullable": true, "default": null, "comment": "作品标签", "extra": "", "key": ""}, {"name": "source_resource_id", "type": "bigint(20) unsigned", "nullable": true, "default": null, "comment": "来源资源ID", "extra": "", "key": "MUL"}, {"name": "source_module_id", "type": "bigint(20) unsigned", "nullable": true, "default": null, "comment": "来源模块内容ID", "extra": "", "key": ""}, {"name": "status", "type": "<PERSON><PERSON>(4)", "nullable": false, "default": "0", "comment": "状态：0=机审，1=人审", "extra": "", "key": "MUL"}, {"name": "auto_review_result", "type": "enum('approved','rejected')", "nullable": true, "default": null, "comment": "机审结果", "extra": "", "key": ""}, {"name": "auto_review_reason", "type": "text", "nullable": true, "default": null, "comment": "机审依据", "extra": "", "key": ""}, {"name": "manual_reviewer_id", "type": "bigint(20) unsigned", "nullable": true, "default": null, "comment": "人工审核人ID", "extra": "", "key": "MUL"}, {"name": "manual_reviewed_at", "type": "timestamp", "nullable": true, "default": null, "comment": "人工审核时间", "extra": "", "key": ""}, {"name": "manual_review_notes", "type": "text", "nullable": true, "default": null, "comment": "人工审核备注", "extra": "", "key": ""}, {"name": "publish_status", "type": "enum('draft','published','hidden','deleted')", "nullable": false, "default": "draft", "comment": "发布状态", "extra": "", "key": ""}, {"name": "published_at", "type": "timestamp", "nullable": true, "default": null, "comment": "发布时间", "extra": "", "key": "MUL"}, {"name": "view_count", "type": "int(11)", "nullable": false, "default": "0", "comment": "浏览次数", "extra": "", "key": ""}, {"name": "like_count", "type": "int(11)", "nullable": false, "default": "0", "comment": "点赞次数", "extra": "", "key": ""}, {"name": "share_count", "type": "int(11)", "nullable": false, "default": "0", "comment": "分享次数", "extra": "", "key": ""}, {"name": "download_count", "type": "int(11)", "nullable": false, "default": "0", "comment": "下载次数", "extra": "", "key": ""}, {"name": "comment_count", "type": "int(11)", "nullable": false, "default": "0", "comment": "评论次数", "extra": "", "key": ""}, {"name": "recommendation_score", "type": "decimal(8,2)", "nullable": false, "default": "0.00", "comment": "推荐权重分数", "extra": "", "key": ""}, {"name": "is_featured", "type": "tinyint(1)", "nullable": false, "default": "0", "comment": "是否精选", "extra": "", "key": "MUL"}, {"name": "report_count", "type": "int(11)", "nullable": false, "default": "0", "comment": "举报次数", "extra": "", "key": ""}, {"name": "created_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}, {"name": "updated_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}, {"name": "deleted_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}], "indexes": [{"name": "PRIMARY", "unique": true, "type": "BTREE", "columns": ["id"]}, {"name": "work_plaza_work_uuid_unique", "unique": true, "type": "BTREE", "columns": ["work_uuid"]}, {"name": "work_plaza_user_id_publish_status_index", "unique": false, "type": "BTREE", "columns": ["user_id", "publish_status"]}, {"name": "work_plaza_work_type_publish_status_index", "unique": false, "type": "BTREE", "columns": ["work_type", "publish_status"]}, {"name": "work_plaza_status_auto_review_result_index", "unique": false, "type": "BTREE", "columns": ["status", "auto_review_result"]}, {"name": "work_plaza_published_at_recommendation_score_index", "unique": false, "type": "BTREE", "columns": ["published_at", "recommendation_score"]}, {"name": "work_plaza_is_featured_publish_status_index", "unique": false, "type": "BTREE", "columns": ["is_featured", "publish_status"]}, {"name": "work_plaza_source_resource_id_foreign", "unique": false, "type": "BTREE", "columns": ["source_resource_id"]}, {"name": "work_plaza_manual_reviewer_id_foreign", "unique": false, "type": "BTREE", "columns": ["manual_reviewer_id"]}], "foreign_keys": [{"constraint_name": "work_plaza_manual_reviewer_id_foreign", "column": "manual_reviewer_id", "referenced_table": "p_users", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "SET NULL"}, {"constraint_name": "work_plaza_source_resource_id_foreign", "column": "source_resource_id", "referenced_table": "p_ai_resources", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "SET NULL"}, {"constraint_name": "work_plaza_user_id_foreign", "column": "user_id", "referenced_table": "p_users", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}], "row_count": 0, "engine": "InnoDB", "charset": "utf8mb4_unicode_ci", "created_at": "2025-07-27 18:05:30"}, {"name": "p_work_shares", "comment": "", "fields": [{"name": "id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "", "extra": "auto_increment", "key": "PRI"}, {"name": "work_id", "type": "bigint(20) unsigned", "nullable": false, "default": null, "comment": "作品ID", "extra": "", "key": "MUL"}, {"name": "share_token", "type": "<PERSON><PERSON><PERSON>(100)", "nullable": false, "default": null, "comment": "分享令牌", "extra": "", "key": "UNI"}, {"name": "share_type", "type": "enum('public','private','password')", "nullable": false, "default": "public", "comment": "", "extra": "", "key": ""}, {"name": "share_password", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": true, "default": null, "comment": "分享密码", "extra": "", "key": ""}, {"name": "expires_at", "type": "timestamp", "nullable": true, "default": null, "comment": "过期时间", "extra": "", "key": ""}, {"name": "access_count", "type": "int(11)", "nullable": false, "default": "0", "comment": "访问次数", "extra": "", "key": ""}, {"name": "max_access_count", "type": "int(11)", "nullable": true, "default": null, "comment": "最大访问次数", "extra": "", "key": ""}, {"name": "is_active", "type": "tinyint(1)", "nullable": false, "default": "1", "comment": "是否激活", "extra": "", "key": ""}, {"name": "created_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}, {"name": "updated_at", "type": "timestamp", "nullable": true, "default": null, "comment": "", "extra": "", "key": ""}], "indexes": [{"name": "PRIMARY", "unique": true, "type": "BTREE", "columns": ["id"]}, {"name": "work_shares_share_token_unique", "unique": true, "type": "BTREE", "columns": ["share_token"]}, {"name": "work_shares_work_id_is_active_index", "unique": false, "type": "BTREE", "columns": ["work_id", "is_active"]}, {"name": "work_shares_share_token_expires_at_index", "unique": false, "type": "BTREE", "columns": ["share_token", "expires_at"]}], "foreign_keys": [{"constraint_name": "work_shares_work_id_foreign", "column": "work_id", "referenced_table": "p_work_plaza", "referenced_column": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}], "row_count": 0, "engine": "InnoDB", "charset": "utf8mb4_unicode_ci", "created_at": "2025-07-27 18:05:30"}]}