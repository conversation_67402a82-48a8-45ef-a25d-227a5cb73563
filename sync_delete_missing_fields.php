<?php
/**
 * 同步删除三个文档和代码中的缺失字段引用
 */

echo "🔍 同步删除缺失字段引用\n";
echo str_repeat("=", 60) . "\n\n";

// 需要删除的字段映射
$fieldsToDelete = [
    'available_points' => 'points',  // 替代字段
    'consumed_at' => 'released_at',  // 替代字段
    'consume_reason' => 'reason',    // 替代字段
    'returned_at' => 'released_at',  // 替代字段
    'return_reason' => 'reason',     // 替代字段
    'provider' => 'platform'         // 替代字段
];

// 需要检查的文件
$filesToCheck = [
    '.cursor/rules/index-new.mdc',
    '.cursor/rules/dev-api-guidelines-database.mdc',
    '.cursor/rules/dev-api-guidelines-add.mdc',
    'php/api/app/Http/Controllers/PyApi/PointsController.php',
    'php/api/app/Services/PyApi/PointsService.php',
    'php/api/app/Models/PointsTransaction.php',
    'php/api/app/Models/PointsFreeze.php',
    'php/api/app/Models/AiModelConfig.php'
];

$analysisResults = [];

echo "📋 1. 分析需要修改的文件\n";
echo "-------------------\n";

foreach ($filesToCheck as $file) {
    if (!file_exists($file)) {
        echo "❌ 文件不存在: $file\n";
        continue;
    }
    
    echo "✅ 检查文件: " . basename($file) . "\n";
    $content = file_get_contents($file);
    $lines = explode("\n", $content);
    
    $fileChanges = [];
    
    foreach ($lines as $lineNum => $line) {
        $originalLine = $line;
        $needsChange = false;
        $changes = [];
        
        foreach ($fieldsToDelete as $oldField => $newField) {
            // 检查是否包含需要删除的字段
            if (strpos($line, $oldField) !== false) {
                // 判断是否是数据库字段引用（而不是注释或其他用途）
                if (strpos($line, "'$oldField'") !== false ||
                    strpos($line, "\"$oldField\"") !== false ||
                    strpos($line, "->$oldField") !== false ||
                    strpos($line, "[$oldField]") !== false ||
                    strpos($line, "$oldField =>") !== false ||
                    strpos($line, "table('$oldField')") !== false) {
                    
                    $needsChange = true;
                    $changes[] = [
                        'old_field' => $oldField,
                        'new_field' => $newField,
                        'type' => 'field_replacement'
                    ];
                }
            }
        }
        
        if ($needsChange) {
            $fileChanges[] = [
                'line_number' => $lineNum + 1,
                'original_line' => $originalLine,
                'changes' => $changes
            ];
        }
    }
    
    if (!empty($fileChanges)) {
        echo "  发现需要修改的行:\n";
        foreach ($fileChanges as $change) {
            echo "    - 第{$change['line_number']}行: ";
            foreach ($change['changes'] as $fieldChange) {
                echo "{$fieldChange['old_field']} → {$fieldChange['new_field']} ";
            }
            echo "\n";
            echo "      原内容: " . trim($change['original_line']) . "\n";
        }
    } else {
        echo "  ✅ 无需修改\n";
    }
    
    $analysisResults[$file] = $fileChanges;
    echo "\n";
}

echo "📝 2. 生成修改建议\n";
echo "-------------------\n";

$modificationPlan = [];

foreach ($analysisResults as $file => $changes) {
    if (empty($changes)) continue;
    
    echo "### " . basename($file) . "\n";
    
    foreach ($changes as $change) {
        $newLine = $change['original_line'];
        
        foreach ($change['changes'] as $fieldChange) {
            $oldField = $fieldChange['old_field'];
            $newField = $fieldChange['new_field'];
            
            // 根据上下文决定如何替换
            if (strpos($file, '.mdc') !== false) {
                // 文档文件：删除整行或替换字段
                if (strpos($change['original_line'], 'Schema::create') !== false ||
                    strpos($change['original_line'], 'table->') !== false) {
                    // 数据库结构定义：替换字段名
                    $newLine = str_replace($oldField, $newField, $newLine);
                } else {
                    // 其他情况：可能需要删除整行
                    $newLine = "// DELETED: " . $change['original_line'];
                }
            } else {
                // PHP文件：替换字段名
                $newLine = str_replace("'$oldField'", "'$newField'", $newLine);
                $newLine = str_replace("\"$oldField\"", "\"$newField\"", $newLine);
                $newLine = str_replace("->$oldField", "->$newField", $newLine);
                $newLine = str_replace("[$oldField]", "[$newField]", $newLine);
            }
        }
        
        echo "第{$change['line_number']}行:\n";
        echo "  原: " . trim($change['original_line']) . "\n";
        echo "  新: " . trim($newLine) . "\n\n";
        
        $modificationPlan[$file][] = [
            'line_number' => $change['line_number'],
            'old_content' => $change['original_line'],
            'new_content' => $newLine,
            'action' => strpos($newLine, '// DELETED:') !== false ? 'delete' : 'replace'
        ];
    }
}

// 保存分析结果
file_put_contents('sync_delete_analysis.json', json_encode([
    'analysis_time' => date('Y-m-d H:i:s'),
    'fields_to_delete' => $fieldsToDelete,
    'analysis_results' => $analysisResults,
    'modification_plan' => $modificationPlan
], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

echo "📄 详细分析结果已保存到: sync_delete_analysis.json\n";

// 特别提醒
echo "\n⚠️  重要提醒:\n";
echo "1. dev-api-guidelines-add.mdc 中发现多处使用缺失字段的代码示例\n";
echo "2. 这些代码示例需要更新为使用替代字段\n";
echo "3. 特别注意 available_points → points 的替换\n";
echo "4. consumed_at/returned_at → released_at 的替换\n";
echo "5. provider → platform 的替换\n";

echo "\n🎯 下一步: 执行精准的字段替换操作\n";
