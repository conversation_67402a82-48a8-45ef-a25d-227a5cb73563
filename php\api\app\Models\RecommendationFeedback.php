<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

/**
 * 推荐反馈模型
 * 用于收集和管理用户对推荐内容的反馈
 */
class RecommendationFeedback extends Model
{
    use SoftDeletes;

    protected $table = 'recommendation_feedbacks';

    /**
     * 反馈类型常量
     */
    const TYPE_LIKE = 'like';
    const TYPE_DISLIKE = 'dislike';
    const TYPE_SHARE = 'share';
    const TYPE_SAVE = 'save';
    const TYPE_CLICK = 'click';
    const TYPE_VIEW = 'view';
    const TYPE_SKIP = 'skip';

    /**
     * 状态常量
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_PROCESSED = 'processed';
    const STATUS_IGNORED = 'ignored';

    /**
     * 推荐内容类型常量
     */
    const CONTENT_TYPE_PROJECT = 'project';
    const CONTENT_TYPE_RESOURCE = 'resource';
    const CONTENT_TYPE_USER = 'user';
    const CONTENT_TYPE_STYLE = 'style';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'user_id',
        'recommendation_id',
        'content_type',
        'content_id',
        'feedback_type',
        'status',
        'score',
        'comment',
        'context_data',
        'processed_at'
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'score' => 'decimal:2',
        'context_data' => 'array',
        'processed_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    /**
     * 默认值
     */
    protected $attributes = [
        'status' => self::STATUS_ACTIVE,
        'score' => 0.0
    ];

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 关联推荐记录
     */
    public function recommendation()
    {
        return $this->belongsTo(Recommendation::class);
    }

    /**
     * 多态关联推荐内容
     */
    public function content()
    {
        return $this->morphTo('content', 'content_type', 'content_id');
    }

    /**
     * 作用域：按用户过滤
     */
    public function scopeByUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 作用域：按反馈类型过滤
     */
    public function scopeByType($query, string $feedbackType)
    {
        return $query->where('feedback_type', $feedbackType);
    }

    /**
     * 作用域：按内容类型过滤
     */
    public function scopeByContentType($query, string $contentType)
    {
        return $query->where('content_type', $contentType);
    }

    /**
     * 作用域：按状态过滤
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：正面反馈
     */
    public function scopePositive($query)
    {
        return $query->whereIn('feedback_type', [self::TYPE_LIKE, self::TYPE_SHARE, self::TYPE_SAVE]);
    }

    /**
     * 作用域：负面反馈
     */
    public function scopeNegative($query)
    {
        return $query->whereIn('feedback_type', [self::TYPE_DISLIKE, self::TYPE_SKIP]);
    }

    /**
     * 作用域：最近反馈
     */
    public function scopeRecent($query, int $days = 7)
    {
        return $query->where('created_at', '>=', Carbon::now()->subDays($days));
    }

    /**
     * 作用域：未处理的反馈
     */
    public function scopeUnprocessed($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * 检查是否为正面反馈
     */
    public function isPositive(): bool
    {
        return in_array($this->feedback_type, [self::TYPE_LIKE, self::TYPE_SHARE, self::TYPE_SAVE]);
    }

    /**
     * 检查是否为负面反馈
     */
    public function isNegative(): bool
    {
        return in_array($this->feedback_type, [self::TYPE_DISLIKE, self::TYPE_SKIP]);
    }

    /**
     * 标记为已处理
     */
    public function markAsProcessed(): void
    {
        $this->status = self::STATUS_PROCESSED;
        $this->processed_at = Carbon::now();
        $this->save();
    }

    /**
     * 标记为忽略
     */
    public function markAsIgnored(): void
    {
        $this->status = self::STATUS_IGNORED;
        $this->save();
    }

    /**
     * 获取上下文数据
     */
    public function getContextData(string $key, $default = null)
    {
        return data_get($this->context_data, $key, $default);
    }

    /**
     * 设置上下文数据
     */
    public function setContextData(string $key, $value): void
    {
        $contextData = $this->context_data ?? [];
        data_set($contextData, $key, $value);
        $this->context_data = $contextData;
    }

    /**
     * 计算反馈权重
     */
    public function calculateWeight(): float
    {
        $weights = [
            self::TYPE_LIKE => 1.0,
            self::TYPE_DISLIKE => -1.0,
            self::TYPE_SHARE => 2.0,
            self::TYPE_SAVE => 1.5,
            self::TYPE_CLICK => 0.5,
            self::TYPE_VIEW => 0.2,
            self::TYPE_SKIP => -0.5
        ];

        return $weights[$this->feedback_type] ?? 0.0;
    }
}