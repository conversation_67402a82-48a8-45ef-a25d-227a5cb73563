<?php
/**
 * 识别需要删除的缺失字段及其在文档、控制器、业务层中的引用
 */

echo "🔍 识别需要删除的缺失字段引用\n";
echo str_repeat("=", 60) . "\n\n";

// 根据之前的分析，需要删除的缺失字段
$missingFieldsToDelete = [
    'p_points_transactions' => ['description'],
    'p_points_freeze' => ['consumed_at', 'consume_reason'],
    // 注意：其他字段都有功能等价替代，不需要删除
];

// 需要检查的文档文件
$documentFiles = [
    '.cursor/rules/dev-api-guidelines-database.mdc',
    '.cursor/rules/dev-api-guidelines-add.mdc', 
    '.cursor/rules/dev-api-guidelines-pyapi.mdc'
];

// 需要检查的代码文件模式
$codeFilePatterns = [
    'php/api/app/Http/Controllers/PyApi/*Controller.php',
    'php/api/app/Services/PyApi/*Service.php',
    'php/api/app/Models/*.php'
];

$analysisResults = [
    'analysis_time' => date('Y-m-d H:i:s'),
    'missing_fields_to_delete' => $missingFieldsToDelete,
    'document_references' => [],
    'code_references' => [],
    'deletion_plan' => []
];

echo "📋 1. 分析需要删除的字段\n";
echo "-------------------\n";

foreach ($missingFieldsToDelete as $tableName => $fields) {
    echo "表: $tableName\n";
    echo "需要删除的字段: " . implode(', ', $fields) . "\n\n";
}

echo "📄 2. 检查文档文件中的引用\n";
echo "-------------------\n";

foreach ($documentFiles as $docFile) {
    if (!file_exists($docFile)) {
        echo "❌ 文档不存在: $docFile\n";
        continue;
    }
    
    echo "✅ 检查文档: " . basename($docFile) . "\n";
    $content = file_get_contents($docFile);
    $lines = explode("\n", $content);
    
    $docReferences = [];
    
    foreach ($missingFieldsToDelete as $tableName => $fields) {
        foreach ($fields as $field) {
            // 查找字段引用
            foreach ($lines as $lineNum => $line) {
                // 检查是否是数据库字段定义（而不是参数名）
                if (strpos($line, $tableName) !== false && strpos($line, $field) !== false) {
                    $docReferences[] = [
                        'table' => $tableName,
                        'field' => $field,
                        'line_number' => $lineNum + 1,
                        'line_content' => trim($line),
                        'context_type' => 'table_definition'
                    ];
                } elseif (strpos($line, "`$field`") !== false || 
                         strpos($line, "- $field") !== false ||
                         strpos($line, "* $field") !== false) {
                    // 检查是否在对应表的上下文中
                    $contextLines = array_slice($lines, max(0, $lineNum - 10), 20);
                    $inTableContext = false;
                    foreach ($contextLines as $contextLine) {
                        if (strpos($contextLine, $tableName) !== false) {
                            $inTableContext = true;
                            break;
                        }
                    }
                    
                    if ($inTableContext) {
                        $docReferences[] = [
                            'table' => $tableName,
                            'field' => $field,
                            'line_number' => $lineNum + 1,
                            'line_content' => trim($line),
                            'context_type' => 'field_reference'
                        ];
                    }
                }
            }
        }
    }
    
    if (!empty($docReferences)) {
        echo "  发现引用:\n";
        foreach ($docReferences as $ref) {
            echo "    - 第{$ref['line_number']}行: {$ref['field']} ({$ref['context_type']})\n";
            echo "      内容: {$ref['line_content']}\n";
        }
    } else {
        echo "  ✅ 无相关字段引用\n";
    }
    
    $analysisResults['document_references'][$docFile] = $docReferences;
    echo "\n";
}

echo "💻 3. 检查代码文件中的引用\n";
echo "-------------------\n";

// 查找相关的控制器和服务文件
$relevantFiles = [];

// 查找积分相关的文件
$pointsFiles = [
    'php/api/app/Http/Controllers/PyApi/PointsController.php',
    'php/api/app/Services/PyApi/PointsService.php',
    'php/api/app/Models/PointsTransaction.php',
    'php/api/app/Models/PointsFreeze.php'
];

foreach ($pointsFiles as $file) {
    if (file_exists($file)) {
        $relevantFiles[] = $file;
    }
}

// 也检查其他可能相关的文件
$otherPatterns = [
    'php/api/app/Http/Controllers/PyApi/*Controller.php',
    'php/api/app/Services/PyApi/*Service.php'
];

foreach ($otherPatterns as $pattern) {
    $files = glob($pattern);
    foreach ($files as $file) {
        $content = file_get_contents($file);
        // 检查是否包含积分相关的内容
        if (strpos($content, 'points_transaction') !== false ||
            strpos($content, 'points_freeze') !== false ||
            strpos($content, 'PointsTransaction') !== false ||
            strpos($content, 'PointsFreeze') !== false) {
            $relevantFiles[] = $file;
        }
    }
}

$relevantFiles = array_unique($relevantFiles);

foreach ($relevantFiles as $codeFile) {
    echo "✅ 检查代码文件: " . basename($codeFile) . "\n";
    $content = file_get_contents($codeFile);
    $lines = explode("\n", $content);
    
    $codeReferences = [];
    
    foreach ($missingFieldsToDelete as $tableName => $fields) {
        foreach ($fields as $field) {
            foreach ($lines as $lineNum => $line) {
                // 检查字段引用，但要区分数据库字段和参数名
                if (strpos($line, "'$field'") !== false ||
                    strpos($line, "\"$field\"") !== false ||
                    strpos($line, "\$$field") !== false ||
                    strpos($line, "->$field") !== false ||
                    strpos($line, "[$field]") !== false) {
                    
                    // 判断是否是数据库字段引用
                    $isDatabaseField = false;
                    $contextType = 'parameter';
                    
                    if (strpos($line, 'fillable') !== false ||
                        strpos($line, 'casts') !== false ||
                        strpos($line, 'DB::') !== false ||
                        strpos($line, '->select') !== false ||
                        strpos($line, '->where') !== false ||
                        strpos($line, 'Schema::') !== false) {
                        $isDatabaseField = true;
                        $contextType = 'database_field';
                    }
                    
                    $codeReferences[] = [
                        'table' => $tableName,
                        'field' => $field,
                        'line_number' => $lineNum + 1,
                        'line_content' => trim($line),
                        'context_type' => $contextType,
                        'is_database_field' => $isDatabaseField
                    ];
                }
            }
        }
    }
    
    if (!empty($codeReferences)) {
        echo "  发现引用:\n";
        foreach ($codeReferences as $ref) {
            $icon = $ref['is_database_field'] ? '🗄️' : '📝';
            echo "    $icon 第{$ref['line_number']}行: {$ref['field']} ({$ref['context_type']})\n";
            echo "      内容: {$ref['line_content']}\n";
        }
    } else {
        echo "  ✅ 无相关字段引用\n";
    }
    
    $analysisResults['code_references'][$codeFile] = $codeReferences;
    echo "\n";
}

echo "📋 4. 生成删除计划\n";
echo "-------------------\n";

$deletionPlan = [];

// 文档删除计划
foreach ($analysisResults['document_references'] as $docFile => $references) {
    if (!empty($references)) {
        $deletionPlan['documents'][$docFile] = [];
        foreach ($references as $ref) {
            $deletionPlan['documents'][$docFile][] = [
                'action' => 'delete_line',
                'line_number' => $ref['line_number'],
                'field' => $ref['field'],
                'table' => $ref['table'],
                'content' => $ref['line_content']
            ];
        }
    }
}

// 代码删除计划
foreach ($analysisResults['code_references'] as $codeFile => $references) {
    if (!empty($references)) {
        $deletionPlan['code_files'][$codeFile] = [];
        foreach ($references as $ref) {
            // 只删除数据库字段引用，保留参数名
            if ($ref['is_database_field']) {
                $deletionPlan['code_files'][$codeFile][] = [
                    'action' => 'modify_line',
                    'line_number' => $ref['line_number'],
                    'field' => $ref['field'],
                    'table' => $ref['table'],
                    'content' => $ref['line_content'],
                    'type' => 'database_field'
                ];
            }
        }
    }
}

$analysisResults['deletion_plan'] = $deletionPlan;

// 输出删除计划
if (isset($deletionPlan['documents'])) {
    echo "📄 文档删除计划:\n";
    foreach ($deletionPlan['documents'] as $file => $actions) {
        echo "  " . basename($file) . ":\n";
        foreach ($actions as $action) {
            echo "    - 删除第{$action['line_number']}行: {$action['field']} 字段\n";
        }
    }
    echo "\n";
}

if (isset($deletionPlan['code_files'])) {
    echo "💻 代码删除计划:\n";
    foreach ($deletionPlan['code_files'] as $file => $actions) {
        echo "  " . basename($file) . ":\n";
        foreach ($actions as $action) {
            echo "    - 修改第{$action['line_number']}行: 删除 {$action['field']} 数据库字段引用\n";
        }
    }
}

// 保存分析结果
file_put_contents('missing_fields_deletion_analysis.json', json_encode($analysisResults, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

echo "\n📄 详细分析结果已保存到: missing_fields_deletion_analysis.json\n";
echo "\n🎯 下一步: 根据删除计划执行精准删除操作\n";
