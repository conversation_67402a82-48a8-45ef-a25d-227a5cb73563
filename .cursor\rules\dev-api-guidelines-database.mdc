---
description: 数据库设计规范文档
globs: ["php/api/**/*.php", "php/api/**/*.json", "php/api/**/.env*", "php/api/**/routes/**", "php/api/**/config/**"]
alwaysApply: true
---

# 📊 数据库设计规范文档

## 核心数据表结构

### 基础业务表
- **p_users**: 用户表（用户信息、认证、偏好设置）
- **p_points_transactions**: 积分交易表（积分流水、冻结、返还）
- **p_points_freeze**: 积分冻结表（冻结机制、安全保障）

### AI生成相关表
- **p_music_library**: 音乐库表（AI生成音乐存储、MiniMax平台）
- **p_sound_library**: 音效库表（AI生成音效存储、火山引擎豆包平台）
- **p_timbre_library**: 音色库表（AI生成音色存储、双平台支持）
- **p_style_library**: 风格库表（剧情风格管理、AI生成配置）
- **p_story_library**: 故事库表（AI生成故事内容、项目表 p_projects 关联）
- **p_character_library**: 角色库表（AI生成角色信息、特征描述）

### 核心资源管理表
- **p_resources**: AI生成资源表（资源管理、模块关联、状态跟踪）
- **p_resource_versions**: 资源版本表（版本控制、提示词管理、本地导出）

### 任务管理表
- **p_ai_generation_tasks**: AI生成任务表（任务状态、进度、结果）
- **p_websocket_sessions**: WebSocket会话表（连接管理、状态同步）

### 用户作品管理表
- **p_user_works**: 用户作品表（用户创作的作品管理）
  - `user_id` - 用户ID，关联p_users表
  - `work_title` - 作品标题
  - `work_type` - 作品类型（video/image/music/story/character）
  - `content` - 作品内容或描述
  - `project_id` - 关联的项目ID（可选）
  - `status` - 作品状态（draft/completed/published/archived）
  - `visibility` - 可见性（private/public/friends_only）
  - `metadata` - 作品元数据（JSON格式，包含文件路径、尺寸、时长等）
  - `tags` - 作品标签（JSON数组）
  - `view_count` - 浏览次数
  - `like_count` - 点赞数量
  - `comment_count` - 评论数量
  - `share_count` - 分享次数
  - `featured_at` - 精选时间（用于推荐算法）
  - `published_at` - 发布时间
  - `created_at` - 创建时间
  - `updated_at` - 更新时间

### 作品发布和社交表
- **p_work_plaza**: 作品广场表（公开作品展示平台）
  - `work_id` - 关联p_user_works表的作品ID
  - `user_id` - 作品创作者ID，关联p_users表
  - `title` - 作品展示标题
  - `description` - 作品描述
  - `category` - 作品分类（video/image/music/story/character/mixed）
  - `tags` - 作品标签（JSON数组，用于搜索和推荐）
  - `thumbnail` - 作品缩略图URL
  - `preview_url` - 预览文件URL（如视频预览、音频试听等）
  - `view_count` - 浏览次数
  - `like_count` - 点赞数量
  - `comment_count` - 评论数量
  - `share_count` - 分享次数
  - `download_count` - 下载次数
  - `rating` - 作品评分（1-5星）
  - `rating_count` - 评分人数
  - `review_status` - 审核状态（pending/approved/rejected/auto_approved）
  - `review_reason` - 审核说明
  - `featured` - 是否精选推荐
  - `featured_at` - 精选时间
  - `published_at` - 发布到广场的时间
  - `created_at` - 创建时间
  - `updated_at` - 更新时间

- **p_work_shares**: 作品分享表（分享链接、权限控制、访问统计）

### 社交功能表
- **p_follows**: 关注关系表（用户关注系统）
  - `follower_id` - 关注者用户ID，关联p_users表
  - `following_id` - 被关注者用户ID，关联p_users表
  - `status` - 关注状态（active/blocked/pending）
  - `notification_enabled` - 是否启用通知
  - `created_at` - 关注时间
  - `updated_at` - 更新时间

- **p_likes**: 点赞表（通用点赞系统）
  - `user_id` - 点赞用户ID，关联p_users表
  - `target_type` - 目标类型（work/comment/user/project）
  - `target_id` - 目标ID（根据target_type关联不同表）
  - `created_at` - 点赞时间

- **p_comments**: 评论表（通用评论系统）
  - `user_id` - 评论用户ID，关联p_users表
  - `target_type` - 评论目标类型（work/project/user）
  - `target_id` - 评论目标ID（根据target_type关联不同表）
  - `content` - 评论内容
  - `parent_id` - 父评论ID（用于回复功能，关联本表）
  - `status` - 评论状态（active/hidden/deleted/pending_review）
  - `like_count` - 评论点赞数
  - `reply_count` - 回复数量
  - `is_pinned` - 是否置顶
  - `created_at` - 评论时间
  - `updated_at` - 更新时间

- **p_work_interactions**: 作品互动表（点赞、评论、分享记录的汇总统计）
