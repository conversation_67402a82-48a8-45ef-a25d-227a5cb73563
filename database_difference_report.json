{"report_time": "2025-08-04 21:44:22", "summary": {"total_existing_tables": 31, "total_planned_tables": 23, "total_code_used_tables": 51, "missing_in_reality_count": 14, "missing_in_planning_count": 22, "tables_with_field_differences": 9, "total_recommendations": 3, "critical_issues": 23}, "existing_tables_count": 31, "planned_tables_count": 23, "code_used_tables_count": 51, "differences": [], "missing_in_reality": [{"planned_name": "p_memberships", "description": "会员表（会员等级、权限、使用记录）", "category": "基础业务表", "source": "index-new.mdc", "has_code_implementation": false}, {"planned_name": "p_music_library", "description": "音乐库表（AI生成音乐存储、MiniMax平台）", "category": "AI生成相关表", "source": "index-new.mdc", "has_code_implementation": false}, {"planned_name": "p_sound_library", "description": "音效库表（AI生成音效存储、火山引擎豆包平台）", "category": "AI生成相关表", "source": "index-new.mdc", "has_code_implementation": false}, {"planned_name": "p_timbre_library", "description": "音色库表（AI生成音色存储、双平台支持）", "category": "AI生成相关表", "source": "index-new.mdc", "has_code_implementation": false}, {"planned_name": "p_story_library", "description": "故事库表（AI生成故事内容、项目表 p_projects 关联）", "category": "AI生成相关表", "source": "index-new.mdc", "has_code_implementation": false}, {"planned_name": "p_resources", "description": "AI生成资源表（资源管理、模块关联、状态跟踪）", "category": "核心资源管理表", "source": "index-new.mdc", "has_code_implementation": true}, {"planned_name": "user_growth_paths", "description": "用户成长路径跟踪系统", "category": "用户成长系统", "source": "dev-api-guidelines-add.mdc", "has_code_implementation": false}, {"planned_name": "user_recommendations", "description": "个性化推荐系统", "category": "推荐系统", "source": "dev-api-guidelines-add.mdc", "has_code_implementation": false}, {"planned_name": "referral_codes", "description": "邀请码表", "category": "邀请佣金系统", "source": "dev-api-guidelines-add.mdc", "has_code_implementation": false}, {"planned_name": "referral_commissions", "description": "邀请佣金表", "category": "邀请佣金系统", "source": "dev-api-guidelines-add.mdc", "has_code_implementation": false}, {"planned_name": "download_tracking", "description": "终端下载推广系统", "category": "下载推广系统", "source": "dev-api-guidelines-add.mdc", "has_code_implementation": false}, {"planned_name": "cache_statistics", "description": "缓存管理和性能优化系统", "category": "缓存管理", "source": "dev-api-guidelines-add.mdc", "has_code_implementation": false}, {"planned_name": "workflow_templates", "description": "工作流模板表", "category": "工作流管理系统", "source": "dev-api-guidelines-add.mdc", "has_code_implementation": false}, {"planned_name": "workflow_executions", "description": "工作流执行表", "category": "工作流管理系统", "source": "dev-api-guidelines-add.mdc", "has_code_implementation": false}], "missing_in_planning": [{"existing_name": "p_achievements", "field_count": 21, "has_code_usage": true, "code_usage": {"used_by_models": ["Achievement"], "used_by_controllers": [], "used_by_services": [], "direct_db_usage": []}}, {"existing_name": "p_ai_resources", "field_count": 32, "has_code_usage": false, "code_usage": null}, {"existing_name": "p_character_categories", "field_count": 13, "has_code_usage": true, "code_usage": {"used_by_models": ["CharacterCategory"], "used_by_controllers": ["CharacterController"], "used_by_services": ["CharacterService"], "direct_db_usage": []}}, {"existing_name": "p_daily_tasks", "field_count": 21, "has_code_usage": true, "code_usage": {"used_by_models": ["DailyTask"], "used_by_controllers": [], "used_by_services": [], "direct_db_usage": []}}, {"existing_name": "p_data_exports", "field_count": 20, "has_code_usage": false, "code_usage": null}, {"existing_name": "p_growth_histories", "field_count": 19, "has_code_usage": true, "code_usage": {"used_by_models": ["GrowthHistory"], "used_by_controllers": [], "used_by_services": [], "direct_db_usage": []}}, {"existing_name": "p_migrations", "field_count": 3, "has_code_usage": false, "code_usage": null}, {"existing_name": "p_platform_performance_metrics", "field_count": 14, "has_code_usage": true, "code_usage": {"used_by_models": ["PlatformPerformanceMetric"], "used_by_controllers": [], "used_by_services": ["AiLoadBalancingService", "AiPlatformHealthService", "AiPlatformSelectionService"], "direct_db_usage": []}}, {"existing_name": "p_projects", "field_count": 18, "has_code_usage": true, "code_usage": {"used_by_models": ["Project"], "used_by_controllers": ["ProjectController"], "used_by_services": ["ProjectManagementService", "ProjectService", "ResourceManagementService", "SearchService", "TemplateService"], "direct_db_usage": []}}, {"existing_name": "p_publications", "field_count": 25, "has_code_usage": true, "code_usage": {"used_by_models": ["Publication"], "used_by_controllers": [], "used_by_services": ["PublicationService", "RecommendationService", "ReviewService", "SocialService"], "direct_db_usage": []}}, {"existing_name": "p_system_monitors", "field_count": 13, "has_code_usage": true, "code_usage": {"used_by_models": ["SystemMonitor"], "used_by_controllers": [], "used_by_services": [], "direct_db_usage": []}}, {"existing_name": "p_user_achievements", "field_count": 12, "has_code_usage": true, "code_usage": {"used_by_models": ["UserAchievement"], "used_by_controllers": [], "used_by_services": [], "direct_db_usage": []}}, {"existing_name": "p_user_character_bindings", "field_count": 16, "has_code_usage": true, "code_usage": {"used_by_models": ["UserCharacterBinding"], "used_by_controllers": [], "used_by_services": ["CharacterService"], "direct_db_usage": []}}, {"existing_name": "p_user_daily_tasks", "field_count": 14, "has_code_usage": true, "code_usage": {"used_by_models": ["UserDailyTask"], "used_by_controllers": [], "used_by_services": [], "direct_db_usage": []}}, {"existing_name": "p_user_files", "field_count": 21, "has_code_usage": true, "code_usage": {"used_by_models": ["UserFile"], "used_by_controllers": [], "used_by_services": ["FileService", "SearchService"], "direct_db_usage": []}}, {"existing_name": "p_user_levels", "field_count": 17, "has_code_usage": true, "code_usage": {"used_by_models": ["UserLevel"], "used_by_controllers": [], "used_by_services": [], "direct_db_usage": []}}, {"existing_name": "p_user_model_preferences", "field_count": 14, "has_code_usage": true, "code_usage": {"used_by_models": ["UserModelPreference"], "used_by_controllers": [], "used_by_services": ["AiPlatformFallbackService", "AiPlatformSelectionService"], "direct_db_usage": []}}, {"existing_name": "p_user_preferences", "field_count": 14, "has_code_usage": true, "code_usage": {"used_by_models": ["UserPreference"], "used_by_controllers": ["UserController"], "used_by_services": ["AiModelService", "RecommendationService", "UserService"], "direct_db_usage": []}}, {"existing_name": "p_user_works", "field_count": 14, "has_code_usage": true, "code_usage": {"used_by_models": ["UserWork"], "used_by_controllers": [], "used_by_services": ["WorkPublishService"], "direct_db_usage": []}}, {"existing_name": "p_work_interactions", "field_count": 8, "has_code_usage": true, "code_usage": {"used_by_models": ["WorkInteraction"], "used_by_controllers": ["WorkPublishController"], "used_by_services": ["WorkPublishService"], "direct_db_usage": []}}, {"existing_name": "p_work_plaza", "field_count": 33, "has_code_usage": true, "code_usage": {"used_by_models": ["WorkPlaza"], "used_by_controllers": ["WorkPublishController"], "used_by_services": ["WorkPublishService"], "direct_db_usage": ["WorkPublishController"]}}, {"existing_name": "p_work_shares", "field_count": 11, "has_code_usage": true, "code_usage": {"used_by_models": ["WorkShare"], "used_by_controllers": [], "used_by_services": ["WorkPublishService"], "direct_db_usage": []}}], "name_conflicts": [], "field_differences": [{"table_name": "users", "existing_full_name": "p_users", "planned_full_name": "p_users", "name_match": true, "existing_fields": ["id", "username", "password", "email", "nickname", "level", "experience", "avatar", "bio", "follower_count", "following_count", "inviter_id", "remark", "status", "points", "frozen_points", "is_vip", "vip_expires_at", "last_login_ip", "last_login_at", "created_at", "updated_at"], "planned_fields": ["id", "username", "email", "password", "available_points", "frozen_points", "created_at", "updated_at"], "field_differences": {"missing_in_existing": {"4": "available_points"}, "missing_in_planned": {"4": "nickname", "5": "level", "6": "experience", "7": "avatar", "8": "bio", "9": "follower_count", "10": "following_count", "11": "inviter_id", "12": "remark", "13": "status", "14": "points", "16": "is_vip", "17": "vip_expires_at", "18": "last_login_ip", "19": "last_login_at"}, "common_fields": {"0": "id", "1": "username", "2": "password", "3": "email", "15": "frozen_points", "20": "created_at", "21": "updated_at"}}, "has_code_usage": true}, {"table_name": "points_transactions", "existing_full_name": "p_points_transactions", "planned_full_name": "p_points_transactions", "name_match": true, "existing_fields": ["id", "user_id", "business_type", "business_id", "amount", "status", "ai_platform", "request_data", "response_data", "timeout_seconds", "completed_at", "failure_reason", "created_at", "updated_at"], "planned_fields": ["id", "user_id", "amount", "type", "business_type", "business_id", "description", "created_at"], "field_differences": {"missing_in_existing": {"3": "type", "6": "description"}, "missing_in_planned": {"5": "status", "6": "ai_platform", "7": "request_data", "8": "response_data", "9": "timeout_seconds", "10": "completed_at", "11": "failure_reason", "13": "updated_at"}, "common_fields": {"0": "id", "1": "user_id", "2": "business_type", "3": "business_id", "4": "amount", "12": "created_at"}}, "has_code_usage": true}, {"table_name": "points_freeze", "existing_full_name": "p_points_freeze", "planned_full_name": "p_points_freeze", "name_match": true, "existing_fields": ["id", "user_id", "transaction_id", "amount", "status", "business_type", "business_id", "expires_at", "released_at", "reason", "created_at", "updated_at"], "planned_fields": ["id", "user_id", "amount", "business_type", "business_id", "status", "consumed_at", "returned_at", "consume_reason", "return_reason", "created_at", "updated_at"], "field_differences": {"missing_in_existing": {"6": "consumed_at", "7": "returned_at", "8": "consume_reason", "9": "return_reason"}, "missing_in_planned": {"2": "transaction_id", "7": "expires_at", "8": "released_at", "9": "reason"}, "common_fields": {"0": "id", "1": "user_id", "3": "amount", "4": "status", "5": "business_type", "6": "business_id", "10": "created_at", "11": "updated_at"}}, "has_code_usage": true}, {"table_name": "style_library", "existing_full_name": "p_style_library", "planned_full_name": "p_style_library", "name_match": true, "existing_fields": ["id", "name", "description", "category", "style_config", "prompt_template", "thumbnail", "is_active", "is_premium", "sort_order", "usage_count", "rating", "tags", "created_by", "created_at", "updated_at"], "planned_fields": ["id", "name", "description", "config", "created_at", "updated_at"], "field_differences": {"missing_in_existing": {"3": "config"}, "missing_in_planned": {"3": "category", "4": "style_config", "5": "prompt_template", "6": "thumbnail", "7": "is_active", "8": "is_premium", "9": "sort_order", "10": "usage_count", "11": "rating", "12": "tags", "13": "created_by"}, "common_fields": {"0": "id", "1": "name", "2": "description", "14": "created_at", "15": "updated_at"}}, "has_code_usage": true}, {"table_name": "character_library", "existing_full_name": "p_character_library", "planned_full_name": "p_character_library", "name_match": true, "existing_fields": ["id", "name", "description", "category_id", "gender", "age_range", "personality", "background", "appearance", "avatar", "images", "voice_config", "style_preferences", "tags", "is_active", "is_premium", "is_featured", "sort_order", "binding_count", "rating", "rating_count", "created_by", "created_at", "updated_at"], "planned_fields": ["id", "name", "description", "characteristics", "created_at", "updated_at"], "field_differences": {"missing_in_existing": {"3": "characteristics"}, "missing_in_planned": {"3": "category_id", "4": "gender", "5": "age_range", "6": "personality", "7": "background", "8": "appearance", "9": "avatar", "10": "images", "11": "voice_config", "12": "style_preferences", "13": "tags", "14": "is_active", "15": "is_premium", "16": "is_featured", "17": "sort_order", "18": "binding_count", "19": "rating", "20": "rating_count", "21": "created_by"}, "common_fields": {"0": "id", "1": "name", "2": "description", "22": "created_at", "23": "updated_at"}}, "has_code_usage": true}, {"table_name": "resource_versions", "existing_full_name": "p_resource_versions", "planned_full_name": "p_resource_versions", "name_match": true, "existing_fields": ["id", "version_uuid", "resource_id", "user_id", "version_number", "version_type", "resource_url", "original_filename", "file_size", "mime_type", "generation_cost", "cost_transaction_id", "prompt_text", "negative_prompt", "ai_platform", "ai_model", "generation_params", "ai_platform_metadata", "generated_at", "url_expires_at", "resource_status", "review_status", "review_notes", "is_downloaded_locally", "local_save_path", "downloaded_at", "reviewer_id", "reviewed_at", "status", "created_at", "updated_at"], "planned_fields": ["id", "resource_id", "version_name", "description", "generation_config", "created_at", "updated_at"], "field_differences": {"missing_in_existing": {"2": "version_name", "3": "description", "4": "generation_config"}, "missing_in_planned": {"1": "version_uuid", "3": "user_id", "4": "version_number", "5": "version_type", "6": "resource_url", "7": "original_filename", "8": "file_size", "9": "mime_type", "10": "generation_cost", "11": "cost_transaction_id", "12": "prompt_text", "13": "negative_prompt", "14": "ai_platform", "15": "ai_model", "16": "generation_params", "17": "ai_platform_metadata", "18": "generated_at", "19": "url_expires_at", "20": "resource_status", "21": "review_status", "22": "review_notes", "23": "is_downloaded_locally", "24": "local_save_path", "25": "downloaded_at", "26": "reviewer_id", "27": "reviewed_at", "28": "status"}, "common_fields": {"0": "id", "2": "resource_id", "29": "created_at", "30": "updated_at"}}, "has_code_usage": true}, {"table_name": "ai_generation_tasks", "existing_full_name": "p_ai_generation_tasks", "planned_full_name": "p_ai_generation_tasks", "name_match": true, "existing_fields": ["id", "user_id", "project_id", "model_config_id", "task_type", "platform", "model_name", "status", "input_data", "output_data", "generation_params", "external_task_id", "cost", "tokens_used", "processing_time_ms", "started_at", "completed_at", "error_message", "metadata", "retry_count", "max_retries", "created_at", "updated_at"], "planned_fields": ["id", "user_id", "task_type", "status", "progress", "input_data", "result_data", "error_message", "created_at", "updated_at"], "field_differences": {"missing_in_existing": {"4": "progress", "6": "result_data"}, "missing_in_planned": {"2": "project_id", "3": "model_config_id", "5": "platform", "6": "model_name", "9": "output_data", "10": "generation_params", "11": "external_task_id", "12": "cost", "13": "tokens_used", "14": "processing_time_ms", "15": "started_at", "16": "completed_at", "18": "metadata", "19": "retry_count", "20": "max_retries"}, "common_fields": {"0": "id", "1": "user_id", "4": "task_type", "7": "status", "8": "input_data", "17": "error_message", "21": "created_at", "22": "updated_at"}}, "has_code_usage": true}, {"table_name": "websocket_sessions", "existing_full_name": "p_websocket_sessions", "planned_full_name": "p_websocket_sessions", "name_match": true, "existing_fields": ["id", "session_id", "user_id", "client_type", "client_version", "connection_ip", "user_agent", "status", "connection_info", "subscribed_events", "connected_at", "last_ping_at", "disconnected_at", "message_count", "disconnect_reason", "created_at", "updated_at"], "planned_fields": ["id", "user_id", "session_id", "client_type", "status", "created_at", "updated_at"], "field_differences": {"missing_in_existing": [], "missing_in_planned": {"4": "client_version", "5": "connection_ip", "6": "user_agent", "8": "connection_info", "9": "subscribed_events", "10": "connected_at", "11": "last_ping_at", "12": "disconnected_at", "13": "message_count", "14": "disconnect_reason"}, "common_fields": {"0": "id", "1": "session_id", "2": "user_id", "3": "client_type", "7": "status", "15": "created_at", "16": "updated_at"}}, "has_code_usage": true}, {"table_name": "ai_model_configs", "existing_full_name": "p_ai_model_configs", "planned_full_name": "ai_model_configs", "name_match": false, "existing_fields": ["id", "platform", "model_name", "model_type", "api_endpoint", "config_params", "capabilities", "is_active", "is_default", "priority", "cost_per_request", "max_tokens", "timeout_seconds", "rate_limits", "performance_metrics", "last_health_check", "health_status", "health_message", "created_at", "updated_at"], "planned_fields": ["id", "model_name", "provider", "model_type", "config_params", "is_active", "priority", "cost_per_request", "created_at", "updated_at"], "field_differences": {"missing_in_existing": {"2": "provider"}, "missing_in_planned": {"1": "platform", "4": "api_endpoint", "6": "capabilities", "8": "is_default", "11": "max_tokens", "12": "timeout_seconds", "13": "rate_limits", "14": "performance_metrics", "15": "last_health_check", "16": "health_status", "17": "health_message"}, "common_fields": {"0": "id", "2": "model_name", "3": "model_type", "5": "config_params", "7": "is_active", "9": "priority", "10": "cost_per_request", "18": "created_at", "19": "updated_at"}}, "has_code_usage": true}], "recommendations": [{"type": "create_missing_tables", "priority": "high", "description": "创建规划中缺失的数据表", "action": "需要创建 14 个数据表", "tables": ["p_memberships", "p_music_library", "p_sound_library", "p_timbre_library", "p_story_library", "p_resources", "user_growth_paths", "user_recommendations", "referral_codes", "referral_commissions", "download_tracking", "cache_statistics", "workflow_templates", "workflow_executions"], "impact": "这些表在文档中有明确规划，但数据库中不存在，可能影响相关功能的实现"}, {"type": "update_documentation", "priority": "medium", "description": "更新文档以包含现有数据表", "action": "需要在文档中补充 22 个数据表的规划", "tables": ["p_achievements", "p_ai_resources", "p_character_categories", "p_daily_tasks", "p_data_exports", "p_growth_histories", "p_migrations", "p_platform_performance_metrics", "p_projects", "p_publications", "p_system_monitors", "p_user_achievements", "p_user_character_bindings", "p_user_daily_tasks", "p_user_files", "p_user_levels", "p_user_model_preferences", "p_user_preferences", "p_user_works", "p_work_interactions", "p_work_plaza", "p_work_shares"], "impact": "这些表已经存在并被代码使用，但文档中没有规划，需要补充文档"}, {"type": "sync_table_fields", "priority": "high", "description": "同步数据表字段", "action": "需要同步 9 个数据表的字段", "tables": ["users", "points_transactions", "points_freeze", "style_library", "character_library", "resource_versions", "ai_generation_tasks", "websocket_sessions", "ai_model_configs"], "impact": "字段不匹配可能导致代码运行错误或功能缺失"}]}