<?php
/**
 * 分析 database_difference_report.md 中缺失字段是否有功能等价的替代字段
 */

echo "🔍 分析缺失字段的功能等价替代字段\n";
echo str_repeat("=", 60) . "\n\n";

// 加载数据库结构
$existingTables = json_decode(file_get_contents('database_structure_export.json'), true);

// 定义需要分析的表和缺失字段
$missingFieldsAnalysis = [
    'p_users' => [
        'missing_fields' => ['available_points'],
        'table_name' => 'p_users'
    ],
    'p_points_transactions' => [
        'missing_fields' => ['type', 'description'],
        'table_name' => 'p_points_transactions'
    ],
    'p_points_freeze' => [
        'missing_fields' => ['consumed_at', 'returned_at', 'consume_reason', 'return_reason'],
        'table_name' => 'p_points_freeze'
    ],
    'p_style_library' => [
        'missing_fields' => ['config'],
        'table_name' => 'p_style_library'
    ],
    'p_character_library' => [
        'missing_fields' => ['characteristics'],
        'table_name' => 'p_character_library'
    ],
    'p_resource_versions' => [
        'missing_fields' => ['version_name', 'description', 'generation_config'],
        'table_name' => 'p_resource_versions'
    ],
    'p_ai_generation_tasks' => [
        'missing_fields' => ['progress', 'result_data'],
        'table_name' => 'p_ai_generation_tasks'
    ],
    'p_ai_model_configs' => [
        'missing_fields' => ['provider'],
        'table_name' => 'p_ai_model_configs'
    ]
];

// 查找表结构
$tableStructures = [];
foreach ($existingTables['tables'] as $table) {
    $tableStructures[$table['name']] = $table;
}

echo "📋 逐表分析缺失字段的替代实现\n";
echo str_repeat("-", 40) . "\n\n";

$analysisResults = [];

foreach ($missingFieldsAnalysis as $key => $analysis) {
    $tableName = $analysis['table_name'];
    $missingFields = $analysis['missing_fields'];
    
    echo "### 📊 $tableName 表分析\n";
    echo "缺失字段: " . implode(', ', $missingFields) . "\n\n";
    
    if (!isset($tableStructures[$tableName])) {
        echo "❌ 表不存在\n\n";
        continue;
    }
    
    $table = $tableStructures[$tableName];
    $existingFields = [];
    foreach ($table['fields'] as $field) {
        $existingFields[$field['name']] = $field;
    }
    
    $fieldAnalysis = [];
    
    foreach ($missingFields as $missingField) {
        echo "🔍 分析缺失字段: **$missingField**\n";
        
        $alternatives = [];
        $functionalEquivalent = null;
        
        // 根据字段名和功能分析可能的替代字段
        switch ($missingField) {
            case 'available_points':
                // 检查是否有 points 字段
                if (isset($existingFields['points'])) {
                    $alternatives[] = [
                        'field' => 'points',
                        'type' => $existingFields['points']['type'],
                        'comment' => $existingFields['points']['comment'],
                        'match_level' => 'exact_function'
                    ];
                    $functionalEquivalent = 'points';
                }
                break;
                
            case 'type':
                // 检查是否有 business_type 或类似字段
                foreach ($existingFields as $fieldName => $fieldInfo) {
                    if (strpos($fieldName, 'type') !== false || 
                        strpos($fieldName, 'business_type') !== false ||
                        strpos($fieldName, 'transaction_type') !== false) {
                        $alternatives[] = [
                            'field' => $fieldName,
                            'type' => $fieldInfo['type'],
                            'comment' => $fieldInfo['comment'],
                            'match_level' => 'similar_function'
                        ];
                    }
                }
                break;
                
            case 'description':
                // 检查是否有 remark, note, comment 等字段
                foreach ($existingFields as $fieldName => $fieldInfo) {
                    if (strpos($fieldName, 'remark') !== false ||
                        strpos($fieldName, 'note') !== false ||
                        strpos($fieldName, 'comment') !== false ||
                        strpos($fieldName, 'desc') !== false) {
                        $alternatives[] = [
                            'field' => $fieldName,
                            'type' => $fieldInfo['type'],
                            'comment' => $fieldInfo['comment'],
                            'match_level' => 'similar_function'
                        ];
                    }
                }
                break;
                
            case 'config':
                // 检查是否有配置相关字段
                foreach ($existingFields as $fieldName => $fieldInfo) {
                    if (strpos($fieldName, 'config') !== false ||
                        strpos($fieldName, 'setting') !== false ||
                        strpos($fieldName, 'param') !== false ||
                        strpos($fieldName, 'metadata') !== false) {
                        $alternatives[] = [
                            'field' => $fieldName,
                            'type' => $fieldInfo['type'],
                            'comment' => $fieldInfo['comment'],
                            'match_level' => 'exact_function'
                        ];
                    }
                }
                break;
                
            case 'characteristics':
                // 检查是否有特征相关字段
                foreach ($existingFields as $fieldName => $fieldInfo) {
                    if (strpos($fieldName, 'character') !== false ||
                        strpos($fieldName, 'feature') !== false ||
                        strpos($fieldName, 'trait') !== false ||
                        strpos($fieldName, 'property') !== false ||
                        strpos($fieldName, 'attribute') !== false) {
                        $alternatives[] = [
                            'field' => $fieldName,
                            'type' => $fieldInfo['type'],
                            'comment' => $fieldInfo['comment'],
                            'match_level' => 'similar_function'
                        ];
                    }
                }
                break;
                
            case 'version_name':
                // 检查是否有版本相关字段
                foreach ($existingFields as $fieldName => $fieldInfo) {
                    if (strpos($fieldName, 'version') !== false ||
                        strpos($fieldName, 'name') !== false ||
                        strpos($fieldName, 'title') !== false) {
                        $alternatives[] = [
                            'field' => $fieldName,
                            'type' => $fieldInfo['type'],
                            'comment' => $fieldInfo['comment'],
                            'match_level' => 'similar_function'
                        ];
                    }
                }
                break;
                
            case 'generation_config':
                // 检查是否有生成配置相关字段
                foreach ($existingFields as $fieldName => $fieldInfo) {
                    if (strpos($fieldName, 'config') !== false ||
                        strpos($fieldName, 'param') !== false ||
                        strpos($fieldName, 'setting') !== false ||
                        strpos($fieldName, 'generation') !== false) {
                        $alternatives[] = [
                            'field' => $fieldName,
                            'type' => $fieldInfo['type'],
                            'comment' => $fieldInfo['comment'],
                            'match_level' => 'exact_function'
                        ];
                    }
                }
                break;
                
            case 'progress':
                // 检查是否有进度相关字段
                foreach ($existingFields as $fieldName => $fieldInfo) {
                    if (strpos($fieldName, 'progress') !== false ||
                        strpos($fieldName, 'percent') !== false ||
                        strpos($fieldName, 'status') !== false) {
                        $alternatives[] = [
                            'field' => $fieldName,
                            'type' => $fieldInfo['type'],
                            'comment' => $fieldInfo['comment'],
                            'match_level' => 'similar_function'
                        ];
                    }
                }
                break;
                
            case 'result_data':
                // 检查是否有结果数据相关字段
                foreach ($existingFields as $fieldName => $fieldInfo) {
                    if (strpos($fieldName, 'result') !== false ||
                        strpos($fieldName, 'output') !== false ||
                        strpos($fieldName, 'data') !== false ||
                        strpos($fieldName, 'response') !== false) {
                        $alternatives[] = [
                            'field' => $fieldName,
                            'type' => $fieldInfo['type'],
                            'comment' => $fieldInfo['comment'],
                            'match_level' => 'exact_function'
                        ];
                    }
                }
                break;
                
            case 'provider':
                // 检查是否有提供商相关字段
                foreach ($existingFields as $fieldName => $fieldInfo) {
                    if (strpos($fieldName, 'provider') !== false ||
                        strpos($fieldName, 'platform') !== false ||
                        strpos($fieldName, 'vendor') !== false ||
                        strpos($fieldName, 'source') !== false) {
                        $alternatives[] = [
                            'field' => $fieldName,
                            'type' => $fieldInfo['type'],
                            'comment' => $fieldInfo['comment'],
                            'match_level' => 'exact_function'
                        ];
                    }
                }
                break;
                
            default:
                // 通用匹配：查找名称相似的字段
                foreach ($existingFields as $fieldName => $fieldInfo) {
                    $similarity = similar_text(strtolower($missingField), strtolower($fieldName), $percent);
                    if ($percent > 60) {
                        $alternatives[] = [
                            'field' => $fieldName,
                            'type' => $fieldInfo['type'],
                            'comment' => $fieldInfo['comment'],
                            'match_level' => 'name_similar',
                            'similarity' => $percent
                        ];
                    }
                }
                break;
        }
        
        // 输出分析结果
        if (!empty($alternatives)) {
            echo "  ✅ 找到可能的替代字段:\n";
            foreach ($alternatives as $alt) {
                $matchIcon = $alt['match_level'] === 'exact_function' ? '🎯' : 
                           ($alt['match_level'] === 'similar_function' ? '🔄' : '📝');
                echo "    $matchIcon {$alt['field']} ({$alt['type']}) - {$alt['comment']}\n";
                echo "      匹配级别: {$alt['match_level']}\n";
                if (isset($alt['similarity'])) {
                    echo "      相似度: {$alt['similarity']}%\n";
                }
            }
            
            // 选择最佳替代字段
            $bestAlternative = null;
            $bestScore = 0;
            foreach ($alternatives as $alt) {
                $score = 0;
                switch ($alt['match_level']) {
                    case 'exact_function': $score = 100; break;
                    case 'similar_function': $score = 80; break;
                    case 'name_similar': $score = $alt['similarity'] ?? 60; break;
                }
                if ($score > $bestScore) {
                    $bestScore = $score;
                    $bestAlternative = $alt;
                }
            }
            
            if ($bestAlternative) {
                echo "  🏆 推荐替代字段: {$bestAlternative['field']}\n";
                $functionalEquivalent = $bestAlternative['field'];
            }
        } else {
            echo "  ❌ 未找到功能等价的替代字段\n";
        }
        
        $fieldAnalysis[$missingField] = [
            'alternatives' => $alternatives,
            'recommended' => $functionalEquivalent,
            'has_alternative' => !empty($alternatives)
        ];
        
        echo "\n";
    }
    
    $analysisResults[$tableName] = $fieldAnalysis;
    echo str_repeat("-", 40) . "\n\n";
}

// 生成总结报告
echo "📊 总结报告\n";
echo str_repeat("=", 30) . "\n\n";

$totalMissingFields = 0;
$fieldsWithAlternatives = 0;
$fieldsWithoutAlternatives = 0;

foreach ($analysisResults as $tableName => $tableAnalysis) {
    echo "### $tableName\n";
    foreach ($tableAnalysis as $missingField => $analysis) {
        $totalMissingFields++;
        if ($analysis['has_alternative']) {
            $fieldsWithAlternatives++;
            echo "  ✅ $missingField → {$analysis['recommended']}\n";
        } else {
            $fieldsWithoutAlternatives++;
            echo "  ❌ $missingField (无替代字段)\n";
        }
    }
    echo "\n";
}

echo "📈 统计数据:\n";
echo "- 总缺失字段: $totalMissingFields 个\n";
echo "- 有替代字段: $fieldsWithAlternatives 个 (" . round($fieldsWithAlternatives/$totalMissingFields*100, 1) . "%)\n";
echo "- 无替代字段: $fieldsWithoutAlternatives 个 (" . round($fieldsWithoutAlternatives/$totalMissingFields*100, 1) . "%)\n";

// 保存分析结果
file_put_contents('missing_fields_alternatives_analysis.json', json_encode([
    'analysis_time' => date('Y-m-d H:i:s'),
    'total_missing_fields' => $totalMissingFields,
    'fields_with_alternatives' => $fieldsWithAlternatives,
    'fields_without_alternatives' => $fieldsWithoutAlternatives,
    'detailed_analysis' => $analysisResults
], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

echo "\n📄 详细分析结果已保存到: missing_fields_alternatives_analysis.json\n";
echo "\n🎯 结论: " . round($fieldsWithAlternatives/$totalMissingFields*100, 1) . "% 的缺失字段有功能等价的替代实现\n";
