---
description: AI视频创作工具系统架构规范
globs: 
alwaysApply: true
---

# AI视频创作工具系统架构规范

## 📋 项目概述

### 🎯 项目定位
本项目是一个完整的AI视频创作工具生态系统，包含Py视频创作工具、WEB网页工具、管理后台、工具API接口服务和AI服务集成模拟返回数据服务五大核心组件。

**📁 文档路径说明**: 本文档中提到的所有开发规范文档都位于 `@.cursor/rules/` 目录下。

### 🏗️ 系统架构设计原则
- **职责边界清晰**：每个组件职责明确，避免功能重叠
- **服务解耦**：组件间通过标准API接口通信，降低耦合度
- **资源本地化**：用户创作资源由Py视频创作工具直接从AI平台下载到本地
- **可选发布机制**：作品发布为增值服务，用户可选择是否发布到广场

## 🔧 开发环境配置

### 当前开发环境
- **操作系统**: Windows 11
- **Python**: 3.12
- **Web服务器**: Nginx 1.26.2
- **PHP**: 8.1.29
- **数据库**: MySQL 8.0.12
- **缓存**: Redis 7.4.2

### 生产环境规划
- **管理后台**: CentOS 8 Stream + Nginx + PHP + MySQL + Redis
- **工具API接口服务**: CentOS 8 Stream + Nginx + PHP + MySQL + Redis
- **WEB网页工具**: CentOS 8 Stream + Nginx（静态部署）
- **Py视频创作工具**: Windows 和 Mac（客户端应用）
- **AI服务集成模拟返回数据服务**: 本地开发专用，生产环境直连真实AI平台

## 📁 项目目录结构

```
项目根目录/
├── php/
│   ├── backend/          # 管理后台
│   ├── api/              # 工具API接口服务
│   ├── web/              # WEB网页工具
│   ├── aiapi/            # AI服务集成模拟返回数据服务
│   └── thirdapi/         # 第三方服务集成模拟返回数据服务
├── python/               # Py视频创作工具
└── .cursor/
    └── rules/            # 开发规范文档
        ├── index-new.mdc                    # 本文档
        ├── dev-aiapi-guidelines.mdc         # AI服务集成开发规范
        ├── dev-thirdapi-guidelines.mdc      # 第三方服务集成开发规范
        ├── dev-api-guidelines-pyapi.mdc     # Py视频创作工具API接口规范
        ├── dev-api-guidelines-webapi.mdc    # WEB工具API接口规范
        └── dev-api-guidelines-adminapi.mdc  # 管理后台API接口规范
```

## 🎯 核心组件职责定义

### 1. AI服务集成模拟返回数据服务 (@php/aiapi/)

**核心职责**：
- 在保持不同AI平台API接口特性前提下集成在一起
- 根据第三方AI的API接口文档接收和模拟返回数据
- 支持"工具API接口服务"的本地开发

**项目功能**：
- 根据"工具API接口服务"请求不同的AI平台API接口
- 验证接收的数据和模拟各种状态返回处理结果
- 支持5个AI平台：DeepSeek、LiblibAI、KlingAI、MiniMax、火山引擎豆包

**作用期限**：
- 作用于支持"Py视频创作工具"本地开发阶段完成
- "Py视频创作工具"上线后通过配置修改AI平台配置直接绕过本服务
- 请求真实线上AI平台的API接口

**开发规范文档**: `dev-aiapi-guidelines.mdc`

### 2. 第三方服务集成模拟返回数据服务 (@php/thirdapi/)

**核心职责**：
- 在保持不同第三方服务API接口特性前提下集成在一起
- 根据第三方服务的API接口文档接收和模拟返回数据
- 支持"工具API接口服务"的本地开发

**项目功能**：
- 根据"工具API接口服务"请求不同的第三方服务API接口
- 验证接收的数据和模拟各种状态返回处理结果
- 支持微信登录/支付、支付宝支付、短信服务、邮件服务

**作用期限**：
- 作用于支持"Py视频创作工具"本地开发阶段完成
- "Py视频创作工具"上线后通过配置修改第三方服务配置直接绕过本服务
- 请求真实线上第三方服务的API接口

**支持的第三方服务**：
- **微信服务**: OAuth登录、微信支付（统一下单、查询、退款等）
- **支付宝支付**: 统一收单、交易查询、退款处理
- **短信服务**: 阿里云短信、腾讯云短信、验证码验证
- **邮件服务**: SMTP发送、SendCloud、模板邮件

**服务地址**: `https://thirdapi.tiptop.cn/`

**开发规范文档**: `dev-thirdapi-guidelines.mdc`

## 📊 **项目架构图**

### **完整系统架构（环境切换优化版）**
```mermaid
graph TB
    subgraph "用户层 - 视频创作工具"
        A[Py视频创作工具<br/>完整创作功能<br/>客户端视频编辑]
        B[WEB网页工具<br/>✅展示职责：首页工具展示、功能介绍、价格方案<br/>✅用户中心：注册登录、充值积分、积分明细、代理推广、代理结算<br/>✅作品广场：作品展示浏览、分类筛选、搜索查看、作品详情展示<br/>✅响应式设计：PC端、移动端、Py视频创作工具嵌入<br/>❌禁止：视频创作、AI生成、WebSocket通信、作品发布创建]
        M[管理后台<br/>@php/backend/<br/>基于Laravel 10<br/>✅系统配置管理：AI平台配置、系统参数设置<br/>✅用户管理：用户信息、权限管理、积分管理<br/>✅数据统计：业务数据分析、系统监控<br/>✅内容审核：作品审核、资源管理]
    end

    subgraph "业务服务层"
        C[工具API接口服务<br/>@php/api/<br/>基于Lumen 10.x<br/>统一API接口<br/>🚨环境切换机制实现层]
        C1[WebSocket服务<br/>swoole-cli artisan websocket:serve<br/>仅为Py视频创作工具提供实时通信]
        C2[AI资源管理服务<br/>资源生成+版本控制+本地导出<br/>可选：作品发布+审核系统]
    end

    subgraph "环境切换服务客户端层"
        SC1[AiServiceClient<br/>AI服务环境切换<br/>mock/real模式自动切换]
        SC2[ThirdPartyServiceClient<br/>第三方服务环境切换<br/>mock/real模式自动切换]
    end

    subgraph "开发支持层（模拟服务）"
        D[AI服务模拟<br/>@php/aiapi/<br/>本地开发模拟真实AI平台<br/>仅负责模拟，不包含环境切换]
        D2[第三方服务模拟<br/>@php/thirdapi/<br/>模拟微信、支付宝等<br/>仅负责模拟，不包含环境切换]
    end

    subgraph "数据存储层"
        E[MySQL数据库<br/>ai_tool<br/>主存储+事务保证<br/>+AI资源表+版本表+作品广场表]
        F[Redis缓存<br/>WebSocket会话管理<br/>快速查询+状态同步<br/>+资源状态缓存]
    end

    subgraph "真实AI服务（生产环境）"
        G[DeepSeek API<br/>剧情生成]
        H[LiblibAI API<br/>图像生成]
        I[KlingAI API<br/>视频生成]
        J[MiniMax API<br/>语音处理]
        K[火山引擎豆包 API<br/>专业语音AI]
    end

    subgraph "真实第三方服务（生产环境）"
        TP1[微信 API<br/>OAuth认证]
        TP2[支付宝 API<br/>支付服务]
        TP3[短信服务 API<br/>验证码发送]
    end

    %% HTTP API 连接 (蓝色虚线) - 三个工具都使用
    A -.->|🔵 HTTP API<br/>创作功能调用| C
    B -.->|🔵 HTTP API<br/>展示功能+用户中心功能+作品广场功能<br/>❌禁用WebSocket| C
    M -.->|🔵 HTTP API<br/>管理功能调用<br/>AdminApi控制器| C

    %% WebSocket 连接 (绿色粗线) - 仅Py视频创作工具使用
    A ==>|🟢 WebSocket实时通信<br/>AI生成进度推送<br/>任务状态通知| C1

    %% 服务间调用 (红色线) - 避免循环依赖
    C -->|🔴 业务逻辑调用<br/>通过服务客户端| SC1
    C -->|🔴 业务逻辑调用<br/>通过服务客户端| SC2
    C -->|🔴 资源管理调用<br/>版本控制+审核| C2
    C1 -->|🔴 获取API密钥<br/>安全传输| C
    C2 -->|🔴 AI生成调用<br/>资源创建| SC1

    %% 环境切换调用 (紫色线) - 核心机制
    SC1 -->|🟣 开发环境<br/>mock模式| D
    SC1 -->|🟣 生产环境<br/>real模式| G
    SC1 -->|🟣 生产环境<br/>real模式| H
    SC1 -->|🟣 生产环境<br/>real模式| I
    SC1 -->|🟣 生产环境<br/>real模式| J
    SC1 -->|🟣 生产环境<br/>real模式| K
    SC2 -->|🟣 开发环境<br/>mock模式| D2
    SC2 -->|🟣 生产环境<br/>real模式| TP1
    SC2 -->|🟣 生产环境<br/>real模式| TP2
    SC2 -->|🟣 生产环境<br/>real模式| TP3

    %% 数据库连接 (橙色线) - 双重保障
    C -->|🟠 数据存储<br/>事务保证| E
    C -->|🟠 缓存操作<br/>快速查询| F
    C1 -->|🟠 会话管理<br/>状态同步| F
    C2 -->|🟠 资源数据存储<br/>版本管理| E
    C2 -->|🟠 资源状态缓存<br/>快速查询| F

    %% 节点样式 - 高对比度清晰字体
    classDef userLayer fill:#FFFFFF,stroke:#1976D2,stroke-width:3px,color:#000000
    classDef adminLayer fill:#FFFFFF,stroke:#FF9800,stroke-width:3px,color:#000000
    classDef serviceLayer fill:#FFFFFF,stroke:#7B1FA2,stroke-width:3px,color:#000000
    classDef clientLayer fill:#FFFFFF,stroke:#E91E63,stroke-width:3px,color:#000000
    classDef mockLayer fill:#FFFFFF,stroke:#4CAF50,stroke-width:3px,color:#000000
    classDef dataLayer fill:#FFFFFF,stroke:#F57C00,stroke-width:3px,color:#000000
    classDef realAiLayer fill:#FFFFFF,stroke:#388E3C,stroke-width:3px,color:#000000
    classDef realThirdLayer fill:#FFFFFF,stroke:#795548,stroke-width:3px,color:#000000

    class A,B userLayer
    class M adminLayer
    class C,C1,C2 serviceLayer
    class SC1,SC2 clientLayer
    class D,D2 mockLayer
    class E,F dataLayer
    class G,H,I,J,K realAiLayer
    class TP1,TP2,TP3 realThirdLayer
```

### **架构优化说明**

**连接类型说明**:
- **🔵 蓝色虚线**: HTTP API调用 (REST接口，三个工具都使用，职责明确)
- **🟢 绿色粗线**: WebSocket实时通信 (仅Py视频创作工具使用，边界清晰)
- **🔴 红色线**: 服务间调用 (异步事件驱动，避免循环依赖)
- **🟠 橙色线**: 数据库和缓存操作 (双重保障，性能优化)
- **🟣 紫色线**: 环境切换调用 (核心机制，自动路由)

**关键优化特性**:
1. **职责边界清晰**: 每个组件的职责明确定义，避免功能重叠
2. **环境切换机制**: 通过服务客户端实现开发/生产环境无缝切换
3. **避免循环依赖**: 使用异步事件驱动架构，解耦组件间依赖
4. **WebSocket边界明确**: 仅为Py视频创作工具提供实时通信，WEB工具不使用
5. **性能优化设计**: 支持1000并发用户，MySQL+Redis双重保障
6. **安全架构升级**: 密钥安全传输，不持久化存储，权限二次验证
7. **功能模块整合**: 相关功能统一管理，避免分散和重复
8. **🎯 AI资源管理**: 完整的资源生成、版本控制、审核发布体系
9. **🎯 差异化存储**: 图像/音频下载处理，视频仅元数据管理

## 🔄 **业务流程图参考**

所有详细的业务流程图已迁移到专门的图表文档：**[dev-chart-guidelines.mdc](.cursor/rules/dev-chart-guidelines.mdc)**

该文档包含完整的系统架构图和业务流程图，包括：
- Py视频创作工具业务流程图（用户管理、业务功能、项目管理、AI核心流程）
- WEB网页工具业务流程图
- 管理后台业务流程图
- 系统架构图表集合

## 🚨 **AI服务集成模拟机制架构图**

### **环境切换机制核心价值**

**关键价值**：
- ✅ **开发效率**：本地开发无需依赖真实第三方平台
- ✅ **成本控制**：避免开发阶段产生任何真实费用
- ✅ **测试完整性**：可以模拟各种边界情况和异常状态
- ✅ **完全兼容**：确保与真实第三方平台API的100%兼容性
- ✅ **架构纯净**：工具API接口服务保持业务逻辑纯净，无模拟污染
- ✅ **安全隔离**：模拟环境与真实环境完全隔离，无数据泄露风险



### **核心业务流程**

#### Py视频创作工具业务流程C-2: AI生成成功流程（环境切换优化版）
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant W as WebSocket服务
    participant A as 工具API接口服务
    participant SC as AiServiceClient
    participant AI as AI平台/模拟服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant E as 事件总线

    P->>W: 发起AI生成请求
    W->>A: 提交业务信息+请求密钥
    A->>DB: 检查用户积分(事务锁定)
    A->>DB: 扣取积分(冻结状态)
    A->>R: 同步积分状态(缓存更新)
    A->>DB: 写入业务日志(状态:冻结)
    A->>R: 缓存业务日志
    A->>SC: 调用AiServiceClient
    Note over SC: 🚨环境切换：根据AI_SERVICE_MODE<br/>自动路由到mock/real服务
    SC->>AI: 发送AI生成请求
    AI->>SC: 返回AI生成结果
    SC->>A: 返回处理结果
    A->>DB: 确认积分扣取(解冻)
    A->>R: 更新积分缓存
    A->>DB: 更新业务日志(状态:成功)
    A->>R: 更新业务缓存
    A->>E: 发布成功事件(异步)
    A->>W: 返回成功结果
    W->>P: 推送成功通知
```

### **核心业务流程**

#### Py视频创作工具业务流程2: 积分不足业务流程（优化版）
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant W as WebSocket服务
    participant A as 工具API接口服务

    P->>W: 发起AI生成请求
    W->>A: 提交业务信息+请求密钥
    A->>A: 检查用户积分(快速验证)
    Note over A: 积分 < 所需积分
    A->>W: 返回积分不足详细信息
    W->>P: 推送积分不足消息(包含充值建议)
    Note over A: 无扣费操作，保护用户资金
```

### **Py视频创作工具业务流程优化说明**

**环境切换机制优化重点**:
- **🔄 自动环境切换**: AiServiceClient根据配置自动路由到模拟/真实服务
- **🔒 安全性增强**: 密钥加密传输，使用后立即清理，不持久化存储
- **⚡ 性能优化**: 事务锁定机制，缓存同步策略，快速积分验证
- **🔄 异步解耦**: 使用事件总线避免循环依赖，提高系统响应性
- **💰 资金安全**: 积分冻结机制，失败自动返还，事务一致性保证
- **📊 监控完善**: 超时检测自适应，连接中断处理，状态追踪完整
- **🎯 资源管理**: AI资源生成、版本控制、审核发布一体化管理
- **🎯 差异化存储**: 图像/音频下载处理，视频仅元数据，优化存储成本

**积分安全机制升级**:
- ✅ **事务锁定**: 使用数据库事务锁定，确保并发安全
- ✅ **状态流转**: frozen → success/failed，状态机模式
- ✅ **自动返还**: 失败或中断时自动返还，用户资金安全
- ✅ **一致性保证**: MySQL+Redis双重保障，数据一致性

**超时检测机制优化**:
- 🕐 **图像生成**: 5分钟超时（可配置）
- 🕐 **视频生成**: 30分钟超时（可配置）
- 🕐 **文本生成**: 1分钟超时（可配置）
- 🕐 **语音合成**: 2分钟超时（可配置）

### **Py视频创作工具扩展业务流程**

#### 🎯 Py视频创作工具业务流程C-5: AI资源生成与版本管理流程（环境切换优化版）
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant A as 工具API接口服务
    participant RM as AI资源管理服务
    participant SC as AiServiceClient
    participant AI as AI平台/模拟服务
    participant DB as MySQL数据库
    participant R as Redis缓存

    P->>A: 发起AI资源生成请求(含module_id)
    A->>RM: 创建资源记录
    RM->>DB: 创建p_resources记录
    RM->>DB: 自动创建v1.0版本记录
    RM->>A: 返回资源UUID
    A->>SC: 调用AiServiceClient
    Note over SC: 🚨环境切换：根据AI_SERVICE_MODE<br/>自动路由到mock/real服务
    SC->>AI: 调用AI服务生成资源
    AI->>SC: 返回资源URL和元数据
    SC->>A: 返回结果+环境模式信息
    A->>RM: 更新版本信息
    RM->>DB: 更新resource_url、file_size等
    RM->>RM: 执行自动内容审核
    RM->>DB: 更新review_status
    RM->>R: 缓存资源状态
    A->>P: 返回资源信息
    P->>AI: 直接下载资源到本地
    P->>A: 确认下载完成
    A->>RM: 更新下载状态
    RM->>DB: 更新downloaded_by_python=true
```

#### 🎯 Py视频创作工具业务流程C-6: 资源下载完成流程（核心流程）
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant A as 工具API接口服务
    participant SC as AiServiceClient
    participant AI as AI平台/模拟服务
    participant DB as MySQL数据库

    P->>A: 请求资源下载信息(resource_id)
    A->>DB: 查询资源信息和AI平台URL
    DB->>A: 返回resource_url和元数据
    A->>P: 返回AI平台URL和文件信息
    Note over SC: 🚨环境切换：资源URL根据环境<br/>指向模拟服务或真实AI平台
    P->>AI: 直接从AI平台下载资源
    AI->>P: 下载完成
    P->>A: 确认下载完成(local_path)
    A->>DB: 更新下载状态和本地路径
    Note over P: 创作完成，资源已保存到本地
```

#### 🎯 Py视频创作工具业务流程C-7: 可选作品发布流程（增值服务）
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant A as 工具API接口服务
    participant WPS as 作品发布权限服务
    participant WP as 作品广场
    participant DB as MySQL数据库

    Note over P: 用户已完成本地导出
    P->>A: [可选] 请求发布作品(module_type, module_id)
    A->>WPS: 检查发布权限
    WPS->>DB: 查询模块相关资源的review_status

    alt 用户选择发布
        DB->>WPS: review_status = 'approved'/'auto_approved'
        WPS->>A: 返回允许发布
        A->>WP: 创建作品广场记录
        WP->>DB: 保存到p_work_plaza表
        WP->>A: 返回发布成功
        A->>P: 通知发布成功
    else 用户选择不发布
        P->>A: 跳过发布，仅本地保存
        A->>P: 确认完成，无需发布
    end
```

#### 🎯 Py视频创作工具业务流程8: 环境切换机制流程（核心机制）
```mermaid
sequenceDiagram
    participant A as 工具API接口服务
    participant SC as AiServiceClient
    participant Config as 配置系统
    participant Mock as 模拟服务
    participant Real as 真实AI服务

    A->>SC: 请求AI服务调用
    SC->>Config: 读取AI_SERVICE_MODE配置

    alt 开发环境 (mock模式)
        Config->>SC: 返回mode=mock
        SC->>Mock: 调用模拟服务
        Mock->>SC: 返回模拟结果+mode=mock
        SC->>A: 返回结果{success:true, mode:'mock', data:...}
        Note over SC: 开发环境：无真实费用，快速响应
    else 生产环境 (real模式)
        Config->>SC: 返回mode=real
        SC->>Real: 调用真实AI服务
        Real->>SC: 返回真实结果
        SC->>A: 返回结果{success:true, mode:'real', data:...}
        Note over SC: 生产环境：真实调用，产生费用
    end

    Note over SC: 环境切换对业务层透明<br/>统一的调用接口和响应格式
```

#### AI服务集成模拟机制架构图

```mermaid
graph TB
    subgraph "本地开发环境"
        A[Py视频创作工具] --> B[工具API接口服务]
        B --> E[AI服务集成模拟返回数据服务]
        E -.->|仅模拟，不真实调用| F1[DeepSeek API格式模拟<br/>剧情生成/角色生成]
        E -.->|仅模拟，不真实调用| F2[LiblibAI API格式模拟<br/>图像生成/角色生成/风格生成]
        E -.->|仅模拟，不真实调用| F3[KlingAI API格式模拟<br/>图像生成/视频生成/角色生成/风格生成]
        E -.->|仅模拟，不真实调用| F4[MiniMax API格式模拟<br/>全业务支持]
        E -.->|仅模拟，不真实调用| F5[火山引擎豆包 API格式模拟<br/>语音合成/音效生成/音色生成]

        B --> T[第三方服务集成模拟返回数据服务]
        T -.->|仅模拟，不真实调用| G1[微信服务API格式模拟<br/>OAuth登录/微信支付]
        T -.->|仅模拟，不真实调用| G2[支付宝API格式模拟<br/>统一收单/退款查询]
        T -.->|仅模拟，不真实调用| G3[短信服务API格式模拟<br/>阿里云/腾讯云短信]
        T -.->|仅模拟，不真实调用| G4[邮件服务API格式模拟<br/>SMTP/SendCloud]
    end

    subgraph "生产环境"
        A2[Py视频创作工具] --> B2[工具API接口服务]
        B2 --> F6[真实第三方AI平台<br/>DeepSeek/LiblibAI/KlingAI<br/>MiniMax/火山引擎豆包]
        B2 --> G5[真实第三方服务平台<br/>微信/支付宝/阿里云/腾讯云]
    end

    style E fill:#fce4ec,stroke:#e91e63
    style T fill:#fff8e1,stroke:#ff9800
    style F1 fill:#ffebee
    style F2 fill:#ffebee
    style F3 fill:#ffebee
    style F4 fill:#ffebee
    style F5 fill:#ffebee
    style G1 fill:#ffebee
    style G2 fill:#ffebee
    style G3 fill:#ffebee
    style G4 fill:#ffebee
    style F6 fill:#e8f5e8,stroke:#4caf50
    style G5 fill:#e8f5e8,stroke:#4caf50
```

#### AI服务调用流程对比图

```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant API as 工具API接口服务
    participant Mock as AI模拟服务
    participant Real as 真实AI平台

    Note over P,Mock: 本地开发阶段
    P->>API: 请求AI生成（图像/视频/文本/语音等）
    Note over API: 🚫 严禁模拟行为<br/>必须真实调用AI服务
    API->>Mock: 真实调用AI平台格式接口<br/>(DeepSeek/LiblibAI/KlingAI/MiniMax/火山引擎豆包)
    Note over Mock: ✅ 唯一模拟职责<br/>1. 按对应AI平台要求验证参数<br/>2. 模拟对应平台响应状态
    alt 参数验证失败
        Mock->>API: 返回对应AI平台格式参数错误
    else 参数验证通过
        Mock->>API: 模拟成功/失败/超时状态
    end
    API->>P: 透明传递模拟结果

    Note over P,Real: 生产环境
    P->>API: 请求AI生成（图像/视频/文本/语音等）
    Note over API: 🚫 严禁模拟行为<br/>必须真实调用AI服务
    API->>Real: 真实调用对应AI平台<br/>(DeepSeek/LiblibAI/KlingAI/MiniMax/火山引擎豆包)
    Real->>API: 返回真实结果
    API->>P: 透明传递真实结果
```

#### 核心机制说明

**AI服务集成模拟返回数据服务** 的核心作用机制：

1. **接收标准请求**：
   - 接收来自"工具API接口服务"的请求
   - 请求格式完全按照真实第三方AI平台的API文档要求
   - 包含相同的参数结构、认证方式、数据格式

2. **数据验证与模拟响应**：
   - 按照真实AI平台（DeepSeek、LiblibAI、KlingAI、MiniMax、火山引擎豆包）的要求对提交数据进行严格验证
   - **验证不通过**：模拟真实AI平台的参数错误结果返回
   - **验证通过**：模拟成功、失败、超时等其中一种状态返回
   - 返回符合真实API规范的数据结构

3. **支持本地开发**：
   - 让开发者在本地环境就能完整测试所有AI功能
   - 无需真实调用第三方AI平台（避免费用、网络依赖）
   - 可以模拟各种异常情况进行充分测试

4. **环境切换机制**：
   - 本地开发：工具API → 模拟服务 → 模拟响应（**不发生真实第三方调用**）
   - 生产环境：工具API → 真实第三方平台 → 真实响应
   - 通过配置文件轻松切换，无需修改业务代码

#### 🚨 重要说明：模拟服务边界

**模拟服务的工作原理**：
1. **接收请求**：模拟服务接收来自工具API的请求
2. **参数验证**：按照真实第三方平台的要求验证参数
3. **内部模拟**：在模拟服务内部生成符合真实API格式的响应
4. **返回结果**：将模拟结果返回给工具API

**❌ 模拟服务不会做的事情**：
- 不会向真实的第三方平台发起任何网络请求
- 不会产生任何真实的费用
- 不会获取真实的用户数据
- 不会执行真实的业务操作

**✅ 模拟服务会做的事情**：
- 验证请求参数的格式和完整性
- 模拟各种响应状态（成功、失败、超时等）
- 返回符合真实API格式的模拟数据
- 记录详细的调用日志

#### 🚨 关键架构边界规范

**模拟行为边界铁律**：

1. **AI服务集成模拟返回数据服务**：
   - ✅ **唯一模拟职责**：仅在接收数据验证后进行模拟行为
   - ✅ **模拟范围**：数据验证、状态返回、响应格式
   - ✅ **模拟时机**：仅在数据验证完成后执行模拟逻辑

2. **工具API接口服务**：
   - ❌ **严禁模拟行为**：不允许在程序代码中进行任何模拟行为
   - ✅ **真实调用职责**：必须真实调用AI服务（模拟服务或真实服务）
   - ✅ **透明传递**：请求和响应数据必须透明传递，不得修改

3. **架构违规检查**：
   - ❌ 工具API接口服务中出现模拟逻辑代码
   - ❌ 工具API接口服务中硬编码返回模拟数据
   - ❌ 工具API接口服务中包含假数据生成逻辑
   - ❌ 绕过AI服务调用直接返回结果

**关键价值**：
- ✅ **开发效率**：本地开发无需依赖真实第三方平台
- ✅ **成本控制**：避免开发阶段产生任何真实费用
- ✅ **测试完整性**：可以模拟各种边界情况和异常状态
- ✅ **完全兼容**：确保与真实第三方平台API的100%兼容性
- ✅ **架构纯净**：工具API接口服务保持业务逻辑纯净，无模拟污染
- ✅ **安全隔离**：模拟环境与真实环境完全隔离，无数据泄露风险

## 🌐 **WEB网页工具业务流程图**

### **WEB网页工具核心业务流程**

#### WEB业务流程1: 用户注册登录流程
```mermaid
sequenceDiagram
    participant W as WEB网页工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant TP as 第三方服务

    W->>A: 用户注册请求(用户名/邮箱/密码)
    A->>DB: 检查用户名/邮箱是否存在
    alt 用户已存在
        DB->>A: 返回用户已存在
        A->>W: 返回注册失败(用户已存在)
    else 用户不存在
        A->>DB: 创建新用户记录
        A->>TP: 发送验证邮件/短信
        TP->>A: 返回发送结果
        A->>R: 缓存验证码
        A->>W: 返回注册成功(待验证)

        Note over W: 用户点击验证链接
        W->>A: 验证请求(验证码)
        A->>R: 验证验证码
        A->>DB: 激活用户账户
        A->>W: 返回验证成功

        W->>A: 登录请求(用户名/密码)
        A->>DB: 验证用户凭据
        A->>R: 生成并存储Token
        A->>W: 返回登录成功(Token)
    end
```

#### WEB业务流程2: 作品广场浏览流程
```mermaid
sequenceDiagram
    participant W as WEB网页工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存

    W->>A: 请求作品列表(分类/搜索条件)
    A->>R: 检查缓存中的作品列表
    alt 缓存命中
        R->>A: 返回缓存的作品列表
    else 缓存未命中
        A->>DB: 查询作品数据(p_work_plaza)
        DB->>A: 返回作品列表
        A->>R: 更新缓存
    end
    A->>W: 返回作品列表(含缩略图)

    Note over W: 用户点击查看作品详情
    W->>A: 请求作品详情(work_id)
    A->>DB: 查询作品详细信息
    A->>DB: 更新作品浏览次数
    A->>W: 返回作品详情(含高清图/视频)
```

#### WEB业务流程3: 用户中心管理流程
```mermaid
sequenceDiagram
    participant W as WEB网页工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant TP as 第三方支付

    W->>A: 请求用户信息(Token)
    A->>R: 验证Token有效性
    A->>DB: 查询用户详细信息
    A->>W: 返回用户信息(积分/等级/统计)

    Note over W: 用户充值积分
    W->>A: 发起充值请求(金额)
    A->>TP: 调用支付接口
    TP->>A: 返回支付链接
    A->>W: 返回支付链接

    Note over W: 用户完成支付
    TP->>A: 支付回调通知
    A->>DB: 更新用户积分
    A->>R: 更新积分缓存
    A->>W: 推送充值成功通知
```

## 🏢 **管理后台业务流程图**

### **管理后台核心业务流程**

#### 管理后台业务流程1: 系统配置管理流程
```mermaid
sequenceDiagram
    participant M as 管理后台
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant AI as AI平台配置

    M->>A: 管理员登录(用户名/密码)
    A->>DB: 验证管理员权限
    A->>R: 生成管理员Token
    A->>M: 返回登录成功(AdminToken)

    M->>A: 请求AI平台配置列表
    A->>DB: 查询AI平台配置
    A->>M: 返回配置列表(API密钥/地址)

    Note over M: 管理员修改AI平台配置
    M->>A: 更新AI平台配置(平台/密钥/地址)
    A->>AI: 测试新配置连通性
    alt 配置测试成功
        A->>DB: 保存新配置
        A->>R: 更新配置缓存
        A->>M: 返回配置更新成功
    else 配置测试失败
        A->>M: 返回配置测试失败(错误详情)
    end
```

#### 管理后台业务流程2: 用户管理流程
```mermaid
sequenceDiagram
    participant M as 管理后台
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存

    M->>A: 请求用户列表(分页/筛选条件)
    A->>DB: 查询用户数据(p_users)
    A->>M: 返回用户列表(基本信息/状态)

    Note over M: 管理员查看用户详情
    M->>A: 请求用户详情(user_id)
    A->>DB: 查询用户详细信息
    A->>DB: 查询用户积分记录
    A->>DB: 查询用户作品统计
    A->>M: 返回用户完整信息

    Note over M: 管理员操作用户账户
    M->>A: 用户账户操作(禁用/启用/积分调整)
    A->>DB: 更新用户状态/积分
    A->>R: 更新用户缓存
    alt 账户被禁用
        A->>R: 清除用户Token
    end
    A->>M: 返回操作成功
```

#### 管理后台业务流程3: 内容审核管理流程
```mermaid
sequenceDiagram
    participant M as 管理后台
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant U as 用户通知

    M->>A: 请求待审核内容列表
    A->>DB: 查询待审核作品(p_work_plaza)
    A->>M: 返回待审核列表(作品信息/提交时间)

    Note over M: 管理员审核作品
    M->>A: 请求作品详情(work_id)
    A->>DB: 查询作品完整信息
    A->>M: 返回作品详情(内容/资源/用户信息)

    M->>A: 提交审核结果(通过/拒绝/原因)
    A->>DB: 更新作品审核状态
    alt 审核通过
        A->>DB: 发布作品到广场
        A->>R: 更新作品缓存
        A->>U: 发送审核通过通知
    else 审核拒绝
        A->>U: 发送审核拒绝通知(含原因)
    end
    A->>M: 返回审核操作成功
```

#### 管理后台业务流程4: 数据统计分析流程
```mermaid
sequenceDiagram
    participant M as 管理后台
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存

    M->>A: 请求系统统计数据(时间范围)
    A->>R: 检查统计数据缓存
    alt 缓存命中且未过期
        R->>A: 返回缓存的统计数据
    else 缓存未命中或已过期
        A->>DB: 查询用户统计数据
        A->>DB: 查询收入统计数据
        A->>DB: 查询AI使用统计
        A->>DB: 查询作品发布统计
        A->>A: 汇总分析统计数据
        A->>R: 缓存统计结果(30分钟)
    end
    A->>M: 返回统计报表(图表数据)

    Note over M: 管理员导出报表
    M->>A: 请求导出报表(格式/范围)
    A->>A: 生成报表文件(Excel/PDF)
    A->>M: 返回报表下载链接
```

#### 管理后台业务流程5: AI平台使用统计分析流程 🚨 新增
```mermaid
sequenceDiagram
    participant M as 管理后台
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant PUS as platform_usage_statistics表
    participant UMP as user_model_preferences表
    participant R as Redis缓存

    Note over M: AI平台使用概览统计
    M->>A: 请求AI平台使用概览(时间范围)
    A->>R: 检查平台统计缓存
    alt 缓存未命中
        A->>PUS: 查询各平台使用统计
        A->>PUS: 聚合成功率、响应时间、用户满意度
        A->>DB: 查询AI任务总量和成本
        A->>A: 计算平台性能评分和推荐指数
        A->>R: 缓存统计结果(30分钟过期)
    end
    A->>M: 返回平台概览(使用量/成功率/满意度/成本分析)

    Note over M: 用户平台偏好分析
    M->>A: 请求用户平台偏好分析
    A->>UMP: 查询用户偏好设置统计
    A->>PUS: 查询用户实际使用行为
    A->>A: 分析偏好设置vs实际使用差异
    A->>M: 返回偏好分析(设置偏好/实际使用/推荐命中率)

    Note over M: 平台性能对比分析
    M->>A: 请求平台性能对比(任务类型)
    A->>PUS: 按任务类型查询各平台性能指标
    A->>A: 计算平均响应时间、成功率、用户评分
    A->>A: 生成性能对比图表数据
    A->>M: 返回性能对比(响应时间/成功率/满意度对比)

    Note over M: 平台成本效益分析
    M->>A: 请求平台成本效益分析
    A->>PUS: 查询各平台成本和使用量数据
    A->>DB: 查询平台定价配置
    A->>A: 计算成本效益比和ROI指标
    A->>M: 返回成本分析(成本分布/效益对比/优化建议)
```

#### 管理后台业务流程6: AI平台配置优化流程 🚨 新增
```mermaid
sequenceDiagram
    participant M as 管理后台
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant PUS as platform_usage_statistics表
    participant AI as AI平台配置
    participant R as Redis缓存

    Note over M: 平台配置智能优化建议
    M->>A: 请求平台配置优化建议
    A->>PUS: 分析各平台历史性能数据
    A->>PUS: 计算平台稳定性和可靠性指标
    A->>A: 基于数据生成优化建议
    A->>M: 返回优化建议(默认平台调整/权重配置/禁用建议)

    Note over M: 管理员应用优化配置
    M->>A: 应用平台配置优化(配置参数)
    A->>AI: 更新AI平台配置
    A->>R: 清除相关配置缓存
    A->>DB: 记录配置变更日志
    A->>A: 验证新配置有效性
    alt 配置验证成功
        A->>M: 返回配置更新成功
        A->>R: 缓存新配置(实时生效)
    else 配置验证失败
        A->>AI: 回滚到原配置
        A->>M: 返回配置更新失败(错误详情)
    end

    Note over M: 平台使用策略调整
    M->>A: 调整平台推荐策略(策略参数)
    A->>DB: 更新推荐算法权重配置
    A->>R: 清除推荐缓存
    A->>A: 重新计算用户推荐
    A->>M: 返回策略调整成功

    Note over M: 实时监控平台状态
    M->>A: 启动平台实时监控
    A->>AI: 定期检测各平台健康状态
    A->>PUS: 实时更新平台性能指标
    A->>R: 更新平台状态缓存
    A->>M: 推送平台状态变化通知
```

### 3. 工具API接口服务 (@php/api/)

**核心职责**：
- 本地开发阶段依赖"AI服务集成模拟返回数据服务"和"第三方服务集成模拟返回数据服务"
- 集成多家AI平台的API接口提供对应AI平台的API接口服务
- 集成多种第三方服务的API接口（微信、支付宝、短信、邮件等）
- 开发支持"Py视频创作工具"的AI视频创作功能
- 支持"WEB网页工具"作品广场和"管理后台"功能实现

#### 3.1 Py视频创作工具的API接口

**控制器目录**: `@php/api/app/Http/Controllers/PyApi`
**业务层目录**: `@php/api/app/Services/PyApi`

**业务逻辑职责**：
- 创建AI创作视频任务
- AI任务调度（含文生文、图生图、图生视频、生成语音、生成音效、生成音乐等所有需要AI的功能）
- 数据处理
- 作品发布
- 用户注册登录
- 用户资料修改与密码找回
- 用户认证
- 充值积分
- 积分管理
- 积分明细
- 代理推广
- 代理结算

**WebSocket服务职责**：
- 仅为Py视频创作工具提供实时通信（AI生成进度推送）

**不包含职责**：
- 不储存且不中转用户创作过程中AI生成的资源
- 视频编辑处理
- 客户端UI逻辑
- 本地文件操作

#### 3.2 WEB网页工具的API接口

**控制器目录**: `@php/api/app/Http/Controllers/WebApi`
**业务层目录**: `@php/api/app/Services/WebApi`

**业务逻辑职责**：
- 功能介绍查询
- 价格方案查询
- 作品数据查询（支持分类筛选、搜索查看、作品详情展示）
- 用户注册登录
- 用户资料修改与密码找回
- 用户认证
- 充值积分
- 积分管理
- 积分明细
- 代理推广
- 代理结算

**响应式设计**：
- 支持PC端、移动端、Py视频创作工具嵌入（1200px/800px窗口）

**不包含职责**：
- 视频创作功能
- AI生成功能
- WebSocket实时通信
- 作品发布创建



#### **WEB网页工具职责边界规范**

**📊 现有业务流程图表**：
详细的WEB网页工具业务流程图请参考 **[dev-chart-guidelines.mdc](.cursor/rules/dev-chart-guidelines.mdc)** 文档中的"WEB网页工具业务流程图"章节：
- **WEB网页工具1**: 用户注册登录流程
- **WEB网页工具2**: 作品广场浏览流程
- **WEB网页工具3**: 用户中心管理流程

**🏗️ 架构说明**：WEB网页工具的完整架构已在 **[dev-chart-guidelines.mdc](.cursor/rules/dev-chart-guidelines.mdc)** 的"完整系统架构图（环境切换优化版）"中体现，包含前端展示层、WebApi控制器层、服务层和数据存储层的完整设计。

##### **✅ 允许的功能职责**
1. **展示职责**：
   - 首页工具展示和功能介绍
   - 价格方案展示和对比
   - 平台统计数据展示
   - 公告和帮助信息展示

2. **用户中心职责**：
   - 用户注册、登录、认证
   - 用户资料管理和修改
   - 密码找回和安全设置
   - 积分查询、充值和明细

3. **作品广场职责**：
   - 作品展示和浏览
   - 分类筛选和搜索
   - 作品详情查看
   - 作品互动（点赞、分享）

4. **代理推广职责**：
   - 代理申请和管理
   - 推广统计和数据
   - 佣金查询和结算
   - 推广链接生成

5. **响应式设计职责**：
   - PC端完整功能体验
   - 移动端优化适配
   - Py视频创作工具嵌入适配

##### **❌ 禁止的功能职责**
1. **创作功能禁止**：
   - 视频创作和编辑
   - AI内容生成
   - 素材处理和合成
   - 本地文件操作

2. **实时通信禁止**：
   - WebSocket连接
   - 实时进度推送
   - 即时消息通信
   - 长连接维护

3. **作品发布禁止**：
   - 作品创建和上传
   - 作品发布到广场
   - 作品审核管理
   - 作品版本控制

4. **高级管理禁止**：
   - 系统配置管理
   - 用户权限管理
   - 数据统计分析
   - 内容审核操作

##### **📋 WEB网页工具API接口列表**

###### **首页展示接口组**
```
GET  /api/web/home/<USER>
GET  /api/web/home/<USER>
GET  /api/web/home/<USER>
GET  /api/web/home/<USER>
```

###### **用户管理接口组**
```
POST /api/web/user/register          # 用户注册
POST /api/web/user/login             # 用户登录
POST /api/web/user/logout            # 用户登出
GET  /api/web/user/profile           # 获取用户资料
PUT  /api/web/user/profile           # 更新用户资料
POST /api/web/user/change-password   # 修改密码
POST /api/web/user/forgot-password   # 忘记密码
POST /api/web/user/verify-email      # 邮箱验证
POST /api/web/user/verify-phone      # 手机验证
GET  /api/web/user/points            # 获取积分信息
GET  /api/web/user/points/history    # 获取积分明细
```

###### **作品广场接口组**
```
GET  /api/web/works                  # 获取作品列表
GET  /api/web/works/{id}             # 获取作品详情
GET  /api/web/works/categories       # 获取作品分类
GET  /api/web/works/search           # 搜索作品
GET  /api/web/works/trending         # 获取热门作品
GET  /api/web/works/latest           # 获取最新作品
POST /api/web/works/{id}/view        # 记录作品浏览
POST /api/web/works/{id}/like        # 作品点赞
POST /api/web/works/{id}/share       # 作品分享
```

###### **代理推广接口组**
```
POST /api/web/agent/apply            # 申请成为代理
GET  /api/web/agent/info             # 获取代理信息
GET  /api/web/agent/statistics       # 获取推广统计
GET  /api/web/agent/commissions      # 获取佣金记录
POST /api/web/agent/withdraw         # 申请提现
GET  /api/web/agent/withdraw/history # 提现记录
GET  /api/web/agent/referrals        # 推荐用户列表
```

###### **支付充值接口组**
```
POST /api/web/payment/create         # 创建支付订单
GET  /api/web/payment/methods        # 获取支付方式
POST /api/web/payment/callback       # 支付回调处理
GET  /api/web/payment/orders         # 获取订单列表
GET  /api/web/payment/orders/{id}    # 获取订单详情
POST /api/web/payment/cancel         # 取消订单
```



#### 3.3 支持"管理后台"的API接口

**控制器目录**: `@php/api/app/Http/Controllers/AdminApi`
**业务层目录**: `@php/api/app/Services/AdminApi`

**业务逻辑职责**：
- 系统配置管理（AI平台配置、系统参数设置）
- 用户管理（用户信息、权限管理、账户状态）
- 内容管理（作品审核、内容监控、违规处理）
- 数据统计（用户统计、收入统计、使用情况分析）
- 积分系统管理（积分规则、充值记录、消费明细）
- 代理系统管理（代理审核、佣金结算、推广数据）
- 素材库管理（音色库、音效库、音乐库、风格库、角色库）
- 系统监控（性能监控、错误日志、API调用统计）
- 财务管理（收入报表、退款处理、财务对账）

### 4. Py视频创作工具 (@python/)

**技术栈**: Python + PySide6 + PyInstaller + WebSocket客户端

**核心创作职责**（调用"Py视频创作工具的API接口"支持）：
- 选风格+写剧情
- 绑角色
- 生成图像
- 视频编辑
- 本地导出

**可选发布职责**（调用"Py视频创作工具的API接口"支持）：
- 作品发布到广场（用户自主选择）

**客户端处理职责**（调用"Py视频创作工具的API接口"支持）：
- 资源本地化
- 视频时间轴编辑
- 本地素材合成
- UI交互逻辑
- 作品导出

**用户中心职责**（调用"用户API接口"支持）：
- 用户注册登录
- 用户资料修改与密码找回
- 用户认证
- 充值积分
- 积分管理
- 积分明细
- 代理推广
- 代理结算

**实时通信职责**（调用"Py视频创作工具的API接口"支持）：
- 通过WebSocket接收AI生成进度推送

### 5. WEB网页工具 (@php/web/)

**展示职责**（调用"WEB网页工具的API接口"支持）：
- 首页工具展示
- 功能介绍
- 价格方案
- 作品展示

**用户中心职责**（调用"用户API接口"支持）：
- 用户注册登录
- 用户资料修改与密码找回
- 用户认证
- 充值积分
- 积分管理
- 积分明细
- 代理推广
- 代理结算

**作品广场职责**：
- 作品展示浏览
- 分类筛选
- 搜索查看
- 作品详情展示

**响应式设计**：
- 支持PC端、移动端

**不包含职责**：
- 视频创作功能
- AI生成功能
- WebSocket实时通信
- 作品发布创建

### 6. 管理后台 (@php/backend/)

**基于Laravel 10开发的管理后台**

**数据管理职责**：
- AI引擎配置
- 音色库、音效库、音乐库管理
- 风格库、角色库管理
- 作品库、会员库管理
- 积分明细管理

**配置管理职责**：
- 第三方AI的API接口地址和密钥管理
- 系统参数配置
- 业务规则配置

**系统管理职责**：
- 用户权限管理
- 系统监控
- 数据统计分析
- 内容审核管理

**🚨 AI平台管理职责（新增）**：
- AI平台使用统计分析（各平台使用量、成功率、响应时间）
- 用户平台偏好分析（偏好设置vs实际使用行为对比）
- 平台性能对比分析（按任务类型的性能指标对比）
- 平台成本效益分析（成本分布、效益对比、优化建议）
- 平台配置智能优化（基于数据的配置建议和策略调整）

### 🎬 视频创作项目管理规范（严格遵循规范版）

#### **项目创建流程规范**
🚨 **严格规范要求**：
- 工具API接口服务严禁储存或中转用户创作视频产生的任何资源文件
- 项目创建流程仅处理文本数据，不涉及任何文件操作
- 不创建文件夹、不存储资源文件、不中转资源文件
- 所有资源文件操作在C-1: AI任务调度流程中由其他服务处理

优化后的项目创建流程，集成风格选择和AI文本处理：

**1. 风格模板获取接口**
```http
GET /py-api/projects/style-templates
Authorization: Bearer {token}
```
返回左侧风格图片列表，包含：卡通可爱、写实风格、科幻风格、古风、现代等

**2. 项目创建接口（优化版）**
```http
POST /py-api/projects/create-with-story
Authorization: Bearer {token}
Content-Type: application/json

{
    "style_id": 1,                    // 选择的风格ID
    "content_type": "own_story",      // "own_story" 或 "story_library"
    "story_content": "用户输入的故事内容或提示词",
    "additional_config": {
        "target_audience": "儿童(3-8岁)"
    }
}
```

**3. 内容类型处理（仅文本数据）**：

**自有故事剧情分镜 (own_story)**：
- 用户在多行输入框录入完整故事文本
- AI处理：仅做文字分镜处理，提取项目标题
- 不对原故事进行任何补充、完善或修改
- AI Prompt: "对以下故事进行文字分镜，提取标题，不修改原故事"
- 输出：分镜文字描述（JSON格式），不产生图片或视频

**AI故事分镜 (ai_story)**：
- 用户在多行输入框录入提示词或故事大纲
- AI处理：生成完整故事文本 + 文字分镜 + 提取标题
- AI Prompt: "根据以下大纲生成完整故事文本并进行文字分镜"
- 输出：故事文本 + 分镜文字描述（JSON格式），不产生图片或视频

**4. 数据库表结构（优化）**：
- **p_projects**: 主项目表
  - `style_id` - 风格模板ID
  - `content_type` - 内容类型 (own_story/ai_story)
  - `title` - AI提取的项目标题
  - `story_content` - 故事内容
  - `storyboard` - 分镜结果
  - `status` - 项目状态 (processing/created/in_progress)

**5. 项目状态管理**：
- `processing` - AI处理中
- `created` - 创建完成
- `in_progress` - 创作中
- `completed` - 已完成
- `published` - 已发布

**6. 返回数据结构（纯文本数据）**：
```json
{
    "success": true,
    "data": {
        "project_id": 12345,
        "title": "AI提取的项目标题",
        "story_content": "完整故事文本内容",
        "storyboard_text": [
            {
                "scene_id": 1,
                "description": "场景文字描述",
                "estimated_duration": "3-5秒",
                "scene_type": "对话场景"
            }
        ],
        "style_config": {
            "style_name": "卡通可爱",
            "style_id": 1,
            "description": "适合儿童的卡通风格"
        }
    }
}
```

⚠️ **注意**：返回的storyboard_text是文字描述，不包含任何图片、视频等资源文件。资源文件将在C-1: AI任务调度流程中根据这些文字描述生成。

**7. 用户体验优化规范（WebSocket实时进度）**：

**提交时的界面反馈**：
- 点击提交按钮后界面保持不变
- 显示处理罩层 + 实时进度条
- 禁用提交按钮，防止重复提交
- 建立WebSocket连接，订阅项目处理进度频道

**WebSocket进度推送规范**：
```javascript
// WebSocket连接
const ws = new WebSocket('ws://localhost:9502');
ws.send(JSON.stringify({
    action: 'subscribe',
    channel: `project_progress_${project_id}`,
    token: userToken
}));

// 进度消息格式
{
    "type": "progress_update",
    "data": {
        "project_id": 12345,
        "progress": 30,
        "status": "分析故事结构",
        "timestamp": "2024-01-01 12:00:00"
    }
}
```

**进度阶段定义**：
- **10%**: "开始AI处理"
- **20-30%**: "分析故事结构/大纲"
- **50-60%**: "生成故事内容/分镜脚本"
- **70-80%**: "创建分镜脚本/提取标题"
- **85%**: "优化分镜内容"
- **90%**: "保存项目数据"
- **100%**: "项目创建完成"

**前端进度显示**：
```html
<div class="progress-overlay">
    <div class="progress-container">
        <div class="progress-bar">
            <div class="progress-fill" style="width: 30%"></div>
        </div>
        <div class="progress-text">30% - 分析故事结构</div>
        <div class="progress-animation">
            <div class="spinner"></div>
        </div>
    </div>
</div>
```

**处理完成后的界面更新**：
- 关闭WebSocket连接，取消订阅进度频道
- 隐藏处理罩层和进度条
- 在原多行输入框位置渲染剧情分镜列表
- 激活"下一步：绑角色"按钮，变为彩色
- 表示满足进入下一步的条件

**8. 分镜编辑功能规范**：

**分镜列表显示**：
```html
<div class="storyboard-item">
    <div class="scene-number">场景 1</div>
    <div class="scene-description">小猫咪站在森林入口...</div>
    <div class="scene-duration">3-5秒</div>
    <div class="scene-actions">
        <button class="edit-btn">编辑</button>
        <button class="merge-btn">合并</button>
        <button class="delete-btn">删除</button>
        <button class="drag-handle">⋮⋮</button>
    </div>
</div>
```

**支持的编辑操作**：
- **手动编辑**: 点击编辑按钮，弹出编辑框修改分镜内容
- **合并分镜**: 选择相邻分镜进行合并操作
- **拖拽排序**: 通过拖拽手柄调整分镜顺序
- **删除分镜**: 删除不需要的分镜片段
- **添加分镜**: 在任意位置插入新的分镜

**分镜编辑API接口**：
```http
PUT /py-api/projects/{project_id}/storyboard
Authorization: Bearer {token}
Content-Type: application/json

{
    "storyboard": [
        {
            "scene_id": 1,
            "description": "修改后的场景描述",
            "duration": "4秒",
            "order": 1
        }
    ]
}
```

**9. WebSocket服务端实现规范**：

**进度推送服务**：
```php
// 在AI处理过程中推送进度
class ProjectProgressService {
    public function pushProgress($projectId, $progress, $status) {
        $message = [
            'type' => 'progress_update',
            'data' => [
                'project_id' => $projectId,
                'progress' => $progress,
                'status' => $status,
                'timestamp' => date('Y-m-d H:i:s')
            ]
        ];

        // 推送到WebSocket服务
        WebSocketService::pushToChannel(
            "project_progress_{$projectId}",
            $message
        );
    }
}

// 在API控制器中调用
$progressService = new ProjectProgressService();
$progressService->pushProgress($projectId, 30, '分析故事结构');
```

**WebSocket频道管理**：
- 频道命名：`project_progress_{project_id}`
- 订阅验证：验证用户Token和项目权限
- 自动清理：项目完成后自动清理频道订阅

**错误处理**：
- WebSocket连接失败时降级到轮询模式
- 进度推送失败时记录日志但不影响主流程
- 超时处理：超过5分钟无进度更新时显示错误提示

**10. UI业务逻辑处理（文本数据展示）**：
项目创建成功后，Py视频创作工具接收完整项目文本数据，用于：
- 显示项目标题和故事文本内容
- 渲染可编辑的分镜文字描述列表
- 应用选择的风格配置信息
- 激活"下一步：绑角色"按钮状态
- 准备进入C-1: AI任务调度流程（资源文件生成阶段）
- 清理WebSocket连接和进度状态

**重要提醒**：
- 此阶段不涉及任何资源文件的生成、存储或展示
- 分镜列表显示的是文字描述，不是图片预览
- 所有图片、视频、音频等资源文件将在后续的AI任务调度流程中生成
- 工具API接口服务严格遵循"不中转或储存用户创作视频产生的任何资源文件"的规范

### 🎯 AI平台选择接口规范（核心功能）

#### **优化后的一键智能推荐接口**
在Py视频创作工具开发中，使用一个核心接口实现智能平台选择功能：

**核心接口125 - 一键智能推荐（优化版）**
```http
POST /py-api/ai-models/select-platform
Authorization: Bearer {token}
Content-Type: application/json

{
    "business_type": "video",
    "auto_recommend": true,
    "criteria": {
        "priority": "quality"
    }
}
```
- **功能**：一键获取最佳推荐 + 备选方案
- **参数**：
  - `business_type` (必需) - 业务类型：`video`/`image`/`voice`/`sound`/`music`/`text`
  - `auto_recommend` (可选，默认true) - 自动推荐模式
  - `criteria` (可选) - 选择标准配置
- **返回**：
  ```json
  {
    "recommended": {
      "platform": "KlingAI",
      "reason": "质量最佳，适合您的使用习惯"
    },
    "alternatives": [
      {"platform": "LiblibAI", "reason": "速度更快"},
      {"platform": "MiniMax", "reason": "成本更低"}
    ]
  }
  ```
- **使用场景**：页面加载时自动调用，为用户提供即时推荐

**备用接口131/132 - 详细选项（按需使用）**
```http
GET /py-api/ai-models/platform-options?task_type={task_type}
GET /py-api/ai-models/user-recommendations?task_type={task_type}
```
- **功能**：获取详细的平台选项和推荐列表
- **使用场景**：仅在用户点击"查看更多平台"时调用
- **优势**：减少不必要的接口调用，提升响应速度

#### **开发实现规范（优化版）**

**1. 简化的用户体验流程**：
```javascript
// 页面加载时 - 一键获取推荐
async function initializeVideoCreation() {
    try {
        const result = await selectOptimalPlatform({
            business_type: 'video',
            auto_recommend: true
        });

        // 直接显示推荐结果
        showRecommendation(result.recommended);

        // 预加载备选方案（用户可能需要）
        preloadAlternatives(result.alternatives);

    } catch (error) {
        // 降级到默认平台
        useDefaultPlatform('KlingAI');
    }
}

// 用户交互 - 简化到最少步骤
function handleUserChoice() {
    // 大多数用户：直接确认推荐
    if (userClicksStartCreation()) {
        startVideoCreation(recommendedPlatform);
    }

    // 少数用户：查看更多选项
    if (userClicksMoreOptions()) {
        showAlternativePlatforms();
    }
}
```

**2. 性能优化策略**：
- **预加载推荐**：页面加载时立即获取推荐，无需用户等待
- **智能缓存**：推荐结果缓存2分钟，备选方案缓存5分钟
- **降级机制**：接口失败时自动使用默认平台，不阻塞用户操作

**3. 用户体验优化**：
- **零等待**：推荐结果即时显示，无需用户选择
- **一键确认**：90%用户场景只需点击"开始创作"
- **可选展开**：需要时才显示详细选项，避免信息过载

**4. 简化的数据流**：
```
页面加载 → 一键推荐接口 → 显示结果 → 用户确认 → 开始创作
```

#### **架构设计原则**

**1. 统一接口设计**：
- 所有平台选择功能统一在 `AiModelController` 中实现
- 避免在各个业务控制器中重复实现相同功能
- 提供通用的参数化接口，支持所有任务类型

**2. 智能推荐算法**：
- 基于用户历史使用记录分析偏好
- 考虑平台当前健康状态和性能指标
- 结合任务特性和用户设定的优先级

**3. 用户偏好学习**：
- 记录用户的每次平台选择行为
- 分析选择模式（手动选择vs智能推荐）
- 持续优化推荐算法的准确性

**4. 向后兼容性**：
- 保留各控制器中的原有方法，确保现有功能不受影响
- 新项目优先使用统一接口
- 提供迁移指南，支持渐进式升级
- 实时平台状态监控（健康状态检测、性能指标更新）

## 🔄 项目依赖关系

```mermaid
graph TB
    subgraph "本地开发环境"
        A[Py视频创作工具] --> B[工具API接口服务]
        C[WEB网页工具] --> B
        D[管理后台] --> B
        B --> E[AI服务集成模拟返回数据服务]
        B --> T[第三方服务集成模拟返回数据服务]
        E -.->|仅模拟，不真实调用| F1[AI平台API格式模拟]
        T -.->|仅模拟，不真实调用| G1[第三方服务API格式模拟]
    end

    subgraph "生产环境"
        A2[Py视频创作工具] --> B2[工具API接口服务]
        C2[WEB网页工具] --> B2
        D2[管理后台] --> B2
        B2 --> F2[真实第三方AI平台]
        B2 --> G2[真实第三方服务平台]
    end

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
    style T fill:#fff8e1
    style F1 fill:#ffebee
    style G1 fill:#ffebee
    style F2 fill:#e8f5e8
    style G2 fill:#e8f5e8
```

**依赖说明**：
- **工具API接口服务**: 依赖"AI服务集成模拟返回数据服务"和"第三方服务集成模拟返回数据服务"提供API接口支持本地开发
- **Py视频创作工具**: 依赖"工具API接口服务"项目提供API接口实现所有功能的开发
- **WEB网页工具**: 依赖"工具API接口服务"项目提供API接口实现所有功能的开发
- **管理后台**: 依赖"工具API接口服务"项目提供API接口实现所有功能的开发

## 🤖 AI模型配置信息

### 支持的AI平台列表
- **LiblibAI**: 图像生成专业平台
- **KlingAI**: 视频生成领导者
- **MiniMax**: 多模态AI平台
- **DeepSeek**: 剧情生成和分镜脚本专家
- **火山引擎豆包**: 专业语音AI平台

### 禁止使用的模型
- OpenAI、GPT系列模型
- anthropic、Claude系列模型

### 业务模型配置矩阵

#### 图像生成业务
**可选平台**: LiblibAI + KlingAI + MiniMax
- **LiblibAI**: 专业图像生成、ComfyUI工作流、风格转换
- **KlingAI**: 高质量图像生成、图像放大、图像修复
- **MiniMax**: 多模态图像生成、图像理解

#### 视频生成业务
**可选平台**: KlingAI + MiniMax
- **KlingAI**: 专业视频生成、图像转视频、视频扩展
- **MiniMax**: 多模态视频生成、视频理解

#### 剧情生成业务
**可选平台**: DeepSeek + MiniMax
- **DeepSeek**: 专业剧情创作、分镜脚本、角色对话
- **MiniMax**: 多模态剧情生成、情节构建

#### 角色生成业务
**可选平台**: LiblibAI + KlingAI + MiniMax
- **LiblibAI**: 角色形象生成、角色设计
- **KlingAI**: 角色动画生成、角色表情
- **MiniMax**: 角色属性生成、角色对话

#### 风格生成业务
**可选平台**: LiblibAI + KlingAI + MiniMax
- **LiblibAI**: 艺术风格生成、风格转换
- **KlingAI**: 视觉风格生成、风格应用
- **MiniMax**: 多模态风格生成、风格理解

#### 音效生成业务
**可选平台**: 火山引擎豆包 + MiniMax
- **火山引擎豆包**: 专业音效处理、音效合成
- **MiniMax**: 多模态音效生成、音效理解

#### 音色生成业务
**可选平台**: MiniMax + 火山引擎豆包
- **MiniMax**: 音色设计、音色合成
- **火山引擎豆包**: 声音复刻、音色处理

#### 音乐生成业务
**可选平台**: MiniMax
- **MiniMax**: 专业音乐生成、音乐创作、音乐理解

#### 语音处理业务
**可选平台**: 火山引擎豆包 + MiniMax
- **火山引擎豆包**: 专业语音合成、音效生成、音色生成
- **MiniMax**: 多模态语音处理、语音理解

## 🔐 Token认证机制规范

### AuthService认证机制
工具API接口服务使用统一的AuthService认证机制，支持两种Token传递方式：

#### 支持的认证方式
1. **Bearer Token方式** (推荐)：
   ```
   Authorization: Bearer {token}
   ```
   - 标准HTTP Bearer Token格式
   - 符合RFC 6750规范
   - 适用于所有API接口

2. **URL参数方式** (兼容性)：
   ```
   ?token={token}
   ```
   - 通过URL参数传递Token
   - 便于快速测试和调试
   - 与Bearer Token方式等效

#### 不支持的认证方式
- **无Bearer前缀的Authorization头**: `Authorization: {token}` ❌ 失败
- **无认证访问**: 直接访问受保护接口 ❌ 失败

#### AuthService.extractToken()处理逻辑
```php
// 优先级1: 从请求参数中获取token参数
$token = $request->input('token');

// 优先级2: 从Authorization头中提取Bearer Token
if (empty($token)) {
    $header = $request->header('Authorization', '');
    $position = strrpos($header, 'Bearer ');
    if ($position !== false) {
        $header = substr($header, $position + 7);
        $token = strpos($header, ',') !== false ? strstr($header, ',', true) : $header;
    }
}
```

#### 安全特性
- Token存储在Redis中，格式：`user:token:{user_id}`
- Token加密存储，使用ApiTokenHelper::encryptToken()
- Token有效期：30-35天随机TTL
- 支持Token失效检查和用户信息验证

## 🚨 关键架构原则

### 1. 资源下载架构铁律
**核心原则**：
1. **资源下载铁律**: 所有基于用户产生的资源文件（视频、风格、角色、音乐、音效等）都必须由"Py视频创作工具"直接从AI平台下载到本地
2. **服务器职责边界**: API服务器只负责管理资源的URL、状态、元数据等附件信息，绝不进行资源文件的中转下载
3. **架构禁止事项**: 严禁在API服务器上进行任何形式的资源文件生成、处理、存储、中转下载

**开发约束规则**：
1. **控制器设计约束**: 资源相关控制器只能提供URL和状态管理，禁止文件操作
2. **服务层设计约束**: 资源相关服务只能进行元数据管理，禁止文件生成和处理逻辑
3. **存储架构约束**: 服务器存储只保存资源元数据，禁止保存实际资源文件
4. **下载流程约束**: Py视频创作工具 → API获取URL → 直接从AI平台下载，禁止服务器中转

### 2. WebSocket使用边界
- ✅ **仅Py视频创作工具使用**：AI生成进度推送、任务状态通知
- ❌ **WEB工具禁用**：避免不必要的连接和资源消耗
- 🔒 **安全传输**：密钥加密传输，不持久化存储

### 3. 避免循环依赖
- WebSocket服务只负责推送，不参与业务逻辑
- 积分变动通知改为异步事件驱动
- 使用事件总线模式解耦组件间依赖

### 4. 性能优化策略
- **并发支持**：设计支持1000用户同时使用
- **缓存策略**：MySQL主存储 + Redis缓存层
- **超时管理**：图像5分钟、视频30分钟、文本1分钟、语音2分钟

## 🎨 作品发布完整规则

### 可发布作品类型
1. **风格作品**: 用户创建的剧情风格可发布到风格广场
2. **角色作品**: 用户创建的角色可发布到角色广场
3. **视频作品**: 用户创作完成的视频可发布到作品广场
4. **发布时机**: 任何时间都可以提交发布申请

### 作品发布流程
1. **资源上传要求**: 发布任何作品都必须上传相关的资源文件
2. **资源重命名机制**: 上传的资源名称会被系统自动重命名
3. **资源地址保护**: 重命名后的资源地址不返回给用户，仅供系统内部使用
4. **审核机制**: 提交后进入审核流程，审核是否通过由系统决定

### 发布安全规则
1. **资源隔离**: 发布资源与用户创作资源完全隔离
2. **地址保护**: 发布资源地址不暴露给用户
3. **权限控制**: 仅审核通过的作品可在广场展示
4. **版权保护**: 发布资源受系统版权保护机制管理

## 📊 API接口业务状态码定义规范

1. 业务状态码和HTTP状态码相同的会映射到HTTP状态码，业务状态码和HTTP状态码不同的HTTP状态码将被设置为200。
2. 所有的业务状态码和状态码说明必须在 `php/api/app/Enums/ApiCodeEnum.php` 中设置。

## 📋 开发文档应用规则

### 控制器层 ↔ 服务层架构规范

**工具API接口服务采用分层架构模式，明确控制器层与服务层的职责分离：**

#### 目录结构与职责分工
```
php/api/
├── app/Http/Controllers/
│   ├── PyApi/              # Py视频创作工具API控制器
│   ├── WebApi/             # WEB工具API控制器
│   └── AdminApi/           # 管理后台API控制器
├── app/Services/
│   ├── PyApi/              # Py视频创作工具业务服务
│   ├── WebApi/             # WEB工具业务服务
│   └── AdminApi/           # 管理后台业务服务
└── app/WebSocket/          # WebSocket服务层
    ├── WebSocketService.php      # WebSocket业务逻辑处理
    ├── MessageHandler.php        # 消息处理与分发
    └── ConnectionManager.php     # 连接管理与状态维护
```

#### 职责分离原则

**控制器层职责**：
- HTTP请求接收与路由处理
- 请求参数验证与格式化
- 响应数据格式化与返回
- 异常处理与错误响应
- WebSocket连接建立与消息路由

**服务层职责**：
- 具体业务逻辑实现
- 数据库操作与事务管理
- 外部API调用与集成
- 复杂算法与数据处理

**WebSocket服务层职责**：
- WebSocket消息处理与分发
- 实时通信业务逻辑
- 连接状态管理与维护
- AI生成进度推送服务

#### 调用流程

**HTTP API调用流程**：
```
HTTP请求 → 控制器层 → 服务层 → 数据库/外部服务 → 服务层 → 控制器层 → HTTP响应
```

**WebSocket通信流程**：
```
WebSocket连接 → WebSocketController → WebSocketService → 业务逻辑处理 → 消息推送 → 客户端
```

**混合调用流程** (AI生成场景)：
```
HTTP请求 → 控制器层 → 服务层 → AI服务调用 → WebSocket推送进度 → HTTP响应结果
```

## 🔄 核心业务流程

### 主要业务流程
**核心创作流程**：选风格+写剧情 → 绑角色 → 生成图像 → 视频编辑 → 本地导出
**可选扩展流程**：本地导出 → [用户选择] → 作品发布到广场

**职责分工**：
- **服务端负责**：风格管理、剧情AI生成、角色管理、图像AI生成、素材存储管理
- **客户端负责**：视频时间轴编辑、本地素材合成、UI交互、作品导出

### 详细业务流程参考
完整的系统架构图和业务流程图已迁移到专门的图表文档，请参考：

📊 **[dev-chart-guidelines.mdc](.cursor/rules/dev-chart-guidelines.mdc)** - AI视频创作工具系统图表集合

#### 🔄 主要业务流程图包含：

**1. Py视频创作工具业务流程图**
- **用户管理流程** (A-1到A-5)：注册、登录、Token验证、密码管理、忘记密码重置
- **业务功能流程** (B-1到B-5)：充值积分、积分管理、代理推广、代理结算、数据处理
- **项目管理流程** (C-0)：视频创作项目创建流程（纯文本数据处理版）
- **AI核心流程** (C-1到C-9)：AI任务调度、AI生成成功、积分不足、处理失败、超时中断、资源管理、资源下载、作品发布、环境切换机制

**2. WEB网页工具业务流程图**
- WEB网页工具1：用户注册登录流程
- WEB网页工具2：作品广场浏览流程
- WEB网页工具3：用户中心管理流程

**3. 管理后台业务流程图**
- 管理后台业务流程1：系统配置管理流程
- 管理后台业务流程2：用户管理流程
- 管理后台业务流程3：内容审核管理流程
- 管理后台业务流程4：数据统计分析流程

**4. 系统架构图表**
- 完整系统架构图（环境切换优化版）
- AI服务集成模拟机制架构图
- AI服务调用流程对比图
- 项目依赖关系图
- **业务流程6**: 资源下载完成流程（核心流程）
- **业务流程7**: 可选作品发布流程（增值服务）
- **业务流程8**: 环境切换机制流程（核心机制）

该章节包含了完整的Mermaid序列图，详细描述了环境切换机制、积分安全、错误处理等所有关键业务场景。

## 📊 数据库设计概述

### 核心数据表结构

#### 基础业务表
- **p_users**: 用户表（用户信息、认证、偏好设置）
- **p_points_transactions**: 积分交易表（积分流水、冻结、返还）
- **p_points_freeze**: 积分冻结表（冻结机制、安全保障）

#### AI生成相关表
- **p_music_library**: 音乐库表（AI生成音乐存储、MiniMax平台）
- **p_sound_library**: 音效库表（AI生成音效存储、火山引擎豆包平台）
- **p_timbre_library**: 音色库表（AI生成音色存储、双平台支持）
- **p_style_library**: 风格库表（剧情风格管理、AI生成配置）
- **p_story_library**: 故事库表（AI生成故事内容、项目表 p_projects 关联）
- **p_character_library**: 角色库表（AI生成角色信息、特征描述）

#### 核心资源管理表
- **p_resources**: AI生成资源表（资源管理、模块关联、状态跟踪）
- **p_resource_versions**: 资源版本表（版本控制、提示词管理、本地导出）

#### 任务管理表
- **p_ai_generation_tasks**: AI生成任务表（任务状态、进度、结果）
- **p_websocket_sessions**: WebSocket会话表（连接管理、状态同步）

#### 用户作品管理表
- **p_user_works**: 用户作品表（用户创作的作品管理）
  - `user_id` - 用户ID，关联p_users表
  - `work_title` - 作品标题
  - `work_type` - 作品类型（video/image/music/story/character）
  - `content` - 作品内容或描述
  - `project_id` - 关联的项目ID（可选）
  - `status` - 作品状态（draft/completed/published/archived）
  - `visibility` - 可见性（private/public/friends_only）
  - `metadata` - 作品元数据（JSON格式，包含文件路径、尺寸、时长等）
  - `tags` - 作品标签（JSON数组）
  - `view_count` - 浏览次数
  - `like_count` - 点赞数量
  - `comment_count` - 评论数量
  - `share_count` - 分享次数
  - `featured_at` - 精选时间（用于推荐算法）
  - `published_at` - 发布时间
  - `created_at` - 创建时间
  - `updated_at` - 更新时间

#### 作品发布和社交表
- **p_work_plaza**: 作品广场表（公开作品展示平台）
  - `work_id` - 关联p_user_works表的作品ID
  - `user_id` - 作品创作者ID，关联p_users表
  - `title` - 作品展示标题
  - `description` - 作品描述
  - `category` - 作品分类（video/image/music/story/character/mixed）
  - `tags` - 作品标签（JSON数组，用于搜索和推荐）
  - `thumbnail` - 作品缩略图URL
  - `preview_url` - 预览文件URL（如视频预览、音频试听等）
  - `view_count` - 浏览次数
  - `like_count` - 点赞数量
  - `comment_count` - 评论数量
  - `share_count` - 分享次数
  - `download_count` - 下载次数
  - `rating` - 作品评分（1-5星）
  - `rating_count` - 评分人数
  - `review_status` - 审核状态（pending/approved/rejected/auto_approved）
  - `review_reason` - 审核说明
  - `featured` - 是否精选推荐
  - `featured_at` - 精选时间
  - `published_at` - 发布到广场的时间
  - `created_at` - 创建时间
  - `updated_at` - 更新时间

- **p_work_shares**: 作品分享表（分享链接、权限控制、访问统计）

#### 社交功能表
- **p_follows**: 关注关系表（用户关注系统）
  - `follower_id` - 关注者用户ID，关联p_users表
  - `following_id` - 被关注者用户ID，关联p_users表
  - `status` - 关注状态（active/blocked/pending）
  - `notification_enabled` - 是否启用通知
  - `created_at` - 关注时间
  - `updated_at` - 更新时间

- **p_likes**: 点赞表（通用点赞系统）
  - `user_id` - 点赞用户ID，关联p_users表
  - `target_type` - 目标类型（work/comment/user/project）
  - `target_id` - 目标ID（根据target_type关联不同表）
  - `created_at` - 点赞时间

- **p_comments**: 评论表（通用评论系统）
  - `user_id` - 评论用户ID，关联p_users表
  - `target_type` - 评论目标类型（work/project/user）
  - `target_id` - 评论目标ID（根据target_type关联不同表）
  - `content` - 评论内容
  - `parent_id` - 父评论ID（用于回复功能，关联本表）
  - `status` - 评论状态（active/hidden/deleted/pending_review）
  - `like_count` - 评论点赞数
  - `reply_count` - 回复数量
  - `is_pinned` - 是否置顶
  - `created_at` - 评论时间
  - `updated_at` - 更新时间

- **p_work_interactions**: 作品互动表（点赞、评论、分享记录的汇总统计）

## ⚡ 性能期望与技术要求

### **系统性能指标**
- **响应延迟**：≤30000ms（30秒）
- **并发支持**：1000用户同时使用
- **系统可用性**：99.9%
- **API响应时间**：平均200ms
- **AI生成时间**：文本15-30秒，图像30-60秒
- **WebSocket连接**：支持长连接，自动重连
- **数据一致性**：MySQL+Redis双重保障
- **安全性**：密钥加密传输，权限二次验证

### **AI生成超时配置**
- 🕐 **图像生成**: 5分钟超时（可配置）
- 🕐 **视频生成**: 30分钟超时（可配置）
- 🕐 **文本生成**: 1分钟超时（可配置）
- 🕐 **语音合成**: 2分钟超时（可配置）

### **环境切换性能对比**
| 环境模式 | 响应时间 | 费用产生 | 数据真实性 | 适用场景 |
|---------|---------|---------|-----------|---------|
| **mock模式** | 100-500ms | 无费用 | 模拟数据 | 开发测试 |
| **real模式** | 1-30秒 | 真实费用 | 真实数据 | 生产环境 |

## 🌐 AI平台API接口指导文档

- **DeepSeek API指导文档**: `ai-api-deepseek.com-guidelines.mdc`
- **KlingAI API指导文档**: `ai-api-klingai.com-guidelines.mdc`
- **LiblibAI API指导文档**: `ai-api-liblibai.art-guidelines.mdc`
- **MiniMax API指导文档**: `ai-api-minimaxi.com-guidelines.mdc`
- **火山引擎豆包API指导文档**: `ai-api-volcengine.com-guidelines.mdc`


## 📚 开发文档使用指南

### 🎯 **分类文档应用场景明确定义**

**针对 @php/api/ 目录的"工具API接口服务"开发，必须严格按照以下规则使用分类文档：**

#### **📚 六大核心文档体系**

1. **🆕 新功能开发文档**：`dev-api-guidelines-add.mdc`
2. **🔧 问题修复文档**：`dev-api-guidelines-edit.mdc`
3. **🤖 【工具API接口服务】环境切换AI服务文档**：`dev-aiapi-guidelines.mdc`
4. **🤖 【Py视频创作工具】对接【工具API接口服务】文档**：`dev-api-guidelines-pyapi.mdc`
5. **🤖 【WEB网页工具】对接【工具API接口服务】文档**：`dev-api-guidelines-webapi.mdc`
6. **🤖 【管理后台】对接【工具API接口服务】文档**：`dev-api-guidelines-adminapi.mdc`

#### **🆕 新功能开发场景**
**主要文档**：`dev-api-guidelines-add.mdc`
**辅助文档**：`dev-aiapi-guidelines.mdc`（当涉及AI功能时）

**适用情况**：
- ✅ **新增API接口开发**：创建全新的控制器、服务层、中间件
- ✅ **新增数据库表设计**：设计新的数据表结构和迁移程序
- ✅ **新增业务模块开发**：实现全新的业务功能模块
- ✅ **新增WebSocket处理器**：开发Py视频创作工具专用的WebSocket服务
- ✅ **新增AI服务环境切换功能**：集成新的AI生成服务和功能（必须配合`dev-aiapi-guidelines.mdc`）
- ✅ **扩展性架构设计**：设计可扩展的系统架构
- ✅ **高级功能扩展**：用户成长路径、个性化推荐、AI模型管理等

#### **🔧 问题修复场景**
**主要文档**：`dev-api-guidelines-edit.mdc`
**辅助文档**：`dev-aiapi-guidelines.mdc`（当修复AI相关问题时）

**适用情况**：
- ✅ **架构违规修复**：修复WebSocket边界违规、职责边界不清等问题
- ✅ **性能指标修正**：统一并发用户数为1000、响应时间优化等
- ✅ **安全机制强化**：积分系统安全加固、认证机制完善
- ✅ **接口规范修正**：移除违规接口、修正API设计
- ✅ **数据库结构修复**：修正表字段、索引优化、关系调整
- ✅ **代码质量提升**：重构现有代码、优化业务逻辑
- ✅ **AI服务调用问题修复**：修复AI API调用失败、超时、格式错误等问题
- ✅ **环境切换机制修复**：修复 mock/real 模式切换问题

#### **🤖 【工具API接口服务】环境切换AI服务场景**
**主要文档**：`dev-aiapi-guidelines.mdc`
**配合文档**：根据具体需求选择 `dev-api-guidelines-add.mdc` 或 `dev-api-guidelines-edit.mdc` 文档

**适用情况**：
- ✅ **AI API接口调用**：通过 AiServiceClient::call() 调用DeepSeek、LiblibAI、KlingAI、MiniMax、火山引擎豆包等AI服务
- ✅ **AI服务环境切换开发**：实现 mock/real 模式的无缝切换，确保开发和生产环境的一致性
- ✅ **AI服务错误处理**：处理AI API调用失败、超时、格式错误等问题
- ✅ **AI服务性能优化**：优化AI API调用性能、实现负载均衡、服务降级
- ✅ **AI服务监控**：监控AI API调用状态、成功率、响应时间等指标
- ✅ **环境切换配置**：配置 AI_SERVICE_MODE 环境变量和相关参数

**包含内容**：
- 🤖 **5个AI平台完整接口规范**：详见 **"🤖 AI模型配置信息"** 章节
- 🤖 **87个AI API接口**：文本生成、图像生成、语音合成、视频生成、音效处理、音频混合等
- 🤖 **环境切换机制**：AiServiceClient 和 ThirdPartyServiceClient 的使用规范
- 🤖 **配置管理**：php/api/config/ai.php 的配置说明和最佳实践
- 🤖 **分平台功能支持详情**：详见 **"🤖 AI模型配置信息"** 章节的业务模型配置矩阵

#### **🎯 第三方服务环境切换场景**
**主要文档**：`dev-thirdapi-guidelines.mdc`
**配合文档**：根据具体需求选择 `dev-api-guidelines-add.mdc` 或 `dev-api-guidelines-edit.mdc` 文档

**适用情况**：
- ✅ **第三方API接口调用**：通过 ThirdPartyServiceClient::call() 调用微信、支付宝、短信等第三方服务
- ✅ **第三方服务环境切换**：实现 mock/real 模式的无缝切换
- ✅ **第三方服务错误处理**：处理第三方API调用失败、超时等问题
- ✅ **第三方服务监控**：监控第三方API调用状态和性能指标

#### **📊 文档使用优先级规则**

1. **🔍 问题诊断优先级**：
   - **AI服务环境切换相关问题** → 优先使用 `dev-aiapi-guidelines.mdc` + 对应的add/edit文档
   - **第三方服务环境切换相关问题** → 优先使用 `dev-thirdapi-guidelines.mdc` + 对应的add/edit文档
   - **现有功能问题修复** → 使用 `dev-api-guidelines-edit.mdc`
   - **新功能需求开发** → 使用 `dev-api-guidelines-add.mdc`

2. **🎯 开发适配规则**：
   - **基础设施开发**：主要使用 `dev-api-guidelines-add.mdc`
   - **核心AI功能开发**：`dev-api-guidelines-add.mdc` + `dev-aiapi-guidelines.mdc`
   - **第三方服务集成**：`dev-api-guidelines-add.mdc` + `dev-thirdapi-guidelines.mdc`
   - **高级功能开发**：三个文档并用，根据具体需求选择
   - **维护阶段**：主要使用 `dev-api-guidelines-edit.mdc` + 对应的专项文档

3. **🚨 紧急修复优先级**：
   - **AI服务故障** → 必须使用 `dev-aiapi-guidelines.mdc` + `dev-api-guidelines-edit.mdc`
   - **第三方服务故障** → 必须使用 `dev-thirdapi-guidelines.mdc` + `dev-api-guidelines-edit.mdc`
   - **环境切换问题** → 优先使用对应的专项文档 + `dev-api-guidelines-edit.mdc`
   - **生产环境问题** → 优先使用 `dev-api-guidelines-edit.mdc`
   - **架构合规性问题** → 必须使用 `dev-api-guidelines-edit.mdc`

4. **🤖 AI功能开发专用规则**：
   - **所有AI相关开发** → 必须使用 `dev-aiapi-guidelines.mdc` 作为主要参考
   - **新增AI功能** → `dev-aiapi-guidelines.mdc` + `dev-api-guidelines-add.mdc`
   - **修复AI功能** → `dev-aiapi-guidelines.mdc` + `dev-api-guidelines-edit.mdc`
   - **AI服务环境切换** → 仅使用 `dev-aiapi-guidelines.mdc` + 本文档环境切换规范

#### **🔄 多文档协作机制**

1. **📋 文档协作优先级**：
   ```
   AI功能开发：`dev-aiapi-guidelines.mdc` (主) + `dev-api-guidelines-add.mdc` (辅)
   AI问题修复：`dev-aiapi-guidelines.mdc` (主) + `dev-api-guidelines-edit.mdc` (辅)
   第三方服务开发：`dev-thirdapi-guidelines.mdc` (主) + `dev-api-guidelines-add.mdc` (辅)
   第三方服务修复：`dev-thirdapi-guidelines.mdc` (主) + `dev-api-guidelines-edit.mdc` (辅)
   非AI新功能：`dev-api-guidelines-add.mdc` (主) + 其他文档 (可选)
   非AI问题修复：`dev-api-guidelines-edit.mdc` (主)
   ```

2. **🎯 具体使用场景判断**：
   - **包含"AI"、"生成"、"智能"关键词** → 必须使用 `dev-aiapi-guidelines.mdc`
   - **涉及DeepSeek、LiblibAI、KlingAI、MiniMax、火山引擎豆包** → 必须使用 `dev-aiapi-guidelines.mdc`
   - **涉及微信、支付宝、短信等第三方服务** → 必须使用 `dev-thirdapi-guidelines.mdc`
   - **文本生成、图像生成、语音合成、视频生成** → 必须使用 `dev-aiapi-guidelines.mdc`
   - **环境切换、AiServiceClient、ThirdPartyServiceClient** → 使用对应的专项文档

#### **⚠️ 重要注意事项**

1. **🛡️ 架构合规性检查**：
   - 所有开发必须遵循index-new.mdc中定义的职责边界
   - WebSocket仅限Py视频创作工具使用
   - 并发用户数统一为1000
   - AI服务调用必须使用 AiServiceClient::call()
   - 第三方服务调用必须使用 ThirdPartyServiceClient::call()
   - 环境切换必须通过配置文件实现，不得硬编码

2. **📋 文档完整性保证**：
   - **环境切换机制**：完整实现 mock/real 模式自动切换
   - **`dev-api-guidelines-add.mdc`**：包含119个API接口、15个开发阶段、完整AI服务环境切换架构
   - **`dev-api-guidelines-edit.mdc`**：包含8个控制器、完整中间件、监控系统
   - **`dev-api-guidelines-pyapi.mdc`**：包含41个控制器、252个API接口、AI平台选择功能优化 🚨 升级
   - **`dev-aiapi-guidelines.mdc`**：包含5个AI平台、87个AI接口、环境切换规范
   - **`dev-thirdapi-guidelines.mdc`**：包含第三方服务环境切换规范
   - **总接口统计**：339个总接口（252个PyAPI接口 + 87个AI接口）🚨 升级优化

3. **🤖 AI服务环境切换规范**：
   - 所有AI功能开发必须使用`dev-aiapi-guidelines.mdc`作为权威依据
   - AI API调用必须使用 AiServiceClient::call() 方法
   - 支持5个AI平台：DeepSeek、LiblibAI、KlingAI、MiniMax、火山引擎豆包
   - 环境切换通过 AI_SERVICE_MODE 环境变量控制 (mock/real)
   - 模拟服务超时30秒，真实服务超时60秒
   - 必须实现AI服务降级和错误处理机制

4. **🔗 第三方服务环境切换规范**：
   - 所有第三方服务调用必须使用 ThirdPartyServiceClient::call() 方法
   - 环境切换通过 THIRD_PARTY_MODE 环境变量控制 (mock/real)
   - 支持微信、支付宝、短信等主要第三方服务
   - 必须实现第三方服务降级和错误处理机制

5. **🔄 持续更新机制**：
   - **新增功能** → 更新 `dev-api-guidelines-add.mdc`
   - **修复问题** → 更新 `dev-api-guidelines-edit.mdc`
   - **AI服务变更** → 更新 `dev-aiapi-guidelines.mdc`
   - **第三方服务变更** → 更新 `dev-thirdapi-guidelines.mdc`
   - **环境切换机制变更** → 更新 `index-new.mdc` 和对应专项文档
   - 定期同步所有文档，确保与`index-new.mdc`规范一致
   - AI服务接口变更需要同步更新相关控制器和服务层

6. **📊 文档使用统计和监控**：
   - 优先使用覆盖率最高的文档（`dev-api-guidelines-add.mdc`）
   - AI相关开发必须使用`dev-aiapi-guidelines.mdc`（100%AI接口覆盖）
   - 第三方服务开发必须使用`dev-thirdapi-guidelines.mdc`
   - 问题修复优先使用`dev-api-guidelines-edit.mdc`（包含完整修复方案）
   - 定期评估文档使用效果，持续优化文档结构

#### **🎯 文档选择决策树**

```
开发任务分析
├─ 是否涉及环境切换？
│  ├─ AI服务环境切换 → `dev-aiapi-guidelines.mdc` (主) + `index-new.mdc` (架构参考)
│  ├─ 第三方服务环境切换 → `dev-thirdapi-guidelines.mdc` (主) + `index-new.mdc` (架构参考)
│  └─ 否 → 继续判断
├─ 是否涉及客户端对接？
│  ├─ Py视频创作工具对接 → `dev-api-guidelines-pyapi.mdc` (最高权重) + `index-new.mdc` (架构边界)
│  ├─ WEB工具对接 → `dev-api-guidelines-webapi.mdc` (最高权重) + `index-new.mdc` (架构边界)
│  ├─ 管理后台对接 → `dev-api-guidelines-adminapi.mdc` (最高权重) + `index-new.mdc` (架构边界)
│  └─ 否 → 继续判断
├─ 是否涉及AI功能？
│  ├─ 是 → `dev-aiapi-guidelines.mdc` (必须主文档)
│  │  ├─ 新增AI功能 → + `dev-api-guidelines-add.mdc` (新增规范)
│  │  ├─ 修复AI功能 → + `dev-api-guidelines-edit.mdc` (修复规范)
│  │  └─ 纯AI接口调用 → 仅使用 `dev-aiapi-guidelines.mdc`
│  └─ 否 → 继续判断
├─ 是否涉及第三方服务？
│  ├─ 是 → `dev-thirdapi-guidelines.mdc` (必须主文档)
│  │  ├─ 新增第三方功能 → + `dev-api-guidelines-add.mdc` (新增规范)
│  │  ├─ 修复第三方功能 → + `dev-api-guidelines-edit.mdc` (修复规范)
│  │  └─ 纯第三方接口调用 → 仅使用 `dev-thirdapi-guidelines.mdc`
│  └─ 否 → 继续判断
├─ 是新功能开发？
│  ├─ 是 → `dev-api-guidelines-add.mdc` (主文档)
│  │  ├─ 涉及AI功能 → + `dev-aiapi-guidelines.mdc` (AI专项规范)
│  │  ├─ 涉及第三方服务 → + `dev-thirdapi-guidelines.mdc` (第三方专项规范)
│  │  └─ 涉及客户端对接 → + 对应的 `dev-api-guidelines-pyapi/webapi/adminapi.mdc`
│  └─ 否 → 继续判断
├─ 是问题修复？
│  ├─ 是 → `dev-api-guidelines-edit.mdc` (主文档)
│  │  ├─ AI相关问题 → + `dev-aiapi-guidelines.mdc` (AI专项规范)
│  │  ├─ 第三方服务问题 → + `dev-thirdapi-guidelines.mdc` (第三方专项规范)
│  │  └─ 客户端对接问题 → + 对应的 `dev-api-guidelines-pyapi/webapi/adminapi.mdc`
│  └─ 否 → 继续判断
└─ 复杂场景 → 多文档组合使用
   ├─ 架构重构 → `dev-api-guidelines-edit.mdc` + `dev-api-guidelines-add.mdc` + 相关专项文档
   ├─ 性能优化 → `dev-api-guidelines-edit.mdc` + 相关专项文档 (AI/第三方/客户端)
   ├─ 安全加固 → `dev-api-guidelines-edit.mdc` + `dev-api-guidelines-add.mdc` (如需新增安全功能)
   └─ 全栈开发 → 根据涉及的技术栈选择对应的多个 `` 文档组合
```


## 📝 技术栈总结

### 后端技术栈
- **管理后台**: Laravel 10
- **工具API接口服务**: Lumen 10
- **数据库**: MySQL 8.0.12
- **缓存**: Redis 7.4.2
- **Web服务器**: Nginx 1.26.2
- **PHP版本**: 8.1.29

### 前端技术栈
- **WEB网页工具**: 响应式UI布局（支持PC和移动），全静态前端调用工具API接口服务
- **Py视频创作工具**: Python + PySide6 + PyInstaller + WebSocket客户端

### 开发工具
- **操作系统**: Windows 11（开发环境）
- **生产环境**: CentOS 8 Stream
- **版本控制**: Git
- **API文档**: 基于OpenAPI规范

---

## 📝 文档维护说明

本文档是AI视频创作工具系统的核心架构规范，所有开发工作都应严格遵循本文档的规定。如需修改架构设计，必须先更新本文档并经过团队评审。

**最后更新**: 2025-08-03
**文档版本**: v4.0 - 开发文档使用指南完整版
**维护人员**: 开发团队

### **v4.0 更新内容**
- ✅ **开发文档使用指南**: 完整的分类文档应用场景明确定义
- ✅ **六大核心文档体系**: 明确的文档分类和职责边界
- ✅ **文档选择决策流程**: 快速决策树和使用优先级规则
- ✅ **环境切换机制规范**: AI服务和第三方服务的环境切换指导
- ✅ **多文档协作机制**: 文档组合使用的具体规则和最佳实践
- ✅ **架构合规性检查**: 完整的开发约束和注意事项
- ✅ **持续更新机制**: 文档维护和同步的规范流程

### **v3.0 更新内容**
- ✅ **完整项目架构图**: 包含环境切换服务客户端层的完整架构
- ✅ **业务流程图迁移**: 所有系统架构图和业务流程图已迁移到 **[dev-chart-guidelines.mdc](.cursor/rules/dev-chart-guidelines.mdc)**
- ✅ **环境切换机制**: AiServiceClient 和 ThirdPartyServiceClient 实现
- ✅ **性能指标**: 完整的系统性能要求和AI生成超时配置
- ✅ **AI模型配置**: 5个AI平台的业务模型配置矩阵
- ✅ **作品发布规则**: 完整的作品发布流程和安全规则
- ✅ **架构边界规范**: 资源下载铁律和WebSocket使用边界
