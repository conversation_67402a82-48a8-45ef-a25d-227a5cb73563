---
description: AI视频创作工具系统架构规范
globs: 
alwaysApply: true
---

# AI视频创作工具系统架构规范

## 📋 项目概述

### 🎯 项目定位
本项目是一个完整的AI视频创作工具生态系统，包含Py视频创作工具、WEB网页工具、管理后台、工具API接口服务和AI服务集成模拟返回数据服务五大核心组件。

**📁 文档路径说明**: 本文档中提到的所有开发规范文档都位于 `@.cursor/rules/` 目录下。

### 🏗️ 系统架构设计原则
- **职责边界清晰**：每个组件职责明确，避免功能重叠
- **服务解耦**：组件间通过标准API接口通信，降低耦合度
- **资源本地化**：用户创作资源由Py视频创作工具直接从AI平台下载到本地
- **可选发布机制**：作品发布为增值服务，用户可选择是否发布到广场

## 🔧 开发环境配置

### 当前开发环境
- **操作系统**: Windows 11
- **Python**: 3.12
- **Web服务器**: Nginx 1.26.2
- **PHP**: 8.1.29
- **数据库**: MySQL 8.0.12
- **缓存**: Redis 7.4.2

### 生产环境规划
- **管理后台**: CentOS 8 Stream + Nginx + PHP + MySQL + Redis
- **工具API接口服务**: CentOS 8 Stream + Nginx + PHP + MySQL + Redis
- **WEB网页工具**: CentOS 8 Stream + Nginx（静态部署）
- **Py视频创作工具**: Windows 和 Mac（客户端应用）
- **AI服务集成模拟返回数据服务**: 本地开发专用，生产环境直连真实AI平台

## 📁 项目目录结构

```
项目根目录/
├── php/
│   ├── backend/          # 管理后台
│   ├── api/              # 工具API接口服务
│   ├── web/              # WEB网页工具
│   ├── aiapi/            # AI服务集成模拟返回数据服务
│   └── thirdapi/         # 第三方服务集成模拟返回数据服务
├── python/               # Py视频创作工具
└── .cursor/
    └── rules/            # 开发规范文档
        ├── index-new.mdc                    # 本文档
        ├── dev-aiapi-guidelines.mdc         # AI服务集成开发规范
        ├── dev-thirdapi-guidelines.mdc      # 第三方服务集成开发规范
        ├── dev-api-guidelines-database.mdc  # 数据库设计规范文档
        ├── dev-api-guidelines-pyapi.mdc     # Py视频创作工具API接口规范
        ├── dev-api-guidelines-webapi.mdc    # WEB工具API接口规范
        └── dev-api-guidelines-adminapi.mdc  # 管理后台API接口规范
```

## 🎯 核心组件职责定义

### 1. AI服务集成模拟返回数据服务 (@php/aiapi/)

**核心职责**：
- 在保持不同AI平台API接口特性前提下集成在一起
- 根据第三方AI的API接口文档接收和模拟返回数据
- 支持"工具API接口服务"的本地开发

**项目功能**：
- 根据"工具API接口服务"请求不同的AI平台API接口
- 验证接收的数据和模拟各种状态返回处理结果
- 支持5个AI平台：DeepSeek、LiblibAI、KlingAI、MiniMax、火山引擎豆包

**作用期限**：
- 作用于支持"Py视频创作工具"本地开发阶段完成
- "Py视频创作工具"上线后通过配置修改AI平台配置直接绕过本服务
- 请求真实线上AI平台的API接口

**开发规范文档**: `dev-aiapi-guidelines.mdc`

### 2. 第三方服务集成模拟返回数据服务 (@php/thirdapi/)

**核心职责**：
- 在保持不同第三方服务API接口特性前提下集成在一起
- 根据第三方服务的API接口文档接收和模拟返回数据
- 支持"工具API接口服务"的本地开发

**项目功能**：
- 根据"工具API接口服务"请求不同的第三方服务API接口
- 验证接收的数据和模拟各种状态返回处理结果
- 支持微信登录/支付、支付宝支付、短信服务、邮件服务

**作用期限**：
- 作用于支持"Py视频创作工具"本地开发阶段完成
- "Py视频创作工具"上线后通过配置修改第三方服务配置直接绕过本服务
- 请求真实线上第三方服务的API接口

**支持的第三方服务**：
- **微信服务**: OAuth登录、微信支付（统一下单、查询、退款等）
- **支付宝支付**: 统一收单、交易查询、退款处理
- **短信服务**: 阿里云短信、腾讯云短信、验证码验证
- **邮件服务**: SMTP发送、SendCloud、模板邮件

**服务地址**: `https://thirdapi.tiptop.cn/`

**开发规范文档**: `dev-thirdapi-guidelines.mdc`

## 📊 **项目架构图**

**📊 完整系统架构图**: 详见 **[dev-chart-guidelines.mdc - 完整系统架构图（环境切换优化版）](.cursor/rules/dev-chart-guidelines.mdc#完整系统架构图环境切换优化版)**

该架构图包含完整的系统组件和连接关系：

### **🏗️ 架构层次结构**
- **用户层**: Py视频创作工具、WEB网页工具、管理后台
- **业务服务层**: 工具API接口服务、WebSocket服务、AI资源管理服务
- **环境切换层**: AiServiceClient、ThirdPartyServiceClient
- **开发支持层**: AI服务模拟、第三方服务模拟
- **数据存储层**: MySQL数据库、Redis缓存
- **真实服务层**: 5个AI平台、3个第三方服务

### **🔗 连接类型说明**
- **🔵 蓝色虚线**: HTTP API调用 (REST接口，三个工具都使用，职责明确)
- **🟢 绿色粗线**: WebSocket实时通信 (仅Py视频创作工具使用，边界清晰)
- **🔴 红色线**: 服务间调用 (异步事件驱动，避免循环依赖)
- **🟠 橙色线**: 数据库和缓存操作 (双重保障，性能优化)
- **🟣 紫色线**: 环境切换调用 (核心机制，自动路由)

### **🎯 关键优化特性**
1. **职责边界清晰**: 每个组件的职责明确定义，避免功能重叠
2. **环境切换机制**: 通过服务客户端实现开发/生产环境无缝切换
3. **避免循环依赖**: 使用异步事件驱动架构，解耦组件间依赖
4. **WebSocket边界明确**: 仅为Py视频创作工具提供实时通信，WEB工具不使用
5. **性能优化设计**: 支持1000并发用户，MySQL+Redis双重保障
6. **安全架构升级**: 密钥安全传输，不持久化存储，权限二次验证
7. **功能模块整合**: 相关功能统一管理，避免分散和重复
8. **🎯 AI资源管理**: 完整的资源生成、版本控制、审核发布体系
9. **🎯 差异化存储**: 图像/音频下载处理，视频仅元数据管理

## 🔄 **业务流程图参考**

所有详细的业务流程图已迁移到专门的图表文档：**[dev-chart-guidelines.mdc](.cursor/rules/dev-chart-guidelines.mdc)**

该文档包含完整的系统架构图和业务流程图，包括：
- Py视频创作工具业务流程图（用户管理、业务功能、项目管理、AI核心流程）
- WEB网页工具业务流程图
- 管理后台业务流程图
- 系统架构图表集合

## 🚨 **AI服务集成模拟机制架构图**

**📊 AI服务集成模拟机制架构图**: 详见 **[dev-chart-guidelines.mdc - AI服务集成模拟机制架构图](.cursor/rules/dev-chart-guidelines.mdc#AI服务集成模拟机制架构图)**

该架构图展示了开发环境与生产环境的完整切换机制，包括：
- **本地开发环境**: 工具API接口服务 → AI服务模拟 → 模拟数据返回
- **生产环境**: 工具API接口服务 → 真实AI平台 → 真实数据返回
- **环境切换机制**: 通过配置自动路由到不同的服务端点

### **🎯 环境切换机制核心价值**

**关键价值**：
- ✅ **开发效率**：本地开发无需依赖真实第三方平台
- ✅ **成本控制**：避免开发阶段产生任何真实费用
- ✅ **测试完整性**：可以模拟各种边界情况和异常状态
- ✅ **完全兼容**：确保与真实第三方平台API的100%兼容性
- ✅ **架构纯净**：工具API接口服务保持业务逻辑纯净，无模拟污染
- ✅ **安全隔离**：模拟环境与真实环境完全隔离，无数据泄露风险

### 3. 工具API接口服务 (@php/api/)

**核心职责**：
- 本地开发阶段依赖"AI服务集成模拟返回数据服务"和"第三方服务集成模拟返回数据服务"
- 集成多家AI平台的API接口提供对应AI平台的API接口服务
- 集成多种第三方服务的API接口（微信、支付宝、短信、邮件等）
- 开发支持"Py视频创作工具"的AI视频创作功能
- 支持"WEB网页工具"作品广场和"管理后台"功能实现

#### 3.1 Py视频创作工具的API接口

**控制器目录**: `@php/api/app/Http/Controllers/PyApi`
**业务层目录**: `@php/api/app/Services/PyApi`

**业务逻辑职责**：
- 创建AI创作视频任务
- AI任务调度（含文生文、图生图、图生视频、生成语音、生成音效、生成音乐等所有需要AI的功能）
- 数据处理
- 作品发布
- 用户注册登录
- 用户资料修改与密码找回
- 用户认证
- 充值积分
- 积分管理
- 积分明细
- 代理推广
- 代理结算

**WebSocket服务职责**：
- 仅为Py视频创作工具提供实时通信（AI生成进度推送）

**不包含职责**：
- 不储存且不中转用户创作过程中AI生成的资源
- 视频编辑处理
- 客户端UI逻辑
- 本地文件操作

#### 3.2 WEB网页工具的API接口

**控制器目录**: `@php/api/app/Http/Controllers/WebApi`
**业务层目录**: `@php/api/app/Services/WebApi`

**业务逻辑职责**：
- 功能介绍查询
- 价格方案查询
- 作品数据查询（支持分类筛选、搜索查看、作品详情展示）
- 用户注册登录
- 用户资料修改与密码找回
- 用户认证
- 充值积分
- 积分管理
- 积分明细
- 代理推广
- 代理结算

**响应式设计**：
- 支持PC端、移动端、Py视频创作工具嵌入（1200px/800px窗口）

**不包含职责**：
- 视频创作功能
- AI生成功能
- WebSocket实时通信
- 作品发布创建



#### **WEB网页工具职责边界规范**

**📊 现有业务流程图表**：
详细的WEB网页工具业务流程图请参考 **[dev-chart-guidelines.mdc](.cursor/rules/dev-chart-guidelines.mdc)** 文档中的"WEB网页工具业务流程图"章节：
- **WEB网页工具1**: 用户注册登录流程
- **WEB网页工具2**: 作品广场浏览流程
- **WEB网页工具3**: 用户中心管理流程

**🏗️ 架构说明**：WEB网页工具的完整架构已在 **[dev-chart-guidelines.mdc](.cursor/rules/dev-chart-guidelines.mdc)** 的"完整系统架构图（环境切换优化版）"中体现，包含前端展示层、WebApi控制器层、服务层和数据存储层的完整设计。

##### **✅ 允许的功能职责**
1. **展示职责**：
   - 首页工具展示和功能介绍
   - 价格方案展示和对比
   - 平台统计数据展示
   - 公告和帮助信息展示

2. **用户中心职责**：
   - 用户注册、登录、认证
   - 用户资料管理和修改
   - 密码找回和安全设置
   - 积分查询、充值和明细

3. **作品广场职责**：
   - 作品展示和浏览
   - 分类筛选和搜索
   - 作品详情查看
   - 作品互动（点赞、分享）

4. **代理推广职责**：
   - 代理申请和管理
   - 推广统计和数据
   - 佣金查询和结算
   - 推广链接生成

5. **响应式设计职责**：
   - PC端完整功能体验
   - 移动端优化适配
   - Py视频创作工具嵌入适配

##### **❌ 禁止的功能职责**
1. **创作功能禁止**：
   - 视频创作和编辑
   - AI内容生成
   - 素材处理和合成
   - 本地文件操作

2. **实时通信禁止**：
   - WebSocket连接
   - 实时进度推送
   - 即时消息通信
   - 长连接维护

3. **作品发布禁止**：
   - 作品创建和上传
   - 作品发布到广场
   - 作品审核管理
   - 作品版本控制

4. **高级管理禁止**：
   - 系统配置管理
   - 用户权限管理
   - 数据统计分析
   - 内容审核操作

##### **📋 WEB网页工具API接口列表**

###### **首页展示接口组**
```
GET  /api/web/home/<USER>
GET  /api/web/home/<USER>
GET  /api/web/home/<USER>
GET  /api/web/home/<USER>
```

###### **用户管理接口组**
```
POST /api/web/user/register          # 用户注册
POST /api/web/user/login             # 用户登录
POST /api/web/user/logout            # 用户登出
GET  /api/web/user/profile           # 获取用户资料
PUT  /api/web/user/profile           # 更新用户资料
POST /api/web/user/change-password   # 修改密码
POST /api/web/user/forgot-password   # 忘记密码
POST /api/web/user/verify-email      # 邮箱验证
POST /api/web/user/verify-phone      # 手机验证
GET  /api/web/user/points            # 获取积分信息
GET  /api/web/user/points/history    # 获取积分明细
```

###### **作品广场接口组**
```
GET  /api/web/works                  # 获取作品列表
GET  /api/web/works/{id}             # 获取作品详情
GET  /api/web/works/categories       # 获取作品分类
GET  /api/web/works/search           # 搜索作品
GET  /api/web/works/trending         # 获取热门作品
GET  /api/web/works/latest           # 获取最新作品
POST /api/web/works/{id}/view        # 记录作品浏览
POST /api/web/works/{id}/like        # 作品点赞
POST /api/web/works/{id}/share       # 作品分享
```

###### **代理推广接口组**
```
POST /api/web/agent/apply            # 申请成为代理
GET  /api/web/agent/info             # 获取代理信息
GET  /api/web/agent/statistics       # 获取推广统计
GET  /api/web/agent/commissions      # 获取佣金记录
POST /api/web/agent/withdraw         # 申请提现
GET  /api/web/agent/withdraw/history # 提现记录
GET  /api/web/agent/referrals        # 推荐用户列表
```

###### **支付充值接口组**
```
POST /api/web/payment/create         # 创建支付订单
GET  /api/web/payment/methods        # 获取支付方式
POST /api/web/payment/callback       # 支付回调处理
GET  /api/web/payment/orders         # 获取订单列表
GET  /api/web/payment/orders/{id}    # 获取订单详情
POST /api/web/payment/cancel         # 取消订单
```



#### 3.3 支持"管理后台"的API接口

**控制器目录**: `@php/api/app/Http/Controllers/AdminApi`
**业务层目录**: `@php/api/app/Services/AdminApi`

**业务逻辑职责**：
- 系统配置管理（AI平台配置、系统参数设置）
- 用户管理（用户信息、权限管理、账户状态）
- 内容管理（作品审核、内容监控、违规处理）
- 数据统计（用户统计、收入统计、使用情况分析）
- 积分系统管理（积分规则、充值记录、消费明细）
- 代理系统管理（代理审核、佣金结算、推广数据）
- 素材库管理（音色库、音效库、音乐库、风格库、角色库）
- 系统监控（性能监控、错误日志、API调用统计）
- 财务管理（收入报表、退款处理、财务对账）

### 4. Py视频创作工具 (@python/)

**技术栈**: Python + PySide6 + PyInstaller + WebSocket客户端

**核心创作职责**（调用"Py视频创作工具的API接口"支持）：
- 选风格+写剧情
- 绑角色
- 生成图像
- 视频编辑
- 本地导出

**可选发布职责**（调用"Py视频创作工具的API接口"支持）：
- 作品发布到广场（用户自主选择）

**客户端处理职责**（调用"Py视频创作工具的API接口"支持）：
- 资源本地化
- 视频时间轴编辑
- 本地素材合成
- UI交互逻辑
- 作品导出

**用户中心职责**（调用"用户API接口"支持）：
- 用户注册登录
- 用户资料修改与密码找回
- 用户认证
- 充值积分
- 积分管理
- 积分明细
- 代理推广
- 代理结算

**实时通信职责**（调用"Py视频创作工具的API接口"支持）：
- 通过WebSocket接收AI生成进度推送

### 5. WEB网页工具 (@php/web/)

**展示职责**（调用"WEB网页工具的API接口"支持）：
- 首页工具展示
- 功能介绍
- 价格方案
- 作品展示

**用户中心职责**（调用"用户API接口"支持）：
- 用户注册登录
- 用户资料修改与密码找回
- 用户认证
- 充值积分
- 积分管理
- 积分明细
- 代理推广
- 代理结算

**作品广场职责**：
- 作品展示浏览
- 分类筛选
- 搜索查看
- 作品详情展示

**响应式设计**：
- 支持PC端、移动端

**不包含职责**：
- 视频创作功能
- AI生成功能
- WebSocket实时通信
- 作品发布创建

### 6. 管理后台 (@php/backend/)

**基于Laravel 10开发的管理后台**

**数据管理职责**：
- AI引擎配置
- 音色库、音效库、音乐库管理
- 风格库、角色库管理
- 作品库、会员库管理
- 积分明细管理

**配置管理职责**：
- 第三方AI的API接口地址和密钥管理
- 系统参数配置
- 业务规则配置

**系统管理职责**：
- 用户权限管理
- 系统监控
- 数据统计分析
- 内容审核管理

**🚨 AI平台管理职责（新增）**：
- AI平台使用统计分析（各平台使用量、成功率、响应时间）
- 用户平台偏好分析（偏好设置vs实际使用行为对比）
- 平台性能对比分析（按任务类型的性能指标对比）
- 平台成本效益分析（成本分布、效益对比、优化建议）
- 平台配置智能优化（基于数据的配置建议和策略调整）

### 🎬 视频创作项目管理规范（严格遵循规范版）

#### **项目创建流程规范**
🚨 **严格规范要求**：
- 工具API接口服务严禁储存或中转用户创作视频产生的任何资源文件
- 项目创建流程仅处理文本数据，不涉及任何文件操作
- 不创建文件夹、不存储资源文件、不中转资源文件
- 所有资源文件操作在C-1: AI任务调度流程中由其他服务处理

优化后的项目创建流程，集成风格选择和AI文本处理：

**1. 风格模板获取接口**
```http
GET /py-api/projects/style-templates
Authorization: Bearer {token}
```
返回左侧风格图片列表，包含：卡通可爱、写实风格、科幻风格、古风、现代等

**2. 项目创建接口（优化版）**
```http
POST /py-api/projects/create-with-story
Authorization: Bearer {token}
Content-Type: application/json

{
    "style_id": 1,                    // 选择的风格ID
    "content_type": "own_story",      // "own_story" 或 "story_library"
    "story_content": "用户输入的故事内容或提示词",
    "additional_config": {
        "target_audience": "儿童(3-8岁)"
    }
}
```

**3. 内容类型处理（仅文本数据）**：

**自有故事剧情分镜 (own_story)**：
- 用户在多行输入框录入完整故事文本
- AI处理：仅做文字分镜处理，提取项目标题
- 不对原故事进行任何补充、完善或修改
- AI Prompt: "对以下故事进行文字分镜，提取标题，不修改原故事"
- 输出：分镜文字描述（JSON格式），不产生图片或视频

**AI故事分镜 (ai_story)**：
- 用户在多行输入框录入提示词或故事大纲
- AI处理：生成完整故事文本 + 文字分镜 + 提取标题
- AI Prompt: "根据以下大纲生成完整故事文本并进行文字分镜"
- 输出：故事文本 + 分镜文字描述（JSON格式），不产生图片或视频

**4. 数据库表结构（优化）**：
- **p_projects**: 主项目表 → [完整字段定义](dev-api-guidelines-database.mdc#项目管理表)
  - `style_id` - 风格模板ID
  - `content_type` - 内容类型 (own_story/ai_story)
  - `title` - AI提取的项目标题
  - `story_content` - 故事内容
  - `storyboard` - 分镜结果
  - `status` - 项目状态 (processing/created/in_progress)
  - 更多字段详见：**[dev-api-guidelines-database.mdc - 项目管理表](dev-api-guidelines-database.mdc#项目管理表)**

**5. 项目状态管理**：
- `processing` - AI处理中
- `created` - 创建完成
- `in_progress` - 创作中
- `completed` - 已完成
- `published` - 已发布

**6. 返回数据结构（纯文本数据）**：
```json
{
    "success": true,
    "data": {
        "project_id": 12345,
        "title": "AI提取的项目标题",
        "story_content": "完整故事文本内容",
        "storyboard_text": [
            {
                "scene_id": 1,
                "description": "场景文字描述",
                "estimated_duration": "3-5秒",
                "scene_type": "对话场景"
            }
        ],
        "style_config": {
            "style_name": "卡通可爱",
            "style_id": 1,
            "description": "适合儿童的卡通风格"
        }
    }
}
```

⚠️ **注意**：返回的storyboard_text是文字描述，不包含任何图片、视频等资源文件。资源文件将在C-1: AI任务调度流程中根据这些文字描述生成。

**7. 用户体验优化规范（WebSocket实时进度）**：

**提交时的界面反馈**：
- 点击提交按钮后界面保持不变
- 显示处理罩层 + 实时进度条
- 禁用提交按钮，防止重复提交
- 建立WebSocket连接，订阅项目处理进度频道

**WebSocket进度推送规范**：
```javascript
// WebSocket连接
const ws = new WebSocket('ws://localhost:9502');
ws.send(JSON.stringify({
    action: 'subscribe',
    channel: `project_progress_${project_id}`,
    token: userToken
}));

// 进度消息格式
{
    "type": "progress_update",
    "data": {
        "project_id": 12345,
        "progress": 30,
        "status": "分析故事结构",
        "timestamp": "2024-01-01 12:00:00"
    }
}
```

**进度阶段定义**：
- **10%**: "开始AI处理"
- **20-30%**: "分析故事结构/大纲"
- **50-60%**: "生成故事内容/分镜脚本"
- **70-80%**: "创建分镜脚本/提取标题"
- **85%**: "优化分镜内容"
- **90%**: "保存项目数据"
- **100%**: "项目创建完成"

**前端进度显示**：
```html
<div class="progress-overlay">
    <div class="progress-container">
        <div class="progress-bar">
            <div class="progress-fill" style="width: 30%"></div>
        </div>
        <div class="progress-text">30% - 分析故事结构</div>
        <div class="progress-animation">
            <div class="spinner"></div>
        </div>
    </div>
</div>
```

**处理完成后的界面更新**：
- 关闭WebSocket连接，取消订阅进度频道
- 隐藏处理罩层和进度条
- 在原多行输入框位置渲染剧情分镜列表
- 激活"下一步：绑角色"按钮，变为彩色
- 表示满足进入下一步的条件

**8. 分镜编辑功能规范**：

**分镜列表显示**：
```html
<div class="storyboard-item">
    <div class="scene-number">场景 1</div>
    <div class="scene-description">小猫咪站在森林入口...</div>
    <div class="scene-duration">3-5秒</div>
    <div class="scene-actions">
        <button class="edit-btn">编辑</button>
        <button class="merge-btn">合并</button>
        <button class="delete-btn">删除</button>
        <button class="drag-handle">⋮⋮</button>
    </div>
</div>
```

**支持的编辑操作**：
- **手动编辑**: 点击编辑按钮，弹出编辑框修改分镜内容
- **合并分镜**: 选择相邻分镜进行合并操作
- **拖拽排序**: 通过拖拽手柄调整分镜顺序
- **删除分镜**: 删除不需要的分镜片段
- **添加分镜**: 在任意位置插入新的分镜

**分镜编辑API接口**：
```http
PUT /py-api/projects/{project_id}/storyboard
Authorization: Bearer {token}
Content-Type: application/json

{
    "storyboard": [
        {
            "scene_id": 1,
            "description": "修改后的场景描述",
            "duration": "4秒",
            "order": 1
        }
    ]
}
```

**9. WebSocket服务端实现规范**：

**进度推送服务**：
```php
// 在AI处理过程中推送进度
class ProjectProgressService {
    public function pushProgress($projectId, $progress, $status) {
        $message = [
            'type' => 'progress_update',
            'data' => [
                'project_id' => $projectId,
                'progress' => $progress,
                'status' => $status,
                'timestamp' => date('Y-m-d H:i:s')
            ]
        ];

        // 推送到WebSocket服务
        WebSocketService::pushToChannel(
            "project_progress_{$projectId}",
            $message
        );
    }
}

// 在API控制器中调用
$progressService = new ProjectProgressService();
$progressService->pushProgress($projectId, 30, '分析故事结构');
```

**WebSocket频道管理**：
- 频道命名：`project_progress_{project_id}`
- 订阅验证：验证用户Token和项目权限
- 自动清理：项目完成后自动清理频道订阅

**错误处理**：
- WebSocket连接失败时降级到轮询模式
- 进度推送失败时记录日志但不影响主流程
- 超时处理：超过5分钟无进度更新时显示错误提示

**10. UI业务逻辑处理（文本数据展示）**：
项目创建成功后，Py视频创作工具接收完整项目文本数据，用于：
- 显示项目标题和故事文本内容
- 渲染可编辑的分镜文字描述列表
- 应用选择的风格配置信息
- 激活"下一步：绑角色"按钮状态
- 准备进入C-1: AI任务调度流程（资源文件生成阶段）
- 清理WebSocket连接和进度状态

**重要提醒**：
- 此阶段不涉及任何资源文件的生成、存储或展示
- 分镜列表显示的是文字描述，不是图片预览
- 所有图片、视频、音频等资源文件将在后续的AI任务调度流程中生成
- 工具API接口服务严格遵循"不中转或储存用户创作视频产生的任何资源文件"的规范

### 🎯 AI平台选择接口规范（核心功能）

#### **优化后的一键智能推荐接口**
在Py视频创作工具开发中，使用一个核心接口实现智能平台选择功能：

**核心接口125 - 一键智能推荐（优化版）**
```http
POST /py-api/ai-models/select-platform
Authorization: Bearer {token}
Content-Type: application/json

{
    "business_type": "video",
    "auto_recommend": true,
    "criteria": {
        "priority": "quality"
    }
}
```
- **功能**：一键获取最佳推荐 + 备选方案
- **参数**：
  - `business_type` (必需) - 业务类型：`video`/`image`/`voice`/`sound`/`music`/`text`
  - `auto_recommend` (可选，默认true) - 自动推荐模式
  - `criteria` (可选) - 选择标准配置
- **返回**：
  ```json
  {
    "recommended": {
      "platform": "KlingAI",
      "reason": "质量最佳，适合您的使用习惯"
    },
    "alternatives": [
      {"platform": "LiblibAI", "reason": "速度更快"},
      {"platform": "MiniMax", "reason": "成本更低"}
    ]
  }
  ```
- **使用场景**：页面加载时自动调用，为用户提供即时推荐

**备用接口131/132 - 详细选项（按需使用）**
```http
GET /py-api/ai-models/platform-options?task_type={task_type}
GET /py-api/ai-models/user-recommendations?task_type={task_type}
```
- **功能**：获取详细的平台选项和推荐列表
- **使用场景**：仅在用户点击"查看更多平台"时调用
- **优势**：减少不必要的接口调用，提升响应速度

#### **开发实现规范（优化版）**

**1. 简化的用户体验流程**：
```javascript
// 页面加载时 - 一键获取推荐
async function initializeVideoCreation() {
    try {
        const result = await selectOptimalPlatform({
            business_type: 'video',
            auto_recommend: true
        });

        // 直接显示推荐结果
        showRecommendation(result.recommended);

        // 预加载备选方案（用户可能需要）
        preloadAlternatives(result.alternatives);

    } catch (error) {
        // 降级到默认平台
        useDefaultPlatform('KlingAI');
    }
}

// 用户交互 - 简化到最少步骤
function handleUserChoice() {
    // 大多数用户：直接确认推荐
    if (userClicksStartCreation()) {
        startVideoCreation(recommendedPlatform);
    }

    // 少数用户：查看更多选项
    if (userClicksMoreOptions()) {
        showAlternativePlatforms();
    }
}
```

**2. 性能优化策略**：
- **预加载推荐**：页面加载时立即获取推荐，无需用户等待
- **智能缓存**：推荐结果缓存2分钟，备选方案缓存5分钟
- **降级机制**：接口失败时自动使用默认平台，不阻塞用户操作

**3. 用户体验优化**：
- **零等待**：推荐结果即时显示，无需用户选择
- **一键确认**：90%用户场景只需点击"开始创作"
- **可选展开**：需要时才显示详细选项，避免信息过载

**4. 简化的数据流**：
```
页面加载 → 一键推荐接口 → 显示结果 → 用户确认 → 开始创作
```

#### **架构设计原则**

**1. 统一接口设计**：
- 所有平台选择功能统一在 `AiModelController` 中实现
- 避免在各个业务控制器中重复实现相同功能
- 提供通用的参数化接口，支持所有任务类型

**2. 智能推荐算法**：
- 基于用户历史使用记录分析偏好
- 考虑平台当前健康状态和性能指标
- 结合任务特性和用户设定的优先级

**3. 用户偏好学习**：
- 记录用户的每次平台选择行为
- 分析选择模式（手动选择vs智能推荐）
- 持续优化推荐算法的准确性

**4. 向后兼容性**：
- 保留各控制器中的原有方法，确保现有功能不受影响
- 新项目优先使用统一接口
- 提供迁移指南，支持渐进式升级
- 实时平台状态监控（健康状态检测、性能指标更新）

## 🔄 项目依赖关系

详细的项目依赖关系图已迁移到专门的图表文档，请参考：

📊 **[dev-chart-guidelines.mdc](.cursor/rules/dev-chart-guidelines.mdc)** - 项目依赖关系图

该图表包含：
- **本地开发环境依赖关系**: 展示各组件在开发阶段的依赖关系
- **生产环境依赖关系**: 展示各组件在生产环境的依赖关系
- **环境切换机制**: 开发和生产环境的无缝切换方式
- **依赖关系说明**: 详细的组件依赖说明和环境切换机制

## 🤖 AI模型配置信息

### 支持的AI平台列表
- **LiblibAI**: 图像生成专业平台
- **KlingAI**: 视频生成领导者
- **MiniMax**: 多模态AI平台
- **DeepSeek**: 剧情生成和分镜脚本专家
- **火山引擎豆包**: 专业语音AI平台

### 禁止使用的模型
- OpenAI、GPT系列模型
- anthropic、Claude系列模型

### 业务模型配置矩阵

#### 图像生成业务
**可选平台**: LiblibAI + KlingAI + MiniMax
- **LiblibAI**: 专业图像生成、ComfyUI工作流、风格转换
- **KlingAI**: 高质量图像生成、图像放大、图像修复
- **MiniMax**: 多模态图像生成、图像理解

#### 视频生成业务
**可选平台**: KlingAI + MiniMax
- **KlingAI**: 专业视频生成、图像转视频、视频扩展
- **MiniMax**: 多模态视频生成、视频理解

#### 剧情生成业务
**可选平台**: DeepSeek + MiniMax
- **DeepSeek**: 专业剧情创作、分镜脚本、角色对话
- **MiniMax**: 多模态剧情生成、情节构建

#### 角色生成业务
**可选平台**: LiblibAI + KlingAI + MiniMax
- **LiblibAI**: 角色形象生成、角色设计
- **KlingAI**: 角色动画生成、角色表情
- **MiniMax**: 角色属性生成、角色对话

#### 风格生成业务
**可选平台**: LiblibAI + KlingAI + MiniMax
- **LiblibAI**: 艺术风格生成、风格转换
- **KlingAI**: 视觉风格生成、风格应用
- **MiniMax**: 多模态风格生成、风格理解

#### 音效生成业务
**可选平台**: 火山引擎豆包 + MiniMax
- **火山引擎豆包**: 专业音效处理、音效合成
- **MiniMax**: 多模态音效生成、音效理解

#### 音色生成业务
**可选平台**: MiniMax + 火山引擎豆包
- **MiniMax**: 音色设计、音色合成
- **火山引擎豆包**: 声音复刻、音色处理

#### 音乐生成业务
**可选平台**: MiniMax
- **MiniMax**: 专业音乐生成、音乐创作、音乐理解

#### 语音处理业务
**可选平台**: 火山引擎豆包 + MiniMax
- **火山引擎豆包**: 专业语音合成、音效生成、音色生成
- **MiniMax**: 多模态语音处理、语音理解

## 🔐 Token认证机制规范

### AuthService认证机制
工具API接口服务使用统一的AuthService认证机制，支持两种Token传递方式：

#### 支持的认证方式
1. **Bearer Token方式** (推荐)：
   ```
   Authorization: Bearer {token}
   ```
   - 标准HTTP Bearer Token格式
   - 符合RFC 6750规范
   - 适用于所有API接口

2. **URL参数方式** (兼容性)：
   ```
   ?token={token}
   ```
   - 通过URL参数传递Token
   - 便于快速测试和调试
   - 与Bearer Token方式等效

#### 不支持的认证方式
- **无Bearer前缀的Authorization头**: `Authorization: {token}` ❌ 失败
- **无认证访问**: 直接访问受保护接口 ❌ 失败

#### AuthService.extractToken()处理逻辑
```php
// 优先级1: 从请求参数中获取token参数
$token = $request->input('token');

// 优先级2: 从Authorization头中提取Bearer Token
if (empty($token)) {
    $header = $request->header('Authorization', '');
    $position = strrpos($header, 'Bearer ');
    if ($position !== false) {
        $header = substr($header, $position + 7);
        $token = strpos($header, ',') !== false ? strstr($header, ',', true) : $header;
    }
}
```

#### 安全特性
- Token存储在Redis中，格式：`user:token:{user_id}`
- Token加密存储，使用ApiTokenHelper::encryptToken()
- Token有效期：30-35天随机TTL
- 支持Token失效检查和用户信息验证

## 🚨 关键架构原则

### 1. 资源下载架构铁律
**核心原则**：
1. **资源下载铁律**: 所有基于用户产生的资源文件（视频、风格、角色、音乐、音效等）都必须由"Py视频创作工具"直接从AI平台下载到本地
2. **服务器职责边界**: API服务器只负责管理资源的URL、状态、元数据等附件信息，绝不进行资源文件的中转下载
3. **架构禁止事项**: 严禁在API服务器上进行任何形式的资源文件生成、处理、存储、中转下载

**开发约束规则**：
1. **控制器设计约束**: 资源相关控制器只能提供URL和状态管理，禁止文件操作
2. **服务层设计约束**: 资源相关服务只能进行元数据管理，禁止文件生成和处理逻辑
3. **存储架构约束**: 服务器存储只保存资源元数据，禁止保存实际资源文件
4. **下载流程约束**: Py视频创作工具 → API获取URL → 直接从AI平台下载，禁止服务器中转

### 2. WebSocket使用边界
- ✅ **仅Py视频创作工具使用**：AI生成进度推送、任务状态通知
- ❌ **WEB工具禁用**：避免不必要的连接和资源消耗
- 🔒 **安全传输**：密钥加密传输，不持久化存储

### 3. 避免循环依赖
- WebSocket服务只负责推送，不参与业务逻辑
- 积分变动通知改为异步事件驱动
- 使用事件总线模式解耦组件间依赖

### 4. 性能优化策略
- **并发支持**：设计支持1000用户同时使用
- **缓存策略**：MySQL主存储 + Redis缓存层
- **超时管理**：图像5分钟、视频30分钟、文本1分钟、语音2分钟

## 🎨 作品发布完整规则

### 可发布作品类型
1. **风格作品**: 用户创建的剧情风格可发布到风格广场
2. **角色作品**: 用户创建的角色可发布到角色广场
3. **视频作品**: 用户创作完成的视频可发布到作品广场
4. **发布时机**: 任何时间都可以提交发布申请

### 作品发布流程
1. **资源上传要求**: 发布任何作品都必须上传相关的资源文件
2. **资源重命名机制**: 上传的资源名称会被系统自动重命名
3. **资源地址保护**: 重命名后的资源地址不返回给用户，仅供系统内部使用
4. **审核机制**: 提交后进入审核流程，审核是否通过由系统决定

### 发布安全规则
1. **资源隔离**: 发布资源与用户创作资源完全隔离
2. **地址保护**: 发布资源地址不暴露给用户
3. **权限控制**: 仅审核通过的作品可在广场展示
4. **版权保护**: 发布资源受系统版权保护机制管理

## 📊 API接口业务状态码定义规范

1. 业务状态码和HTTP状态码相同的会映射到HTTP状态码，业务状态码和HTTP状态码不同的HTTP状态码将被设置为200。
2. 所有的业务状态码和状态码说明必须在 `php/api/app/Enums/ApiCodeEnum.php` 中设置。

## 📋 开发文档应用规则

### 控制器层 ↔ 服务层架构规范

**工具API接口服务采用分层架构模式，明确控制器层与服务层的职责分离：**

#### 目录结构与职责分工
```
php/api/
├── app/Http/Controllers/
│   ├── PyApi/              # Py视频创作工具API控制器
│   ├── WebApi/             # WEB工具API控制器
│   └── AdminApi/           # 管理后台API控制器
├── app/Services/
│   ├── PyApi/              # Py视频创作工具业务服务
│   ├── WebApi/             # WEB工具业务服务
│   └── AdminApi/           # 管理后台业务服务
└── app/WebSocket/          # WebSocket服务层
    ├── WebSocketService.php      # WebSocket业务逻辑处理
    ├── MessageHandler.php        # 消息处理与分发
    └── ConnectionManager.php     # 连接管理与状态维护
```

#### 职责分离原则

**控制器层职责**：
- HTTP请求接收与路由处理
- 请求参数验证与格式化
- 响应数据格式化与返回
- 异常处理与错误响应
- WebSocket连接建立与消息路由

**服务层职责**：
- 具体业务逻辑实现
- 数据库操作与事务管理
- 外部API调用与集成
- 复杂算法与数据处理

**WebSocket服务层职责**：
- WebSocket消息处理与分发
- 实时通信业务逻辑
- 连接状态管理与维护
- AI生成进度推送服务

#### 调用流程

**HTTP API调用流程**：
```
HTTP请求 → 控制器层 → 服务层 → 数据库/外部服务 → 服务层 → 控制器层 → HTTP响应
```

**WebSocket通信流程**：
```
WebSocket连接 → WebSocketController → WebSocketService → 业务逻辑处理 → 消息推送 → 客户端
```

**混合调用流程** (AI生成场景)：
```
HTTP请求 → 控制器层 → 服务层 → AI服务调用 → WebSocket推送进度 → HTTP响应结果
```

## 🔄 核心业务流程

### 主要业务流程
**核心创作流程**：选风格+写剧情 → 绑角色 → 生成图像 → 视频编辑 → 本地导出
**可选扩展流程**：本地导出 → [用户选择] → 作品发布到广场

**职责分工**：
- **服务端负责**：风格管理、剧情AI生成、角色管理、图像AI生成、素材存储管理
- **客户端负责**：视频时间轴编辑、本地素材合成、UI交互、作品导出

### 详细业务流程参考
完整的系统架构图和业务流程图已迁移到专门的图表文档，请参考：

📊 **[dev-chart-guidelines.mdc](.cursor/rules/dev-chart-guidelines.mdc)** - AI视频创作工具系统图表集合

#### 🔄 主要业务流程图包含：

**1. Py视频创作工具业务流程图**
- **用户管理流程** (A-1到A-5)：注册、登录、Token验证、密码管理、忘记密码重置
- **业务功能流程** (B-1到B-5)：充值积分、积分管理、代理推广、代理结算、数据处理
- **项目管理流程** (C-0)：视频创作项目创建流程（纯文本数据处理版）
- **AI核心流程** (C-1到C-9)：AI任务调度、AI生成成功、积分不足、处理失败、超时中断、资源管理、资源下载、作品发布、环境切换机制

**2. WEB网页工具业务流程图**
- WEB网页工具1：用户注册登录流程
- WEB网页工具2：作品广场浏览流程
- WEB网页工具3：用户中心管理流程

**3. 管理后台业务流程图**
- 管理后台业务流程1：系统配置管理流程
- 管理后台业务流程2：用户管理流程
- 管理后台业务流程3：内容审核管理流程
- 管理后台业务流程4：数据统计分析流程

**4. 系统架构图表**
- 完整系统架构图（环境切换优化版）
- AI服务集成模拟机制架构图
- AI服务调用流程对比图
- 项目依赖关系图
- **业务流程6**: 资源下载完成流程（核心流程）
- **业务流程7**: 可选作品发布流程（增值服务）
- **业务流程8**: 环境切换机制流程（核心机制）

该章节包含了完整的Mermaid序列图，详细描述了环境切换机制、积分安全、错误处理等所有关键业务场景。

#### 📋 核心组件功能职责规范索引

为增强AI程序员的理解和行为规范化，**[dev-chart-guidelines.mdc](.cursor/rules/dev-chart-guidelines.mdc)** 文档还包含：

**🎯 AI程序员行为规范化指南**：
- **工具API接口服务功能职责矩阵**: Py视频创作工具、WEB网页工具、管理后台的详细职责边界
- **关键架构边界规范**: 资源下载架构铁律、WebSocket使用边界、功能禁止列表
- **WEB网页工具功能边界详细规范**: 允许和禁止的功能职责清单
- **AI程序员开发指导原则**: 严格的开发约束和架构遵循规则
- **详细规范文档参考**: 完整的功能职责定义和开发指南索引

这些规范确保AI程序员能够：
- ✅ **准确理解**: 每个组件的职责边界和功能范围
- ✅ **规范开发**: 严格遵循架构约束和开发原则
- ✅ **避免违规**: 防止跨界操作和架构违规行为
- ✅ **提升效率**: 快速定位相关规范和开发指导

## 📊 数据库设计概述

**📋 完整数据库设计规范**: 详见 **[dev-api-guidelines-database.mdc](.cursor/rules/dev-api-guidelines-database.mdc)** 文档

该文档包含完整的多个数据表设计，包括：
- **详细字段类型和长度限制**: 每个字段的确切类型、长度、约束、默认值
- **数据表关系说明**: 5条核心关系链和完整的外键关联
- **索引优化建议**: 高频查询字段、复合索引、唯一索引策略
- **数据一致性约束**: 外键约束、枚举类型、JSON验证规范

### 核心数据表结构

#### 基础业务表
- **p_users**: 用户表（用户信息、认证、偏好设置）→ [详细字段定义](dev-api-guidelines-database.mdc#基础业务表)
- **p_points_transactions**: 积分交易表（积分流水、冻结、返还）→ [详细字段定义](dev-api-guidelines-database.mdc#基础业务表)
- **p_points_freeze**: 积分冻结表（冻结机制、安全保障）→ [详细字段定义](dev-api-guidelines-database.mdc#基础业务表)

#### AI生成相关表
- **p_ai_model_configs**: AI模型配置表（AI平台配置、模型管理）→ [详细字段定义](dev-api-guidelines-database.mdc#AI生成相关表)
- **p_style_library**: 风格库表（剧情风格管理、AI生成配置）→ [详细字段定义](dev-api-guidelines-database.mdc#AI生成相关表)
- **p_character_library**: 角色库表（AI生成角色信息、特征描述）→ [详细字段定义](dev-api-guidelines-database.mdc#AI生成相关表)
- **p_character_categories**: 角色分类表（角色分类管理）→ [详细字段定义](dev-api-guidelines-database.mdc#AI生成相关表)
- **p_music_library**: 音乐库表（AI生成音乐存储、MiniMax平台）→ [详细字段定义](dev-api-guidelines-database.mdc#AI生成相关表)
- **p_sound_library**: 音效库表（AI生成音效存储、火山引擎豆包平台）→ [详细字段定义](dev-api-guidelines-database.mdc#AI生成相关表)
- **p_timbre_library**: 音色库表（AI生成音色存储、双平台支持）→ [详细字段定义](dev-api-guidelines-database.mdc#AI生成相关表)
- **p_story_library**: 故事库表（AI生成故事内容、项目表关联）→ [详细字段定义](dev-api-guidelines-database.mdc#AI生成相关表)

#### 核心资源管理表
- **p_resources**: AI生成资源表（资源管理、模块关联、状态跟踪）→ [详细字段定义](dev-api-guidelines-database.mdc#核心资源管理表)
- **p_resource_versions**: 资源版本表（版本控制、提示词管理、本地导出）→ [详细字段定义](dev-api-guidelines-database.mdc#核心资源管理表)

#### 任务管理表
- **p_ai_generation_tasks**: AI生成任务表（任务状态、进度、结果）→ [详细字段定义](dev-api-guidelines-database.mdc#任务管理表)
- **p_websocket_sessions**: WebSocket会话表（连接管理、状态同步）→ [详细字段定义](dev-api-guidelines-database.mdc#任务管理表)

#### 项目管理表
- **p_projects**: 项目表（视频创作项目管理）→ [详细字段定义](dev-api-guidelines-database.mdc#项目管理表)
- **p_project_collaborators**: 项目协作者表（项目协作管理）→ [详细字段定义](dev-api-guidelines-database.mdc#项目管理表)

#### 用户成长系统表
- **p_user_levels**: 用户等级表（等级系统配置）→ [详细字段定义](dev-api-guidelines-database.mdc#用户成长系统表)
- **p_achievements**: 成就表（成就系统）→ [详细字段定义](dev-api-guidelines-database.mdc#用户成长系统表)
- **p_user_achievements**: 用户成就表（用户成就记录）→ [详细字段定义](dev-api-guidelines-database.mdc#用户成长系统表)
- **p_daily_tasks**: 每日任务表（每日任务系统）→ [详细字段定义](dev-api-guidelines-database.mdc#用户成长系统表)
- **p_user_daily_tasks**: 用户每日任务表（用户每日任务记录）→ [详细字段定义](dev-api-guidelines-database.mdc#用户成长系统表)
- **p_growth_histories**: 用户成长历史表（成长轨迹记录）→ [详细字段定义](dev-api-guidelines-database.mdc#用户成长系统表)

#### 用户作品管理表
- **p_user_works**: 用户作品表（用户创作的作品管理）→ [详细字段定义](dev-api-guidelines-database.mdc#社交功能表)
  - 包含17个字段：用户ID、作品标题、作品类型、内容描述、项目关联、状态管理、可见性控制、元数据、标签、统计数据等
  - 支持多种作品类型：video/image/music/story/character
  - 完整的状态管理：draft/completed/published/archived
  - 社交功能支持：浏览、点赞、评论、分享统计

#### 作品发布和社交表
- **p_work_plaza**: 作品广场表（公开作品展示平台）→ [详细字段定义](dev-api-guidelines-database.mdc#社交功能表)
  - 包含22个字段：作品关联、用户信息、展示内容、分类标签、预览资源、统计数据、评分系统、审核机制、精选推荐等
  - 支持多种作品分类：video/image/music/story/character/mixed
  - 完整的审核流程：pending/approved/rejected/auto_approved
  - 丰富的统计功能：浏览、点赞、评论、分享、下载、评分统计

- **p_work_shares**: 作品分享表（分享链接、权限控制、访问统计）→ [详细字段定义](dev-api-guidelines-database.mdc#社交功能表)

#### 社交功能表
- **p_follows**: 关注关系表（用户关注系统）→ [详细字段定义](dev-api-guidelines-database.mdc#社交功能表)
  - 包含8个字段：关注者ID、被关注者ID、关注状态、通知设置、时间戳等
  - 支持关注状态管理：active/blocked/pending
  - 完整的通知控制和时间追踪

- **p_likes**: 点赞表（通用点赞系统）→ [详细字段定义](dev-api-guidelines-database.mdc#社交功能表)
  - 包含5个字段：用户ID、目标类型、目标ID、点赞时间
  - 支持多种目标类型：work/comment/user/project
  - 通用的多态关联设计

- **p_comments**: 评论表（通用评论系统）→ [详细字段定义](dev-api-guidelines-database.mdc#社交功能表)
  - 包含12个字段：用户ID、目标类型、目标ID、评论内容、父评论ID、状态管理、统计数据、置顶功能、时间戳等
  - 支持多种评论目标：work/project/user
  - 完整的回复功能和状态管理：active/hidden/deleted/pending_review
  - 丰富的互动功能：点赞统计、回复统计、置顶功能

- **p_work_interactions**: 作品互动表（点赞、评论、分享记录的汇总统计）→ [详细字段定义](dev-api-guidelines-database.mdc#社交功能表)

## ⚡ 性能期望与技术要求

### **系统性能指标**
- **响应延迟**：≤30000ms（30秒）
- **并发支持**：1000用户同时使用
- **系统可用性**：99.9%
- **API响应时间**：平均200ms
- **AI生成时间**：文本15-30秒，图像30-60秒
- **WebSocket连接**：支持长连接，自动重连
- **数据一致性**：MySQL+Redis双重保障
- **安全性**：密钥加密传输，权限二次验证

### **AI生成超时配置**
- 🕐 **图像生成**: 5分钟超时（可配置）
- 🕐 **视频生成**: 30分钟超时（可配置）
- 🕐 **文本生成**: 1分钟超时（可配置）
- 🕐 **语音合成**: 2分钟超时（可配置）

### **环境切换性能对比**
| 环境模式 | 响应时间 | 费用产生 | 数据真实性 | 适用场景 |
|---------|---------|---------|-----------|---------|
| **mock模式** | 100-500ms | 无费用 | 模拟数据 | 开发测试 |
| **real模式** | 1-30秒 | 真实费用 | 真实数据 | 生产环境 |

## 🌐 AI平台API接口指导文档

- **DeepSeek API指导文档**: `ai-api-deepseek.com-guidelines.mdc`
- **KlingAI API指导文档**: `ai-api-klingai.com-guidelines.mdc`
- **LiblibAI API指导文档**: `ai-api-liblibai.art-guidelines.mdc`
- **MiniMax API指导文档**: `ai-api-minimaxi.com-guidelines.mdc`
- **火山引擎豆包API指导文档**: `ai-api-volcengine.com-guidelines.mdc`


## 📚 开发文档使用指南

### 🎯 **分类文档应用场景明确定义**

**针对 @php/api/ 目录的"工具API接口服务"开发，必须严格按照以下规则使用分类文档：**

#### **📚 六大核心文档体系**

1. **🆕 新功能开发文档**：`dev-api-guidelines-add.mdc`
2. **🔧 问题修复文档**：`dev-api-guidelines-edit.mdc`
3. **🤖 【工具API接口服务】环境切换AI服务文档**：`dev-aiapi-guidelines.mdc`
4. **📊 数据库设计规范文档**：`dev-api-guidelines-database.mdc` - **多个数据表完整设计**
   - 包含详细字段类型、长度限制、约束条件、索引建议
   - 涵盖基础业务、AI生成、资源管理、任务管理、用户成长、社交功能等全部数据表
   - 提供数据表关系说明、性能优化建议、数据一致性约束规范
5. **🤖 【Py视频创作工具】对接【工具API接口服务】文档**：`dev-api-guidelines-pyapi.mdc`
6. **🤖 【WEB网页工具】对接【工具API接口服务】文档**：`dev-api-guidelines-webapi.mdc`
7. **🤖 【管理后台】对接【工具API接口服务】文档**：`dev-api-guidelines-adminapi.mdc`

#### **🆕 新功能开发场景**
**主要文档**：`dev-api-guidelines-add.mdc`
**辅助文档**：`dev-aiapi-guidelines.mdc`（当涉及AI功能时）

**适用情况**：
- ✅ **新增API接口开发**：创建全新的控制器、服务层、中间件
- ✅ **新增数据库表设计**：设计新的数据表结构和迁移程序
- ✅ **新增业务模块开发**：实现全新的业务功能模块
- ✅ **新增WebSocket处理器**：开发Py视频创作工具专用的WebSocket服务
- ✅ **新增AI服务环境切换功能**：集成新的AI生成服务和功能（必须配合`dev-aiapi-guidelines.mdc`）
- ✅ **扩展性架构设计**：设计可扩展的系统架构
- ✅ **高级功能扩展**：用户成长路径、个性化推荐、AI模型管理等

#### **🔧 问题修复场景**
**主要文档**：`dev-api-guidelines-edit.mdc`
**辅助文档**：`dev-aiapi-guidelines.mdc`（当修复AI相关问题时）

**适用情况**：
- ✅ **架构违规修复**：修复WebSocket边界违规、职责边界不清等问题
- ✅ **性能指标修正**：统一并发用户数为1000、响应时间优化等
- ✅ **安全机制强化**：积分系统安全加固、认证机制完善
- ✅ **接口规范修正**：移除违规接口、修正API设计
- ✅ **数据库结构修复**：修正表字段、索引优化、关系调整
- ✅ **代码质量提升**：重构现有代码、优化业务逻辑
- ✅ **AI服务调用问题修复**：修复AI API调用失败、超时、格式错误等问题
- ✅ **环境切换机制修复**：修复 mock/real 模式切换问题

#### **🤖 【工具API接口服务】环境切换AI服务场景**
**主要文档**：`dev-aiapi-guidelines.mdc`
**配合文档**：根据具体需求选择 `dev-api-guidelines-add.mdc` 或 `dev-api-guidelines-edit.mdc` 文档

**适用情况**：
- ✅ **AI API接口调用**：通过 AiServiceClient::call() 调用DeepSeek、LiblibAI、KlingAI、MiniMax、火山引擎豆包等AI服务
- ✅ **AI服务环境切换开发**：实现 mock/real 模式的无缝切换，确保开发和生产环境的一致性
- ✅ **AI服务错误处理**：处理AI API调用失败、超时、格式错误等问题
- ✅ **AI服务性能优化**：优化AI API调用性能、实现负载均衡、服务降级
- ✅ **AI服务监控**：监控AI API调用状态、成功率、响应时间等指标
- ✅ **环境切换配置**：配置 AI_SERVICE_MODE 环境变量和相关参数

**包含内容**：
- 🤖 **5个AI平台完整接口规范**：详见 **"🤖 AI模型配置信息"** 章节
- 🤖 **87个AI API接口**：文本生成、图像生成、语音合成、视频生成、音效处理、音频混合等
- 🤖 **环境切换机制**：AiServiceClient 和 ThirdPartyServiceClient 的使用规范
- 🤖 **配置管理**：php/api/config/ai.php 的配置说明和最佳实践
- 🤖 **分平台功能支持详情**：详见 **"🤖 AI模型配置信息"** 章节的业务模型配置矩阵

#### **🎯 第三方服务环境切换场景**
**主要文档**：`dev-thirdapi-guidelines.mdc`
**配合文档**：根据具体需求选择 `dev-api-guidelines-add.mdc` 或 `dev-api-guidelines-edit.mdc` 文档

**适用情况**：
- ✅ **第三方API接口调用**：通过 ThirdPartyServiceClient::call() 调用微信、支付宝、短信等第三方服务
- ✅ **第三方服务环境切换**：实现 mock/real 模式的无缝切换
- ✅ **第三方服务错误处理**：处理第三方API调用失败、超时等问题
- ✅ **第三方服务监控**：监控第三方API调用状态和性能指标

#### **📊 文档使用优先级规则**

1. **🔍 问题诊断优先级**：
   - **AI服务环境切换相关问题** → 优先使用 `dev-aiapi-guidelines.mdc` + 对应的add/edit文档
   - **第三方服务环境切换相关问题** → 优先使用 `dev-thirdapi-guidelines.mdc` + 对应的add/edit文档
   - **数据库设计/迁移相关问题** → 优先使用 `dev-api-guidelines-database.mdc` + 对应的add/edit文档
   - **现有功能问题修复** → 使用 `dev-api-guidelines-edit.mdc`
   - **新功能需求开发** → 使用 `dev-api-guidelines-add.mdc`

2. **🎯 开发适配规则**：
   - **基础设施开发**：主要使用 `dev-api-guidelines-add.mdc`
   - **数据库设计开发**：`dev-api-guidelines-database.mdc` + `dev-api-guidelines-add.mdc`
   - **核心AI功能开发**：`dev-api-guidelines-add.mdc` + `dev-aiapi-guidelines.mdc`
   - **第三方服务集成**：`dev-api-guidelines-add.mdc` + `dev-thirdapi-guidelines.mdc`
   - **高级功能开发**：多个文档并用，根据具体需求选择
   - **维护阶段**：主要使用 `dev-api-guidelines-edit.mdc` + 对应的专项文档

3. **🚨 紧急修复优先级**：
   - **AI服务故障** → 必须使用 `dev-aiapi-guidelines.mdc` + `dev-api-guidelines-edit.mdc`
   - **第三方服务故障** → 必须使用 `dev-thirdapi-guidelines.mdc` + `dev-api-guidelines-edit.mdc`
   - **数据库故障/性能问题** → 必须使用 `dev-api-guidelines-database.mdc` + `dev-api-guidelines-edit.mdc`
   - **环境切换问题** → 优先使用对应的专项文档 + `dev-api-guidelines-edit.mdc`
   - **生产环境问题** → 优先使用 `dev-api-guidelines-edit.mdc`
   - **架构合规性问题** → 必须使用 `dev-api-guidelines-edit.mdc`

4. **🤖 AI功能开发专用规则**：
   - **所有AI相关开发** → 必须使用 `dev-aiapi-guidelines.mdc` 作为主要参考
   - **新增AI功能** → `dev-aiapi-guidelines.mdc` + `dev-api-guidelines-add.mdc`
   - **修复AI功能** → `dev-aiapi-guidelines.mdc` + `dev-api-guidelines-edit.mdc`
   - **AI服务环境切换** → 仅使用 `dev-aiapi-guidelines.mdc` + 本文档环境切换规范

#### **🔄 多文档协作机制**

1. **📋 文档协作优先级**：
   ```
   AI功能开发：`dev-aiapi-guidelines.mdc` (主) + `dev-api-guidelines-add.mdc` (辅)
   AI问题修复：`dev-aiapi-guidelines.mdc` (主) + `dev-api-guidelines-edit.mdc` (辅)
   第三方服务开发：`dev-thirdapi-guidelines.mdc` (主) + `dev-api-guidelines-add.mdc` (辅)
   第三方服务修复：`dev-thirdapi-guidelines.mdc` (主) + `dev-api-guidelines-edit.mdc` (辅)
   非AI新功能：`dev-api-guidelines-add.mdc` (主) + 其他文档 (可选)
   非AI问题修复：`dev-api-guidelines-edit.mdc` (主)
   ```

2. **🎯 具体使用场景判断**：
   - **包含"AI"、"生成"、"智能"关键词** → 必须使用 `dev-aiapi-guidelines.mdc`
   - **涉及DeepSeek、LiblibAI、KlingAI、MiniMax、火山引擎豆包** → 必须使用 `dev-aiapi-guidelines.mdc`
   - **涉及微信、支付宝、短信等第三方服务** → 必须使用 `dev-thirdapi-guidelines.mdc`
   - **涉及数据表、迁移、模型、字段、索引** → 必须使用 `dev-api-guidelines-database.mdc`
   - **文本生成、图像生成、语音合成、视频生成** → 必须使用 `dev-aiapi-guidelines.mdc`
   - **环境切换、AiServiceClient、ThirdPartyServiceClient** → 使用对应的专项文档

#### **⚠️ 重要注意事项**

1. **🛡️ 架构合规性检查**：
   - 所有开发必须遵循index-new.mdc中定义的职责边界
   - WebSocket仅限Py视频创作工具使用
   - 并发用户数统一为1000
   - AI服务调用必须使用 AiServiceClient::call()
   - 第三方服务调用必须使用 ThirdPartyServiceClient::call()
   - 环境切换必须通过配置文件实现，不得硬编码

2. **📋 文档完整性保证**：
   - **环境切换机制**：完整实现 mock/real 模式自动切换
   - **`dev-api-guidelines-add.mdc`**：包含119个API接口、15个开发阶段、完整AI服务环境切换架构
   - **`dev-api-guidelines-edit.mdc`**：包含8个控制器、完整中间件、监控系统
   - **`dev-api-guidelines-database.mdc`**：包多个数据表、完整字段类型规范、索引优化建议
     * 基础业务表(3个)、AI生成表(8个)、资源管理表(4个)、任务管理表(2个)
     * 项目管理表(2个)、用户成长表(6个)、系统管理表(6个)、社交功能表(6个)
     * 邀请佣金表(2个)、下载推广表(1个)、工作流管理表(2个)、其他功能表(7个)
     * 详细字段类型：BIGINT、VARCHAR、TEXT、JSON、BOOLEAN、DECIMAL、TIMESTAMP、ENUM
     * 完整约束规范：主键、外键、唯一索引、复合索引、数据一致性约束
   - **`dev-api-guidelines-pyapi.mdc`**：包含41个控制器、252个API接口、AI平台选择功能优化 🚨 升级
   - **`dev-aiapi-guidelines.mdc`**：包含5个AI平台、87个AI接口、环境切换规范
   - **`dev-thirdapi-guidelines.mdc`**：包含第三方服务环境切换规范
   - **总接口统计**：339个总接口（252个PyAPI接口 + 87个AI接口）🚨 升级优化

3. **🤖 AI服务环境切换规范**：
   - 所有AI功能开发必须使用`dev-aiapi-guidelines.mdc`作为权威依据
   - AI API调用必须使用 AiServiceClient::call() 方法
   - 支持5个AI平台：DeepSeek、LiblibAI、KlingAI、MiniMax、火山引擎豆包
   - 环境切换通过 AI_SERVICE_MODE 环境变量控制 (mock/real)
   - 模拟服务超时30秒，真实服务超时60秒
   - 必须实现AI服务降级和错误处理机制

4. **🔗 第三方服务环境切换规范**：
   - 所有第三方服务调用必须使用 ThirdPartyServiceClient::call() 方法
   - 环境切换通过 THIRD_PARTY_MODE 环境变量控制 (mock/real)
   - 支持微信、支付宝、短信等主要第三方服务
   - 必须实现第三方服务降级和错误处理机制

5. **🔄 持续更新机制**：
   - **新增功能** → 更新 `dev-api-guidelines-add.mdc`
   - **修复问题** → 更新 `dev-api-guidelines-edit.mdc`
   - **AI服务变更** → 更新 `dev-aiapi-guidelines.mdc`
   - **第三方服务变更** → 更新 `dev-thirdapi-guidelines.mdc`
   - **数据库结构变更** → 更新 `dev-api-guidelines-database.mdc`
   - **环境切换机制变更** → 更新 `index-new.mdc` 和对应专项文档
   - 定期同步所有文档，确保与`index-new.mdc`规范一致
   - AI服务接口变更需要同步更新相关控制器和服务层

6. **📊 文档使用统计和监控**：
   - 优先使用覆盖率最高的文档（`dev-api-guidelines-add.mdc`）
   - AI相关开发必须使用`dev-aiapi-guidelines.mdc`（100%AI接口覆盖）
   - 第三方服务开发必须使用`dev-thirdapi-guidelines.mdc`
   - 数据库相关开发必须使用`dev-api-guidelines-database.mdc`（49个表完整覆盖）
     * 涵盖所有实际存在的39个表 + 规划中的10个表
     * 提供精确的字段类型、长度限制、约束条件定义
     * 包含5条核心关系链、索引优化建议、性能优化策略
     * 支持数据库迁移、模型设计、性能调优的完整指导
   - 问题修复优先使用`dev-api-guidelines-edit.mdc`（包含完整修复方案）
   - 定期评估文档使用效果，持续优化文档结构

#### **🎯 文档选择决策树**

```
开发任务分析
├─ 是否需要理解架构/业务流程？
│  ├─ 系统架构理解 → `dev-chart-guidelines.mdc` (架构图表) + `index-new.mdc` (架构规范)
│  ├─ 业务流程理解 → `dev-chart-guidelines.mdc` (流程图表) + 对应的API文档
│  ├─ 组件职责边界 → `dev-chart-guidelines.mdc` (职责矩阵) + `index-new.mdc` (详细规范)
│  ├─ 新人入门学习 → `dev-chart-guidelines.mdc` (图表概览) → `index-new.mdc` (完整规范)
│  └─ 否 → 继续判断
├─ 是否涉及数据库操作？
│  ├─ 数据表设计/修改 → `dev-api-guidelines-database.mdc` (必须主文档) + `dev-api-guidelines-add.mdc` (新增规范)
│  │  ├─ 新增数据表 → 参考其中表的设计规范和字段类型标准
│  │  ├─ 修改表结构 → 遵循字段类型规范和约束条件
│  │  └─ 表关系设计 → 参考5条核心关系链和外键约束规范
│  ├─ 数据库迁移 → `dev-api-guidelines-database.mdc` (字段类型规范) + `dev-api-guidelines-edit.mdc` (修改规范)
│  │  ├─ 创建迁移文件 → 使用详细的字段类型和长度限制定义
│  │  ├─ 修改字段类型 → 遵循DECIMAL、VARCHAR、ENUM等类型规范
│  │  └─ 添加索引约束 → 参考索引建议和性能优化策略
│  ├─ 模型关系设计 → `dev-api-guidelines-database.mdc` (关系说明) + 对应的API文档
│  │  ├─ 外键关联 → 参考数据表关系说明和关联规范
│  │  ├─ 多态关联 → 参考通用设计模式（如p_likes、p_comments表）
│  │  └─ 业务关联 → 参考核心关系链设计
│  ├─ 数据库性能优化 → `dev-api-guidelines-database.mdc` (索引建议) + `dev-api-guidelines-edit.mdc` (优化规范)
│  │  ├─ 索引优化 → 参考高频查询字段、复合索引、唯一索引策略
│  │  ├─ 查询优化 → 参考数据一致性约束和性能建议
│  │  └─ 存储优化 → 参考字段类型选择和存储策略
│  └─ 否 → 继续判断
├─ 是否涉及环境切换？
│  ├─ AI服务环境切换 → `dev-aiapi-guidelines.mdc` (主) + `index-new.mdc` (架构参考)
│  ├─ 第三方服务环境切换 → `dev-thirdapi-guidelines.mdc` (主) + `index-new.mdc` (架构参考)
│  └─ 否 → 继续判断
├─ 是否涉及客户端对接？
│  ├─ Py视频创作工具对接 → `dev-api-guidelines-pyapi.mdc` (最高权重) + `dev-chart-guidelines.mdc` (业务流程)
│  ├─ WEB工具对接 → `dev-api-guidelines-webapi.mdc` (最高权重) + `dev-chart-guidelines.mdc` (业务流程)
│  ├─ 管理后台对接 → `dev-api-guidelines-adminapi.mdc` (最高权重) + `dev-chart-guidelines.mdc` (业务流程)
│  └─ 否 → 继续判断
├─ 是否涉及AI功能？
│  ├─ 是 → `dev-aiapi-guidelines.mdc` (必须主文档)
│  │  ├─ 新增AI功能 → + `dev-api-guidelines-add.mdc` (新增规范) + `dev-chart-guidelines.mdc` (AI流程)
│  │  ├─ 修复AI功能 → + `dev-api-guidelines-edit.mdc` (修复规范) + `dev-chart-guidelines.mdc` (AI流程)
│  │  └─ 纯AI接口调用 → 仅使用 `dev-aiapi-guidelines.mdc`
│  └─ 否 → 继续判断
├─ 是否涉及第三方服务？
│  ├─ 是 → `dev-thirdapi-guidelines.mdc` (必须主文档)
│  │  ├─ 新增第三方功能 → + `dev-api-guidelines-add.mdc` (新增规范)
│  │  ├─ 修复第三方功能 → + `dev-api-guidelines-edit.mdc` (修复规范)
│  │  └─ 纯第三方接口调用 → 仅使用 `dev-thirdapi-guidelines.mdc`
│  └─ 否 → 继续判断
├─ 是新功能开发？
│  ├─ 是 → `dev-api-guidelines-add.mdc` (主文档)
│  │  ├─ 涉及AI功能 → + `dev-aiapi-guidelines.mdc` (AI专项规范) + `dev-chart-guidelines.mdc` (AI流程)
│  │  ├─ 涉及第三方服务 → + `dev-thirdapi-guidelines.mdc` (第三方专项规范)
│  │  ├─ 涉及数据库设计 → + `dev-api-guidelines-database.mdc` (数据库规范)
│  │  └─ 涉及客户端对接 → + 对应的 `dev-api-guidelines-pyapi/webapi/adminapi.mdc` + `dev-chart-guidelines.mdc` (业务流程)
│  └─ 否 → 继续判断
├─ 是问题修复？
│  ├─ 是 → `dev-api-guidelines-edit.mdc` (主文档)
│  │  ├─ AI相关问题 → + `dev-aiapi-guidelines.mdc` (AI专项规范) + `dev-chart-guidelines.mdc` (AI流程)
│  │  ├─ 第三方服务问题 → + `dev-thirdapi-guidelines.mdc` (第三方专项规范)
│  │  ├─ 数据库相关问题 → + `dev-api-guidelines-database.mdc` (数据库规范)
│  │  └─ 客户端对接问题 → + 对应的 `dev-api-guidelines-pyapi/webapi/adminapi.mdc` + `dev-chart-guidelines.mdc` (业务流程)
│  └─ 否 → 继续判断
└─ 复杂场景 → 多文档组合使用
   ├─ 架构重构 → `dev-chart-guidelines.mdc` (架构图) + `dev-api-guidelines-edit.mdc` + `dev-api-guidelines-add.mdc`
   ├─ 数据库重构 → `dev-api-guidelines-database.mdc` (数据库设计) + `dev-api-guidelines-edit.mdc` (迁移规范)
   ├─ 性能优化 → `dev-chart-guidelines.mdc` (性能流程) + `dev-api-guidelines-database.mdc` (索引优化) + `dev-api-guidelines-edit.mdc`
   ├─ 安全加固 → `dev-api-guidelines-edit.mdc` + `dev-api-guidelines-add.mdc` (如需新增安全功能)
   ├─ 全栈开发 → `dev-chart-guidelines.mdc` (完整架构) + `dev-api-guidelines-database.mdc` (数据设计) + 对应的多个文档组合
   └─ AI程序员规范化 → `dev-chart-guidelines.mdc` (行为规范指南) + `index-new.mdc` (详细规范)
```


## 📝 技术栈总结

### 后端技术栈
- **管理后台**: Laravel 10
- **工具API接口服务**: Lumen 10
- **数据库**: MySQL 8.0.12
- **缓存**: Redis 7.4.2
- **Web服务器**: Nginx 1.26.2
- **PHP版本**: 8.1.29

### 前端技术栈
- **WEB网页工具**: 响应式UI布局（支持PC和移动），全静态前端调用工具API接口服务
- **Py视频创作工具**: Python + PySide6 + PyInstaller + WebSocket客户端

### 开发工具
- **操作系统**: Windows 11（开发环境）
- **生产环境**: CentOS 8 Stream
- **版本控制**: Git
- **API文档**: 基于OpenAPI规范

---

## 📝 文档维护说明

本文档是AI视频创作工具系统的核心架构规范，所有开发工作都应严格遵循本文档的规定。如需修改架构设计，必须先更新本文档并经过团队评审。

**最后更新**: 2025-08-03
**文档版本**: v4.0 - 开发文档使用指南完整版
**维护人员**: 开发团队

### **v4.0 更新内容**
- ✅ **开发文档使用指南**: 完整的分类文档应用场景明确定义
- ✅ **六大核心文档体系**: 明确的文档分类和职责边界
- ✅ **文档选择决策流程**: 快速决策树和使用优先级规则
- ✅ **环境切换机制规范**: AI服务和第三方服务的环境切换指导
- ✅ **多文档协作机制**: 文档组合使用的具体规则和最佳实践
- ✅ **架构合规性检查**: 完整的开发约束和注意事项
- ✅ **持续更新机制**: 文档维护和同步的规范流程

### **v3.0 更新内容**
- ✅ **完整项目架构图**: 包含环境切换服务客户端层的完整架构
- ✅ **业务流程图迁移**: 所有系统架构图和业务流程图已迁移到 **[dev-chart-guidelines.mdc](.cursor/rules/dev-chart-guidelines.mdc)**
- ✅ **环境切换机制**: AiServiceClient 和 ThirdPartyServiceClient 实现
- ✅ **性能指标**: 完整的系统性能要求和AI生成超时配置
- ✅ **AI模型配置**: 5个AI平台的业务模型配置矩阵
- ✅ **作品发布规则**: 完整的作品发布流程和安全规则
- ✅ **架构边界规范**: 资源下载铁律和WebSocket使用边界
