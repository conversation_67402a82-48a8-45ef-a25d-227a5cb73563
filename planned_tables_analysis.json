{"extraction_time": "2025-08-04 21:36:32", "source_documents": ["index-new.mdc", "dev-api-guidelines-add.mdc"], "planned_tables": {"p_users": {"description": "用户表（用户信息、认证、偏好设置）", "source": "index-new.mdc", "category": "基础业务表", "fields": {"id": "ID", "username": "用户名", "email": "邮箱", "password": "密码", "available_points": "可用积分", "frozen_points": "冻结积分", "created_at": "创建时间", "updated_at": "更新时间"}}, "p_points_transactions": {"description": "积分交易表（积分流水、冻结、返还）", "source": "index-new.mdc", "category": "基础业务表", "fields": {"id": "ID", "user_id": "用户ID", "amount": "积分数量", "type": "交易类型", "business_type": "业务类型", "business_id": "业务ID", "description": "描述", "created_at": "创建时间"}}, "p_points_freeze": {"description": "积分冻结表（冻结机制、安全保障）", "source": "index-new.mdc", "category": "基础业务表", "fields": {"id": "ID", "user_id": "用户ID", "amount": "冻结积分数量", "business_type": "业务类型", "business_id": "业务ID", "status": "状态（frozen/consumed/returned）", "consumed_at": "消费时间", "returned_at": "返还时间", "consume_reason": "消费原因", "return_reason": "返还原因", "created_at": "创建时间", "updated_at": "更新时间"}}, "p_memberships": {"description": "会员表（会员等级、权限、使用记录）", "source": "index-new.mdc", "category": "基础业务表", "fields": {"id": "ID", "user_id": "用户ID", "membership_type": "会员类型", "expires_at": "过期时间", "created_at": "创建时间", "updated_at": "更新时间"}}, "p_music_library": {"description": "音乐库表（AI生成音乐存储、MiniMax平台）", "source": "index-new.mdc", "category": "AI生成相关表", "fields": {"id": "ID", "title": "音乐标题", "description": "描述", "file_path": "文件路径", "platform": "生成平台", "created_at": "创建时间", "updated_at": "更新时间"}}, "p_sound_library": {"description": "音效库表（AI生成音效存储、火山引擎豆包平台）", "source": "index-new.mdc", "category": "AI生成相关表", "fields": {"id": "ID", "title": "音效标题", "description": "描述", "file_path": "文件路径", "platform": "生成平台", "created_at": "创建时间", "updated_at": "更新时间"}}, "p_timbre_library": {"description": "音色库表（AI生成音色存储、双平台支持）", "source": "index-new.mdc", "category": "AI生成相关表", "fields": {"id": "ID", "name": "音色名称", "description": "描述", "config": "配置参数", "platform": "支持平台", "created_at": "创建时间", "updated_at": "更新时间"}}, "p_style_library": {"description": "风格库表（剧情风格管理、AI生成配置）", "source": "index-new.mdc", "category": "AI生成相关表", "fields": {"id": "ID", "name": "风格名称", "description": "描述", "config": "配置参数", "created_at": "创建时间", "updated_at": "更新时间"}}, "p_story_library": {"description": "故事库表（AI生成故事内容、项目表 p_projects 关联）", "source": "index-new.mdc", "category": "AI生成相关表", "fields": {"id": "ID", "title": "故事标题", "content": "故事内容", "project_id": "关联项目ID", "created_at": "创建时间", "updated_at": "更新时间"}}, "p_character_library": {"description": "角色库表（AI生成角色信息、特征描述）", "source": "index-new.mdc", "category": "AI生成相关表", "fields": {"id": "ID", "name": "角色名称", "description": "角色描述", "characteristics": "特征描述", "created_at": "创建时间", "updated_at": "更新时间"}}, "p_resources": {"description": "AI生成资源表（资源管理、模块关联、状态跟踪）", "source": "index-new.mdc", "category": "核心资源管理表", "fields": {"id": "ID", "user_id": "用户ID", "resource_type": "资源类型", "title": "资源标题", "status": "状态", "file_path": "文件路径", "created_at": "创建时间", "updated_at": "更新时间"}}, "p_resource_versions": {"description": "资源版本表（版本控制、提示词管理、本地导出）", "source": "index-new.mdc", "category": "核心资源管理表", "fields": {"id": "ID", "resource_id": "资源ID", "version_name": "版本名称", "description": "版本描述", "generation_config": "生成配置", "created_at": "创建时间", "updated_at": "更新时间"}}, "p_ai_generation_tasks": {"description": "AI生成任务表（任务状态、进度、结果）", "source": "index-new.mdc", "category": "任务管理表", "fields": {"id": "ID", "user_id": "用户ID", "task_type": "任务类型", "status": "任务状态", "progress": "进度", "input_data": "输入数据", "result_data": "结果数据", "error_message": "错误信息", "created_at": "创建时间", "updated_at": "更新时间"}}, "p_websocket_sessions": {"description": "WebSocket会话表（连接管理、状态同步）", "source": "index-new.mdc", "category": "任务管理表", "fields": {"id": "ID", "user_id": "用户ID", "session_id": "会话ID", "client_type": "客户端类型", "status": "连接状态", "created_at": "创建时间", "updated_at": "更新时间"}}, "user_growth_paths": {"description": "用户成长路径跟踪系统", "source": "dev-api-guidelines-add.mdc", "category": "用户成长系统", "fields": {"id": "ID", "user_id": "用户ID", "milestone_type": "里程碑类型（first_story, first_video等）", "milestone_data": "里程碑数据（JSON）", "achieved_at": "达成时间", "created_at": "创建时间", "updated_at": "更新时间"}}, "user_recommendations": {"description": "个性化推荐系统", "source": "dev-api-guidelines-add.mdc", "category": "推荐系统", "fields": {"id": "ID", "user_id": "用户ID", "recommendation_type": "推荐类型（voice, style, character）", "recommendation_data": "推荐数据（JSON）", "confidence_score": "置信度分数", "is_clicked": "是否被点击", "created_at": "创建时间", "updated_at": "更新时间"}}, "referral_codes": {"description": "邀请码表", "source": "dev-api-guidelines-add.mdc", "category": "邀请佣金系统", "fields": {"id": "ID", "user_id": "用户ID", "code": "邀请码", "usage_count": "使用次数", "max_usage": "最大使用次数", "commission_rate": "佣金比例", "is_active": "是否激活", "created_at": "创建时间", "updated_at": "更新时间"}}, "referral_commissions": {"description": "邀请佣金表", "source": "dev-api-guidelines-add.mdc", "category": "邀请佣金系统", "fields": {"id": "ID", "referrer_id": "邀请人ID", "referred_id": "被邀请人ID", "referral_code": "邀请码", "commission_amount": "佣金金额", "status": "状态（pending, paid, cancelled）", "earned_at": "获得时间", "paid_at": "支付时间", "created_at": "创建时间", "updated_at": "更新时间"}}, "download_tracking": {"description": "终端下载推广系统", "source": "dev-api-guidelines-add.mdc", "category": "下载推广系统", "fields": {"id": "ID", "download_id": "下载ID", "platform": "平台（windows, macos, linux）", "version": "版本", "ip_address": "IP地址", "user_agent": "用户代理", "referrer": "来源", "user_id": "用户ID（可选）", "downloaded_at": "下载时间", "created_at": "创建时间", "updated_at": "更新时间"}}, "ai_model_configs": {"description": "AI模型管理系统", "source": "dev-api-guidelines-add.mdc", "category": "AI模型管理", "fields": {"id": "ID", "model_name": "模型名称", "provider": "提供商（deepseek, liblib, kling, minimax, volcengine）", "model_type": "模型类型（text, image, video, voice）", "config_params": "配置参数（JSON）", "is_active": "是否激活", "priority": "优先级", "cost_per_request": "每次请求成本", "created_at": "创建时间", "updated_at": "更新时间"}}, "cache_statistics": {"description": "缓存管理和性能优化系统", "source": "dev-api-guidelines-add.mdc", "category": "缓存管理", "fields": {"id": "ID", "cache_key": "缓存键", "cache_type": "缓存类型（redis, memory, database）", "hit_count": "命中次数", "miss_count": "未命中次数", "hit_rate": "命中率", "last_accessed": "最后访问时间", "created_at": "创建时间", "updated_at": "更新时间"}}, "workflow_templates": {"description": "工作流模板表", "source": "dev-api-guidelines-add.mdc", "category": "工作流管理系统", "fields": {"id": "ID", "template_name": "模板名称", "template_description": "模板描述", "workflow_steps": "工作流步骤（JSON）", "default_params": "默认参数（JSON）", "is_public": "是否公开", "created_by": "创建者ID", "usage_count": "使用次数", "created_at": "创建时间", "updated_at": "更新时间"}}, "workflow_executions": {"description": "工作流执行表", "source": "dev-api-guidelines-add.mdc", "category": "工作流管理系统", "fields": {"id": "ID", "template_id": "模板ID", "user_id": "用户ID", "execution_params": "执行参数（JSON）", "status": "状态（pending, running, completed, failed）", "execution_log": "执行日志（JSON）", "started_at": "开始时间", "completed_at": "完成时间", "created_at": "创建时间", "updated_at": "更新时间"}}}, "summary": {"total_tables": 23, "categories": {"0": "基础业务表", "4": "AI生成相关表", "10": "核心资源管理表", "12": "任务管理表", "14": "用户成长系统", "15": "推荐系统", "16": "邀请佣金系统", "18": "下载推广系统", "19": "AI模型管理", "20": "缓存管理", "21": "工作流管理系统"}}}